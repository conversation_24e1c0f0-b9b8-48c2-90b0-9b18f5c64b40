var nh=Object.defineProperty,rh=Object.defineProperties;var oh=Object.getOwnPropertyDescriptors;var ol=Object.getOwnPropertySymbols;var ih=Object.prototype.hasOwnProperty,sh=Object.prototype.propertyIsEnumerable;var il=(e,t,n)=>t in e?nh(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,E=(e,t)=>{for(var n in t||={})ih.call(t,n)&&il(e,n,t[n]);if(ol)for(var n of ol(t))sh.call(t,n)&&il(e,n,t[n]);return e},z=(e,t)=>rh(e,oh(t));var rs;function Zr(){return rs}function Oe(e){let t=rs;return rs=e,t}var sl=Symbol("NotFound");function $t(e){return e===sl||e?.name==="\u0275NotFound"}function al(e,t){return Object.is(e,t)}var W=null,Qr=!1,is=1,ah=null,$e=Symbol("SIGNAL");function j(e){let t=W;return W=e,t}function Yr(){return W}var Kr={version:0,lastCleanEpoch:0,dirty:!1,producerNode:void 0,producerLastReadVersion:void 0,producerIndexOfThis:void 0,nextProducerIndex:0,liveConsumerNode:void 0,liveConsumerIndexOfThis:void 0,consumerAllowSignalWrites:!1,consumerIsAlwaysLive:!1,kind:"unknown",producerMustRecompute:()=>!1,producerRecomputeValue:()=>{},consumerMarkedDirty:()=>{},consumerOnSignalRead:()=>{}};function Jr(e){if(Qr)throw new Error("");if(W===null)return;W.consumerOnSignalRead(e);let t=W.nextProducerIndex++;if(no(W),t<W.producerNode.length&&W.producerNode[t]!==e&&Rn(W)){let n=W.producerNode[t];to(n,W.producerIndexOfThis[t])}W.producerNode[t]!==e&&(W.producerNode[t]=e,W.producerIndexOfThis[t]=Rn(W)?ul(e,W,t):0),W.producerLastReadVersion[t]=e.version}function cl(){is++}function ll(e){if(!(Rn(e)&&!e.dirty)&&!(!e.dirty&&e.lastCleanEpoch===is)){if(!e.producerMustRecompute(e)&&!eo(e)){os(e);return}e.producerRecomputeValue(e),os(e)}}function ss(e){if(e.liveConsumerNode===void 0)return;let t=Qr;Qr=!0;try{for(let n of e.liveConsumerNode)n.dirty||ch(n)}finally{Qr=t}}function as(){return W?.consumerAllowSignalWrites!==!1}function ch(e){e.dirty=!0,ss(e),e.consumerMarkedDirty?.(e)}function os(e){e.dirty=!1,e.lastCleanEpoch=is}function Xr(e){return e&&(e.nextProducerIndex=0),j(e)}function cs(e,t){if(j(t),!(!e||e.producerNode===void 0||e.producerIndexOfThis===void 0||e.producerLastReadVersion===void 0)){if(Rn(e))for(let n=e.nextProducerIndex;n<e.producerNode.length;n++)to(e.producerNode[n],e.producerIndexOfThis[n]);for(;e.producerNode.length>e.nextProducerIndex;)e.producerNode.pop(),e.producerLastReadVersion.pop(),e.producerIndexOfThis.pop()}}function eo(e){no(e);for(let t=0;t<e.producerNode.length;t++){let n=e.producerNode[t],r=e.producerLastReadVersion[t];if(r!==n.version||(ll(n),r!==n.version))return!0}return!1}function ls(e){if(no(e),Rn(e))for(let t=0;t<e.producerNode.length;t++)to(e.producerNode[t],e.producerIndexOfThis[t]);e.producerNode.length=e.producerLastReadVersion.length=e.producerIndexOfThis.length=0,e.liveConsumerNode&&(e.liveConsumerNode.length=e.liveConsumerIndexOfThis.length=0)}function ul(e,t,n){if(dl(e),e.liveConsumerNode.length===0&&fl(e))for(let r=0;r<e.producerNode.length;r++)e.producerIndexOfThis[r]=ul(e.producerNode[r],e,r);return e.liveConsumerIndexOfThis.push(n),e.liveConsumerNode.push(t)-1}function to(e,t){if(dl(e),e.liveConsumerNode.length===1&&fl(e))for(let r=0;r<e.producerNode.length;r++)to(e.producerNode[r],e.producerIndexOfThis[r]);let n=e.liveConsumerNode.length-1;if(e.liveConsumerNode[t]=e.liveConsumerNode[n],e.liveConsumerIndexOfThis[t]=e.liveConsumerIndexOfThis[n],e.liveConsumerNode.length--,e.liveConsumerIndexOfThis.length--,t<e.liveConsumerNode.length){let r=e.liveConsumerIndexOfThis[t],o=e.liveConsumerNode[t];no(o),o.producerIndexOfThis[r]=t}}function Rn(e){return e.consumerIsAlwaysLive||(e?.liveConsumerNode?.length??0)>0}function no(e){e.producerNode??=[],e.producerIndexOfThis??=[],e.producerLastReadVersion??=[]}function dl(e){e.liveConsumerNode??=[],e.liveConsumerIndexOfThis??=[]}function fl(e){return e.producerNode!==void 0}function pl(e){ah?.(e)}function lh(){throw new Error}var hl=lh;function gl(e){hl(e)}function us(e){hl=e}var uh=null;function ds(e,t){let n=Object.create(ro);n.value=e,t!==void 0&&(n.equal=t);let r=()=>ml(n);return r[$e]=n,pl(n),[r,s=>An(n,s),s=>vl(n,s)]}function ml(e){return Jr(e),e.value}function An(e,t){as()||gl(e),e.equal(e.value,t)||(e.value=t,dh(e))}function vl(e,t){as()||gl(e),An(e,t(e.value))}var ro=z(E({},Kr),{equal:al,value:void 0,kind:"signal"});function dh(e){e.version++,cl(),ss(e),uh?.(e)}function D(e){return typeof e=="function"}function Bt(e){let n=e(r=>{Error.call(r),r.stack=new Error().stack});return n.prototype=Object.create(Error.prototype),n.prototype.constructor=n,n}var oo=Bt(e=>function(n){e(this),this.message=n?`${n.length} errors occurred during unsubscription:
${n.map((r,o)=>`${o+1}) ${r.toString()}`).join(`
  `)}`:"",this.name="UnsubscriptionError",this.errors=n});function On(e,t){if(e){let n=e.indexOf(t);0<=n&&e.splice(n,1)}}var G=class e{constructor(t){this.initialTeardown=t,this.closed=!1,this._parentage=null,this._finalizers=null}unsubscribe(){let t;if(!this.closed){this.closed=!0;let{_parentage:n}=this;if(n)if(this._parentage=null,Array.isArray(n))for(let i of n)i.remove(this);else n.remove(this);let{initialTeardown:r}=this;if(D(r))try{r()}catch(i){t=i instanceof oo?i.errors:[i]}let{_finalizers:o}=this;if(o){this._finalizers=null;for(let i of o)try{yl(i)}catch(s){t=t??[],s instanceof oo?t=[...t,...s.errors]:t.push(s)}}if(t)throw new oo(t)}}add(t){var n;if(t&&t!==this)if(this.closed)yl(t);else{if(t instanceof e){if(t.closed||t._hasParent(this))return;t._addParent(this)}(this._finalizers=(n=this._finalizers)!==null&&n!==void 0?n:[]).push(t)}}_hasParent(t){let{_parentage:n}=this;return n===t||Array.isArray(n)&&n.includes(t)}_addParent(t){let{_parentage:n}=this;this._parentage=Array.isArray(n)?(n.push(t),n):n?[n,t]:t}_removeParent(t){let{_parentage:n}=this;n===t?this._parentage=null:Array.isArray(n)&&On(n,t)}remove(t){let{_finalizers:n}=this;n&&On(n,t),t instanceof e&&t._removeParent(this)}};G.EMPTY=(()=>{let e=new G;return e.closed=!0,e})();var fs=G.EMPTY;function io(e){return e instanceof G||e&&"closed"in e&&D(e.remove)&&D(e.add)&&D(e.unsubscribe)}function yl(e){D(e)?e():e.unsubscribe()}var De={onUnhandledError:null,onStoppedNotification:null,Promise:void 0,useDeprecatedSynchronousErrorHandling:!1,useDeprecatedNextContext:!1};var zt={setTimeout(e,t,...n){let{delegate:r}=zt;return r?.setTimeout?r.setTimeout(e,t,...n):setTimeout(e,t,...n)},clearTimeout(e){let{delegate:t}=zt;return(t?.clearTimeout||clearTimeout)(e)},delegate:void 0};function so(e){zt.setTimeout(()=>{let{onUnhandledError:t}=De;if(t)t(e);else throw e})}function kn(){}var El=ps("C",void 0,void 0);function Il(e){return ps("E",void 0,e)}function wl(e){return ps("N",e,void 0)}function ps(e,t,n){return{kind:e,value:t,error:n}}var mt=null;function Gt(e){if(De.useDeprecatedSynchronousErrorHandling){let t=!mt;if(t&&(mt={errorThrown:!1,error:null}),e(),t){let{errorThrown:n,error:r}=mt;if(mt=null,n)throw r}}else e()}function Sl(e){De.useDeprecatedSynchronousErrorHandling&&mt&&(mt.errorThrown=!0,mt.error=e)}var vt=class extends G{constructor(t){super(),this.isStopped=!1,t?(this.destination=t,io(t)&&t.add(this)):this.destination=hh}static create(t,n,r){return new qt(t,n,r)}next(t){this.isStopped?gs(wl(t),this):this._next(t)}error(t){this.isStopped?gs(Il(t),this):(this.isStopped=!0,this._error(t))}complete(){this.isStopped?gs(El,this):(this.isStopped=!0,this._complete())}unsubscribe(){this.closed||(this.isStopped=!0,super.unsubscribe(),this.destination=null)}_next(t){this.destination.next(t)}_error(t){try{this.destination.error(t)}finally{this.unsubscribe()}}_complete(){try{this.destination.complete()}finally{this.unsubscribe()}}},fh=Function.prototype.bind;function hs(e,t){return fh.call(e,t)}var ms=class{constructor(t){this.partialObserver=t}next(t){let{partialObserver:n}=this;if(n.next)try{n.next(t)}catch(r){ao(r)}}error(t){let{partialObserver:n}=this;if(n.error)try{n.error(t)}catch(r){ao(r)}else ao(t)}complete(){let{partialObserver:t}=this;if(t.complete)try{t.complete()}catch(n){ao(n)}}},qt=class extends vt{constructor(t,n,r){super();let o;if(D(t)||!t)o={next:t??void 0,error:n??void 0,complete:r??void 0};else{let i;this&&De.useDeprecatedNextContext?(i=Object.create(t),i.unsubscribe=()=>this.unsubscribe(),o={next:t.next&&hs(t.next,i),error:t.error&&hs(t.error,i),complete:t.complete&&hs(t.complete,i)}):o=t}this.destination=new ms(o)}};function ao(e){De.useDeprecatedSynchronousErrorHandling?Sl(e):so(e)}function ph(e){throw e}function gs(e,t){let{onStoppedNotification:n}=De;n&&zt.setTimeout(()=>n(e,t))}var hh={closed:!0,next:kn,error:ph,complete:kn};var Wt=typeof Symbol=="function"&&Symbol.observable||"@@observable";function pe(e){return e}function vs(...e){return ys(e)}function ys(e){return e.length===0?pe:e.length===1?e[0]:function(n){return e.reduce((r,o)=>o(r),n)}}var k=(()=>{class e{constructor(n){n&&(this._subscribe=n)}lift(n){let r=new e;return r.source=this,r.operator=n,r}subscribe(n,r,o){let i=mh(n)?n:new qt(n,r,o);return Gt(()=>{let{operator:s,source:a}=this;i.add(s?s.call(i,a):a?this._subscribe(i):this._trySubscribe(i))}),i}_trySubscribe(n){try{return this._subscribe(n)}catch(r){n.error(r)}}forEach(n,r){return r=bl(r),new r((o,i)=>{let s=new qt({next:a=>{try{n(a)}catch(c){i(c),s.unsubscribe()}},error:i,complete:o});this.subscribe(s)})}_subscribe(n){var r;return(r=this.source)===null||r===void 0?void 0:r.subscribe(n)}[Wt](){return this}pipe(...n){return ys(n)(this)}toPromise(n){return n=bl(n),new n((r,o)=>{let i;this.subscribe(s=>i=s,s=>o(s),()=>r(i))})}}return e.create=t=>new e(t),e})();function bl(e){var t;return(t=e??De.Promise)!==null&&t!==void 0?t:Promise}function gh(e){return e&&D(e.next)&&D(e.error)&&D(e.complete)}function mh(e){return e&&e instanceof vt||gh(e)&&io(e)}function Es(e){return D(e?.lift)}function x(e){return t=>{if(Es(t))return t.lift(function(n){try{return e(n,this)}catch(r){this.error(r)}});throw new TypeError("Unable to lift unknown Observable type")}}function R(e,t,n,r,o){return new Is(e,t,n,r,o)}var Is=class extends vt{constructor(t,n,r,o,i,s){super(t),this.onFinalize=i,this.shouldUnsubscribe=s,this._next=n?function(a){try{n(a)}catch(c){t.error(c)}}:super._next,this._error=o?function(a){try{o(a)}catch(c){t.error(c)}finally{this.unsubscribe()}}:super._error,this._complete=r?function(){try{r()}catch(a){t.error(a)}finally{this.unsubscribe()}}:super._complete}unsubscribe(){var t;if(!this.shouldUnsubscribe||this.shouldUnsubscribe()){let{closed:n}=this;super.unsubscribe(),!n&&((t=this.onFinalize)===null||t===void 0||t.call(this))}}};function Zt(){return x((e,t)=>{let n=null;e._refCount++;let r=R(t,void 0,void 0,void 0,()=>{if(!e||e._refCount<=0||0<--e._refCount){n=null;return}let o=e._connection,i=n;n=null,o&&(!i||o===i)&&o.unsubscribe(),t.unsubscribe()});e.subscribe(r),r.closed||(n=e.connect())})}var Qt=class extends k{constructor(t,n){super(),this.source=t,this.subjectFactory=n,this._subject=null,this._refCount=0,this._connection=null,Es(t)&&(this.lift=t.lift)}_subscribe(t){return this.getSubject().subscribe(t)}getSubject(){let t=this._subject;return(!t||t.isStopped)&&(this._subject=this.subjectFactory()),this._subject}_teardown(){this._refCount=0;let{_connection:t}=this;this._subject=this._connection=null,t?.unsubscribe()}connect(){let t=this._connection;if(!t){t=this._connection=new G;let n=this.getSubject();t.add(this.source.subscribe(R(n,void 0,()=>{this._teardown(),n.complete()},r=>{this._teardown(),n.error(r)},()=>this._teardown()))),t.closed&&(this._connection=null,t=G.EMPTY)}return t}refCount(){return Zt()(this)}};var Cl=Bt(e=>function(){e(this),this.name="ObjectUnsubscribedError",this.message="object unsubscribed"});var ee=(()=>{class e extends k{constructor(){super(),this.closed=!1,this.currentObservers=null,this.observers=[],this.isStopped=!1,this.hasError=!1,this.thrownError=null}lift(n){let r=new co(this,this);return r.operator=n,r}_throwIfClosed(){if(this.closed)throw new Cl}next(n){Gt(()=>{if(this._throwIfClosed(),!this.isStopped){this.currentObservers||(this.currentObservers=Array.from(this.observers));for(let r of this.currentObservers)r.next(n)}})}error(n){Gt(()=>{if(this._throwIfClosed(),!this.isStopped){this.hasError=this.isStopped=!0,this.thrownError=n;let{observers:r}=this;for(;r.length;)r.shift().error(n)}})}complete(){Gt(()=>{if(this._throwIfClosed(),!this.isStopped){this.isStopped=!0;let{observers:n}=this;for(;n.length;)n.shift().complete()}})}unsubscribe(){this.isStopped=this.closed=!0,this.observers=this.currentObservers=null}get observed(){var n;return((n=this.observers)===null||n===void 0?void 0:n.length)>0}_trySubscribe(n){return this._throwIfClosed(),super._trySubscribe(n)}_subscribe(n){return this._throwIfClosed(),this._checkFinalizedStatuses(n),this._innerSubscribe(n)}_innerSubscribe(n){let{hasError:r,isStopped:o,observers:i}=this;return r||o?fs:(this.currentObservers=null,i.push(n),new G(()=>{this.currentObservers=null,On(i,n)}))}_checkFinalizedStatuses(n){let{hasError:r,thrownError:o,isStopped:i}=this;r?n.error(o):i&&n.complete()}asObservable(){let n=new k;return n.source=this,n}}return e.create=(t,n)=>new co(t,n),e})(),co=class extends ee{constructor(t,n){super(),this.destination=t,this.source=n}next(t){var n,r;(r=(n=this.destination)===null||n===void 0?void 0:n.next)===null||r===void 0||r.call(n,t)}error(t){var n,r;(r=(n=this.destination)===null||n===void 0?void 0:n.error)===null||r===void 0||r.call(n,t)}complete(){var t,n;(n=(t=this.destination)===null||t===void 0?void 0:t.complete)===null||n===void 0||n.call(t)}_subscribe(t){var n,r;return(r=(n=this.source)===null||n===void 0?void 0:n.subscribe(t))!==null&&r!==void 0?r:fs}};var te=class extends ee{constructor(t){super(),this._value=t}get value(){return this.getValue()}_subscribe(t){let n=super._subscribe(t);return!n.closed&&t.next(this._value),n}getValue(){let{hasError:t,thrownError:n,_value:r}=this;if(t)throw n;return this._throwIfClosed(),r}next(t){super.next(this._value=t)}};var ce=new k(e=>e.complete());function Dl(e){return e&&D(e.schedule)}function Tl(e){return e[e.length-1]}function Ml(e){return D(Tl(e))?e.pop():void 0}function rt(e){return Dl(Tl(e))?e.pop():void 0}function Nl(e,t,n,r){function o(i){return i instanceof n?i:new n(function(s){s(i)})}return new(n||(n=Promise))(function(i,s){function a(f){try{l(r.next(f))}catch(p){s(p)}}function c(f){try{l(r.throw(f))}catch(p){s(p)}}function l(f){f.done?i(f.value):o(f.value).then(a,c)}l((r=r.apply(e,t||[])).next())})}function _l(e){var t=typeof Symbol=="function"&&Symbol.iterator,n=t&&e[t],r=0;if(n)return n.call(e);if(e&&typeof e.length=="number")return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}function yt(e){return this instanceof yt?(this.v=e,this):new yt(e)}function xl(e,t,n){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var r=n.apply(e,t||[]),o,i=[];return o=Object.create((typeof AsyncIterator=="function"?AsyncIterator:Object).prototype),a("next"),a("throw"),a("return",s),o[Symbol.asyncIterator]=function(){return this},o;function s(h){return function(S){return Promise.resolve(S).then(h,p)}}function a(h,S){r[h]&&(o[h]=function(O){return new Promise(function($,U){i.push([h,O,$,U])>1||c(h,O)})},S&&(o[h]=S(o[h])))}function c(h,S){try{l(r[h](S))}catch(O){y(i[0][3],O)}}function l(h){h.value instanceof yt?Promise.resolve(h.value.v).then(f,p):y(i[0][2],h)}function f(h){c("next",h)}function p(h){c("throw",h)}function y(h,S){h(S),i.shift(),i.length&&c(i[0][0],i[0][1])}}function Rl(e){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var t=e[Symbol.asyncIterator],n;return t?t.call(e):(e=typeof _l=="function"?_l(e):e[Symbol.iterator](),n={},r("next"),r("throw"),r("return"),n[Symbol.asyncIterator]=function(){return this},n);function r(i){n[i]=e[i]&&function(s){return new Promise(function(a,c){s=e[i](s),o(a,c,s.done,s.value)})}}function o(i,s,a,c){Promise.resolve(c).then(function(l){i({value:l,done:a})},s)}}var lo=e=>e&&typeof e.length=="number"&&typeof e!="function";function uo(e){return D(e?.then)}function fo(e){return D(e[Wt])}function po(e){return Symbol.asyncIterator&&D(e?.[Symbol.asyncIterator])}function ho(e){return new TypeError(`You provided ${e!==null&&typeof e=="object"?"an invalid object":`'${e}'`} where a stream was expected. You can provide an Observable, Promise, ReadableStream, Array, AsyncIterable, or Iterable.`)}function vh(){return typeof Symbol!="function"||!Symbol.iterator?"@@iterator":Symbol.iterator}var go=vh();function mo(e){return D(e?.[go])}function vo(e){return xl(this,arguments,function*(){let n=e.getReader();try{for(;;){let{value:r,done:o}=yield yt(n.read());if(o)return yield yt(void 0);yield yield yt(r)}}finally{n.releaseLock()}})}function yo(e){return D(e?.getReader)}function K(e){if(e instanceof k)return e;if(e!=null){if(fo(e))return yh(e);if(lo(e))return Eh(e);if(uo(e))return Ih(e);if(po(e))return Al(e);if(mo(e))return wh(e);if(yo(e))return Sh(e)}throw ho(e)}function yh(e){return new k(t=>{let n=e[Wt]();if(D(n.subscribe))return n.subscribe(t);throw new TypeError("Provided object does not correctly implement Symbol.observable")})}function Eh(e){return new k(t=>{for(let n=0;n<e.length&&!t.closed;n++)t.next(e[n]);t.complete()})}function Ih(e){return new k(t=>{e.then(n=>{t.closed||(t.next(n),t.complete())},n=>t.error(n)).then(null,so)})}function wh(e){return new k(t=>{for(let n of e)if(t.next(n),t.closed)return;t.complete()})}function Al(e){return new k(t=>{bh(e,t).catch(n=>t.error(n))})}function Sh(e){return Al(vo(e))}function bh(e,t){var n,r,o,i;return Nl(this,void 0,void 0,function*(){try{for(n=Rl(e);r=yield n.next(),!r.done;){let s=r.value;if(t.next(s),t.closed)return}}catch(s){o={error:s}}finally{try{r&&!r.done&&(i=n.return)&&(yield i.call(n))}finally{if(o)throw o.error}}t.complete()})}function le(e,t,n,r=0,o=!1){let i=t.schedule(function(){n(),o?e.add(this.schedule(null,r)):this.unsubscribe()},r);if(e.add(i),!o)return i}function Eo(e,t=0){return x((n,r)=>{n.subscribe(R(r,o=>le(r,e,()=>r.next(o),t),()=>le(r,e,()=>r.complete(),t),o=>le(r,e,()=>r.error(o),t)))})}function Io(e,t=0){return x((n,r)=>{r.add(e.schedule(()=>n.subscribe(r),t))})}function Ol(e,t){return K(e).pipe(Io(t),Eo(t))}function kl(e,t){return K(e).pipe(Io(t),Eo(t))}function Pl(e,t){return new k(n=>{let r=0;return t.schedule(function(){r===e.length?n.complete():(n.next(e[r++]),n.closed||this.schedule())})})}function Ll(e,t){return new k(n=>{let r;return le(n,t,()=>{r=e[go](),le(n,t,()=>{let o,i;try{({value:o,done:i}=r.next())}catch(s){n.error(s);return}i?n.complete():n.next(o)},0,!0)}),()=>D(r?.return)&&r.return()})}function wo(e,t){if(!e)throw new Error("Iterable cannot be null");return new k(n=>{le(n,t,()=>{let r=e[Symbol.asyncIterator]();le(n,t,()=>{r.next().then(o=>{o.done?n.complete():n.next(o.value)})},0,!0)})})}function Fl(e,t){return wo(vo(e),t)}function jl(e,t){if(e!=null){if(fo(e))return Ol(e,t);if(lo(e))return Pl(e,t);if(uo(e))return kl(e,t);if(po(e))return wo(e,t);if(mo(e))return Ll(e,t);if(yo(e))return Fl(e,t)}throw ho(e)}function q(e,t){return t?jl(e,t):K(e)}function C(...e){let t=rt(e);return q(e,t)}function Yt(e,t){let n=D(e)?e:()=>e,r=o=>o.error(n());return new k(t?o=>t.schedule(r,0,o):r)}function ws(e){return!!e&&(e instanceof k||D(e.lift)&&D(e.subscribe))}var Be=Bt(e=>function(){e(this),this.name="EmptyError",this.message="no elements in sequence"});function A(e,t){return x((n,r)=>{let o=0;n.subscribe(R(r,i=>{r.next(e.call(t,i,o++))}))})}var{isArray:Ch}=Array;function Dh(e,t){return Ch(t)?e(...t):e(t)}function Vl(e){return A(t=>Dh(e,t))}var{isArray:Th}=Array,{getPrototypeOf:Mh,prototype:_h,keys:Nh}=Object;function Hl(e){if(e.length===1){let t=e[0];if(Th(t))return{args:t,keys:null};if(xh(t)){let n=Nh(t);return{args:n.map(r=>t[r]),keys:n}}}return{args:e,keys:null}}function xh(e){return e&&typeof e=="object"&&Mh(e)===_h}function Ul(e,t){return e.reduce((n,r,o)=>(n[r]=t[o],n),{})}function So(...e){let t=rt(e),n=Ml(e),{args:r,keys:o}=Hl(e);if(r.length===0)return q([],t);let i=new k(Rh(r,t,o?s=>Ul(o,s):pe));return n?i.pipe(Vl(n)):i}function Rh(e,t,n=pe){return r=>{$l(t,()=>{let{length:o}=e,i=new Array(o),s=o,a=o;for(let c=0;c<o;c++)$l(t,()=>{let l=q(e[c],t),f=!1;l.subscribe(R(r,p=>{i[c]=p,f||(f=!0,a--),a||r.next(n(i.slice()))},()=>{--s||r.complete()}))},r)},r)}}function $l(e,t,n){e?le(n,e,t):t()}function Bl(e,t,n,r,o,i,s,a){let c=[],l=0,f=0,p=!1,y=()=>{p&&!c.length&&!l&&t.complete()},h=O=>l<r?S(O):c.push(O),S=O=>{i&&t.next(O),l++;let $=!1;K(n(O,f++)).subscribe(R(t,U=>{o?.(U),i?h(U):t.next(U)},()=>{$=!0},void 0,()=>{if($)try{for(l--;c.length&&l<r;){let U=c.shift();s?le(t,s,()=>S(U)):S(U)}y()}catch(U){t.error(U)}}))};return e.subscribe(R(t,h,()=>{p=!0,y()})),()=>{a?.()}}function Z(e,t,n=1/0){return D(t)?Z((r,o)=>A((i,s)=>t(r,i,o,s))(K(e(r,o))),n):(typeof t=="number"&&(n=t),x((r,o)=>Bl(r,o,e,n)))}function zl(e=1/0){return Z(pe,e)}function Gl(){return zl(1)}function Kt(...e){return Gl()(q(e,rt(e)))}function Pn(e){return new k(t=>{K(e()).subscribe(t)})}function Ee(e,t){return x((n,r)=>{let o=0;n.subscribe(R(r,i=>e.call(t,i,o++)&&r.next(i)))})}function ot(e){return x((t,n)=>{let r=null,o=!1,i;r=t.subscribe(R(n,void 0,void 0,s=>{i=K(e(s,ot(e)(t))),r?(r.unsubscribe(),r=null,i.subscribe(n)):o=!0})),o&&(r.unsubscribe(),r=null,i.subscribe(n))})}function ql(e,t,n,r,o){return(i,s)=>{let a=n,c=t,l=0;i.subscribe(R(s,f=>{let p=l++;c=a?e(c,f,p):(a=!0,f),r&&s.next(c)},o&&(()=>{a&&s.next(c),s.complete()})))}}function Jt(e,t){return D(t)?Z(e,t,1):Z(e,1)}function it(e){return x((t,n)=>{let r=!1;t.subscribe(R(n,o=>{r=!0,n.next(o)},()=>{r||n.next(e),n.complete()}))})}function ze(e){return e<=0?()=>ce:x((t,n)=>{let r=0;t.subscribe(R(n,o=>{++r<=e&&(n.next(o),e<=r&&n.complete())}))})}function bo(e=Ah){return x((t,n)=>{let r=!1;t.subscribe(R(n,o=>{r=!0,n.next(o)},()=>r?n.complete():n.error(e())))})}function Ah(){return new Be}function Ln(e){return x((t,n)=>{try{t.subscribe(n)}finally{n.add(e)}})}function Ge(e,t){let n=arguments.length>=2;return r=>r.pipe(e?Ee((o,i)=>e(o,i,r)):pe,ze(1),n?it(t):bo(()=>new Be))}function Xt(e){return e<=0?()=>ce:x((t,n)=>{let r=[];t.subscribe(R(n,o=>{r.push(o),e<r.length&&r.shift()},()=>{for(let o of r)n.next(o);n.complete()},void 0,()=>{r=null}))})}function Ss(e,t){let n=arguments.length>=2;return r=>r.pipe(e?Ee((o,i)=>e(o,i,r)):pe,Xt(1),n?it(t):bo(()=>new Be))}function bs(e,t){return x(ql(e,t,arguments.length>=2,!0))}function Cs(...e){let t=rt(e);return x((n,r)=>{(t?Kt(e,n,t):Kt(e,n)).subscribe(r)})}function Ie(e,t){return x((n,r)=>{let o=null,i=0,s=!1,a=()=>s&&!o&&r.complete();n.subscribe(R(r,c=>{o?.unsubscribe();let l=0,f=i++;K(e(c,f)).subscribe(o=R(r,p=>r.next(t?t(c,p,f,l++):p),()=>{o=null,a()}))},()=>{s=!0,a()}))})}function Co(e){return x((t,n)=>{K(e).subscribe(R(n,()=>n.complete(),kn)),!n.closed&&t.subscribe(n)})}function J(e,t,n){let r=D(e)||t||n?{next:e,error:t,complete:n}:e;return r?x((o,i)=>{var s;(s=r.subscribe)===null||s===void 0||s.call(r);let a=!0;o.subscribe(R(i,c=>{var l;(l=r.next)===null||l===void 0||l.call(r,c),i.next(c)},()=>{var c;a=!1,(c=r.complete)===null||c===void 0||c.call(r),i.complete()},c=>{var l;a=!1,(l=r.error)===null||l===void 0||l.call(r,c),i.error(c)},()=>{var c,l;a&&((c=r.unsubscribe)===null||c===void 0||c.call(r)),(l=r.finalize)===null||l===void 0||l.call(r)}))}):pe}var Ls="https://angular.dev/best-practices/security#preventing-cross-site-scripting-xss",I=class extends Error{code;constructor(t,n){super(Un(t,n)),this.code=t}};function kh(e){return`NG0${Math.abs(e)}`}function Un(e,t){return`${kh(e)}${t?": "+t:""}`}function H(e){for(let t in e)if(e[t]===H)return t;throw Error("")}function We(e){if(typeof e=="string")return e;if(Array.isArray(e))return`[${e.map(We).join(", ")}]`;if(e==null)return""+e;let t=e.overriddenName||e.name;if(t)return`${t}`;let n=e.toString();if(n==null)return""+n;let r=n.indexOf(`
`);return r>=0?n.slice(0,r):n}function Fs(e,t){return e?t?`${e} ${t}`:e:t||""}var Ph=H({__forward_ref__:H});function xo(e){return e.__forward_ref__=xo,e.toString=function(){return We(this())},e}function ue(e){return js(e)?e():e}function js(e){return typeof e=="function"&&e.hasOwnProperty(Ph)&&e.__forward_ref__===xo}function Ql(e,t){e==null&&Vs(t,e,null,"!=")}function Vs(e,t,n,r){throw new Error(`ASSERTION ERROR: ${e}`+(r==null?"":` [Expected=> ${n} ${r} ${t} <=Actual]`))}function b(e){return{token:e.token,providedIn:e.providedIn||null,factory:e.factory,value:void 0}}function $n(e){return Lh(e,Ro)}function Hs(e){return $n(e)!==null}function Lh(e,t){return e.hasOwnProperty(t)&&e[t]||null}function Fh(e){let t=e?.[Ro]??null;return t||null}function Ts(e){return e&&e.hasOwnProperty(To)?e[To]:null}var Ro=H({\u0275prov:H}),To=H({\u0275inj:H}),w=class{_desc;ngMetadataName="InjectionToken";\u0275prov;constructor(t,n){this._desc=t,this.\u0275prov=void 0,typeof n=="number"?this.__NG_ELEMENT_ID__=n:n!==void 0&&(this.\u0275prov=b({token:this,providedIn:n.providedIn||"root",factory:n.factory}))}get multi(){return this}toString(){return`InjectionToken ${this._desc}`}};function Us(e){return e&&!!e.\u0275providers}var $s=H({\u0275cmp:H}),Bs=H({\u0275dir:H}),zs=H({\u0275pipe:H}),Gs=H({\u0275mod:H}),Vn=H({\u0275fac:H}),Ct=H({__NG_ELEMENT_ID__:H}),Wl=H({__NG_ENV_ID__:H});function Yl(e){return typeof e=="string"?e:e==null?"":String(e)}function Mo(e){return typeof e=="function"?e.name||e.toString():typeof e=="object"&&e!=null&&typeof e.type=="function"?e.type.name||e.type.toString():Yl(e)}var qs=H({ngErrorCode:H}),Kl=H({ngErrorMessage:H}),jn=H({ngTokenPath:H});function Ws(e,t){return Jl("",-200,t)}function Ao(e,t){throw new I(-201,!1)}function jh(e,t){e[jn]??=[];let n=e[jn],r;typeof t=="object"&&"multi"in t&&t?.multi===!0?(Ql(t.provide,"Token with multi: true should have a provide property"),r=Mo(t.provide)):r=Mo(t),n[0]!==r&&e[jn].unshift(r)}function Vh(e,t){let n=e[jn],r=e[qs],o=e[Kl]||e.message;return e.message=Uh(o,r,n,t),e}function Jl(e,t,n){let r=new I(t,e);return r[qs]=t,r[Kl]=e,n&&(r[jn]=n),r}function Hh(e){return e[qs]}function Uh(e,t,n=[],r=null){let o="";n&&n.length>1&&(o=` Path: ${n.join(" -> ")}.`);let i=r?` Source: ${r}.`:"";return Un(t,`${e}${i}${o}`)}var Ms;function Xl(){return Ms}function he(e){let t=Ms;return Ms=e,t}function Zs(e,t,n){let r=$n(e);if(r&&r.providedIn=="root")return r.value===void 0?r.value=r.factory():r.value;if(n&8)return null;if(t!==void 0)return t;Ao(e,"Injector")}var $h={},Et=$h,Bh="__NG_DI_FLAG__",_s=class{injector;constructor(t){this.injector=t}retrieve(t,n){let r=It(n)||0;try{return this.injector.get(t,r&8?null:Et,r)}catch(o){if($t(o))return o;throw o}}};function zh(e,t=0){let n=Zr();if(n===void 0)throw new I(-203,!1);if(n===null)return Zs(e,void 0,t);{let r=Gh(t),o=n.retrieve(e,r);if($t(o)){if(r.optional)return null;throw o}return o}}function M(e,t=0){return(Xl()||zh)(ue(e),t)}function v(e,t){return M(e,It(t))}function It(e){return typeof e>"u"||typeof e=="number"?e:0|(e.optional&&8)|(e.host&&1)|(e.self&&2)|(e.skipSelf&&4)}function Gh(e){return{optional:!!(e&8),host:!!(e&1),self:!!(e&2),skipSelf:!!(e&4)}}function Ns(e){let t=[];for(let n=0;n<e.length;n++){let r=ue(e[n]);if(Array.isArray(r)){if(r.length===0)throw new I(900,!1);let o,i=0;for(let s=0;s<r.length;s++){let a=r[s],c=qh(a);typeof c=="number"?c===-1?o=a.token:i|=c:o=a}t.push(M(o,i))}else t.push(M(r))}return t}function qh(e){return e[Bh]}function wt(e,t){let n=e.hasOwnProperty(Vn);return n?e[Vn]:null}function Oo(e,t){e.forEach(n=>Array.isArray(n)?Oo(n,t):t(n))}function Qs(e,t,n){t>=e.length?e.push(n):e.splice(t,0,n)}function Bn(e,t){return t>=e.length-1?e.pop():e.splice(t,1)[0]}function eu(e,t,n,r){let o=e.length;if(o==t)e.push(n,r);else if(o===1)e.push(r,e[0]),e[0]=n;else{for(o--,e.push(e[o-1],e[o]);o>t;){let i=o-2;e[o]=e[i],o--}e[t]=n,e[t+1]=r}}function tu(e,t,n){let r=tn(e,t);return r>=0?e[r|1]=n:(r=~r,eu(e,r,t,n)),r}function ko(e,t){let n=tn(e,t);if(n>=0)return e[n|1]}function tn(e,t){return Wh(e,t,1)}function Wh(e,t,n){let r=0,o=e.length>>n;for(;o!==r;){let i=r+(o-r>>1),s=e[i<<n];if(t===s)return i<<n;s>t?o=i:r=i+1}return~(o<<n)}var Dt={},st=[],Ze=new w(""),Ys=new w("",-1),Ks=new w(""),Hn=class{get(t,n=Et){if(n===Et){let o=Jl("",-201);throw o.name="\u0275NotFound",o}return n}};function Js(e){return e[Gs]||null}function ct(e){return e[$s]||null}function Xs(e){return e[Bs]||null}function nu(e){return e[zs]||null}function Tt(e){return{\u0275providers:e}}function ru(e){return Tt([{provide:Ze,multi:!0,useValue:e}])}function ou(...e){return{\u0275providers:ea(!0,e),\u0275fromNgModule:!0}}function ea(e,...t){let n=[],r=new Set,o,i=s=>{n.push(s)};return Oo(t,s=>{let a=s;_o(a,i,[],r)&&(o||=[],o.push(a))}),o!==void 0&&iu(o,i),n}function iu(e,t){for(let n=0;n<e.length;n++){let{ngModule:r,providers:o}=e[n];ta(o,i=>{t(i,r)})}}function _o(e,t,n,r){if(e=ue(e),!e)return!1;let o=null,i=Ts(e),s=!i&&ct(e);if(!i&&!s){let c=e.ngModule;if(i=Ts(c),i)o=c;else return!1}else{if(s&&!s.standalone)return!1;o=e}let a=r.has(o);if(s){if(a)return!1;if(r.add(o),s.dependencies){let c=typeof s.dependencies=="function"?s.dependencies():s.dependencies;for(let l of c)_o(l,t,n,r)}}else if(i){if(i.imports!=null&&!a){r.add(o);let l;try{Oo(i.imports,f=>{_o(f,t,n,r)&&(l||=[],l.push(f))})}finally{}l!==void 0&&iu(l,t)}if(!a){let l=wt(o)||(()=>new o);t({provide:o,useFactory:l,deps:st},o),t({provide:Ks,useValue:o,multi:!0},o),t({provide:Ze,useValue:()=>M(o),multi:!0},o)}let c=i.providers;if(c!=null&&!a){let l=e;ta(c,f=>{t(f,l)})}}else return!1;return o!==e&&e.providers!==void 0}function ta(e,t){for(let n of e)Us(n)&&(n=n.\u0275providers),Array.isArray(n)?ta(n,t):t(n)}var Zh=H({provide:String,useValue:H});function su(e){return e!==null&&typeof e=="object"&&Zh in e}function Qh(e){return!!(e&&e.useExisting)}function Yh(e){return!!(e&&e.useFactory)}function No(e){return typeof e=="function"}var zn=new w(""),Do={},Zl={},Ds;function Gn(){return Ds===void 0&&(Ds=new Hn),Ds}var ne=class{},St=class extends ne{parent;source;scopes;records=new Map;_ngOnDestroyHooks=new Set;_onDestroyHooks=[];get destroyed(){return this._destroyed}_destroyed=!1;injectorDefTypes;constructor(t,n,r,o){super(),this.parent=n,this.source=r,this.scopes=o,Rs(t,s=>this.processProvider(s)),this.records.set(Ys,en(void 0,this)),o.has("environment")&&this.records.set(ne,en(void 0,this));let i=this.records.get(zn);i!=null&&typeof i.value=="string"&&this.scopes.add(i.value),this.injectorDefTypes=new Set(this.get(Ks,st,{self:!0}))}retrieve(t,n){let r=It(n)||0;try{return this.get(t,Et,r)}catch(o){if($t(o))return o;throw o}}destroy(){Fn(this),this._destroyed=!0;let t=j(null);try{for(let r of this._ngOnDestroyHooks)r.ngOnDestroy();let n=this._onDestroyHooks;this._onDestroyHooks=[];for(let r of n)r()}finally{this.records.clear(),this._ngOnDestroyHooks.clear(),this.injectorDefTypes.clear(),j(t)}}onDestroy(t){return Fn(this),this._onDestroyHooks.push(t),()=>this.removeOnDestroy(t)}runInContext(t){Fn(this);let n=Oe(this),r=he(void 0),o;try{return t()}finally{Oe(n),he(r)}}get(t,n=Et,r){if(Fn(this),t.hasOwnProperty(Wl))return t[Wl](this);let o=It(r),i,s=Oe(this),a=he(void 0);try{if(!(o&4)){let l=this.records.get(t);if(l===void 0){let f=tg(t)&&$n(t);f&&this.injectableDefInScope(f)?l=en(xs(t),Do):l=null,this.records.set(t,l)}if(l!=null)return this.hydrate(t,l,o)}let c=o&2?Gn():this.parent;return n=o&8&&n===Et?null:n,c.get(t,n)}catch(c){let l=Hh(c);throw l===-200||l===-201?new I(l,null):c}finally{he(a),Oe(s)}}resolveInjectorInitializers(){let t=j(null),n=Oe(this),r=he(void 0),o;try{let i=this.get(Ze,st,{self:!0});for(let s of i)s()}finally{Oe(n),he(r),j(t)}}toString(){let t=[],n=this.records;for(let r of n.keys())t.push(We(r));return`R3Injector[${t.join(", ")}]`}processProvider(t){t=ue(t);let n=No(t)?t:ue(t&&t.provide),r=Jh(t);if(!No(t)&&t.multi===!0){let o=this.records.get(n);o||(o=en(void 0,Do,!0),o.factory=()=>Ns(o.multi),this.records.set(n,o)),n=t,o.multi.push(t)}this.records.set(n,r)}hydrate(t,n,r){let o=j(null);try{if(n.value===Zl)throw Ws(We(t));return n.value===Do&&(n.value=Zl,n.value=n.factory(void 0,r)),typeof n.value=="object"&&n.value&&eg(n.value)&&this._ngOnDestroyHooks.add(n.value),n.value}finally{j(o)}}injectableDefInScope(t){if(!t.providedIn)return!1;let n=ue(t.providedIn);return typeof n=="string"?n==="any"||this.scopes.has(n):this.injectorDefTypes.has(n)}removeOnDestroy(t){let n=this._onDestroyHooks.indexOf(t);n!==-1&&this._onDestroyHooks.splice(n,1)}};function xs(e){let t=$n(e),n=t!==null?t.factory:wt(e);if(n!==null)return n;if(e instanceof w)throw new I(204,!1);if(e instanceof Function)return Kh(e);throw new I(204,!1)}function Kh(e){if(e.length>0)throw new I(204,!1);let n=Fh(e);return n!==null?()=>n.factory(e):()=>new e}function Jh(e){if(su(e))return en(void 0,e.useValue);{let t=au(e);return en(t,Do)}}function au(e,t,n){let r;if(No(e)){let o=ue(e);return wt(o)||xs(o)}else if(su(e))r=()=>ue(e.useValue);else if(Yh(e))r=()=>e.useFactory(...Ns(e.deps||[]));else if(Qh(e))r=(o,i)=>M(ue(e.useExisting),i!==void 0&&i&8?8:void 0);else{let o=ue(e&&(e.useClass||e.provide));if(Xh(e))r=()=>new o(...Ns(e.deps));else return wt(o)||xs(o)}return r}function Fn(e){if(e.destroyed)throw new I(205,!1)}function en(e,t,n=!1){return{factory:e,value:t,multi:n?[]:void 0}}function Xh(e){return!!e.deps}function eg(e){return e!==null&&typeof e=="object"&&typeof e.ngOnDestroy=="function"}function tg(e){return typeof e=="function"||typeof e=="object"&&e.ngMetadataName==="InjectionToken"}function Rs(e,t){for(let n of e)Array.isArray(n)?Rs(n,t):n&&Us(n)?Rs(n.\u0275providers,t):t(n)}function oe(e,t){let n;e instanceof St?(Fn(e),n=e):n=new _s(e);let r,o=Oe(n),i=he(void 0);try{return t()}finally{Oe(o),he(i)}}function cu(){return Xl()!==void 0||Zr()!=null}var Te=0,N=1,_=2,re=3,Se=4,be=5,qn=6,Po=7,ge=8,nn=9,Qe=10,ie=11,rn=12,na=13,on=14,Ce=15,sn=16,Mt=17,Wn=18,Zn=19,ra=20,qe=21,Lo=22,Qn=23,me=24,Fo=25,Me=26,lu=1;var lt=7,Yn=8,Kn=9,de=10;function Pe(e){return Array.isArray(e)&&typeof e[lu]=="object"}function _e(e){return Array.isArray(e)&&e[lu]===!0}function oa(e){return(e.flags&4)!==0}function an(e){return e.componentOffset>-1}function ia(e){return(e.flags&1)===1}function _t(e){return!!e.template}function cn(e){return(e[_]&512)!==0}function Nt(e){return(e[_]&256)===256}var sa="svg",uu="math";function Ne(e){for(;Array.isArray(e);)e=e[Te];return e}function du(e,t){return Ne(t[e])}function ut(e,t){return Ne(t[e.index])}function aa(e,t){return e.data[t]}function Le(e,t){let n=t[e];return Pe(n)?n:n[Te]}function jo(e){return(e[_]&128)===128}function fu(e){return _e(e[re])}function ca(e,t){return t==null?null:e[t]}function la(e){e[Mt]=0}function ua(e){e[_]&1024||(e[_]|=1024,jo(e)&&Xn(e))}function Jn(e){return!!(e[_]&9216||e[me]?.dirty)}function Vo(e){e[Qe].changeDetectionScheduler?.notify(8),e[_]&64&&(e[_]|=1024),Jn(e)&&Xn(e)}function Xn(e){e[Qe].changeDetectionScheduler?.notify(0);let t=at(e);for(;t!==null&&!(t[_]&8192||(t[_]|=8192,!jo(t)));)t=at(t)}function da(e,t){if(Nt(e))throw new I(911,!1);e[qe]===null&&(e[qe]=[]),e[qe].push(t)}function pu(e,t){if(e[qe]===null)return;let n=e[qe].indexOf(t);n!==-1&&e[qe].splice(n,1)}function at(e){let t=e[re];return _e(t)?t[re]:t}var P={lFrame:xu(null),bindingsEnabled:!0,skipHydrationRootTNode:null},er=function(e){return e[e.Off=0]="Off",e[e.Exhaustive=1]="Exhaustive",e[e.OnlyDirtyViews=2]="OnlyDirtyViews",e}(er||{}),ng=0,As=!1;function hu(){return P.lFrame.elementDepthCount}function gu(){P.lFrame.elementDepthCount++}function mu(){P.lFrame.elementDepthCount--}function vu(){return P.bindingsEnabled}function yu(){return P.skipHydrationRootTNode!==null}function Eu(e){return P.skipHydrationRootTNode===e}function Iu(){P.skipHydrationRootTNode=null}function fe(){return P.lFrame.lView}function Ho(){return P.lFrame.tView}function Fe(){let e=fa();for(;e!==null&&e.type===64;)e=e.parent;return e}function fa(){return P.lFrame.currentTNode}function wu(){let e=P.lFrame,t=e.currentTNode;return e.isParent?t:t.parent}function tr(e,t){let n=P.lFrame;n.currentTNode=e,n.isParent=t}function pa(){return P.lFrame.isParent}function Su(){P.lFrame.isParent=!1}function ha(e){Vs("Must never be called in production mode"),ng=e}function ga(){return As}function ma(e){let t=As;return As=e,t}function bu(e){return P.lFrame.bindingIndex=e}function Cu(e){let t=P.lFrame,n=t.bindingIndex;return t.bindingIndex=t.bindingIndex+e,n}function Du(){return P.lFrame.inI18n}function Tu(e,t){let n=P.lFrame;n.bindingIndex=n.bindingRootIndex=e,Uo(t)}function Mu(){return P.lFrame.currentDirectiveIndex}function Uo(e){P.lFrame.currentDirectiveIndex=e}function _u(e){let t=P.lFrame.currentDirectiveIndex;return t===-1?null:e[t]}function va(e){P.lFrame.currentQueryIndex=e}function rg(e){let t=e[N];return t.type===2?t.declTNode:t.type===1?e[be]:null}function ya(e,t,n){if(n&4){let o=t,i=e;for(;o=o.parent,o===null&&!(n&1);)if(o=rg(i),o===null||(i=i[on],o.type&10))break;if(o===null)return!1;t=o,e=i}let r=P.lFrame=Nu();return r.currentTNode=t,r.lView=e,!0}function $o(e){let t=Nu(),n=e[N];P.lFrame=t,t.currentTNode=n.firstChild,t.lView=e,t.tView=n,t.contextLView=e,t.bindingIndex=n.bindingStartIndex,t.inI18n=!1}function Nu(){let e=P.lFrame,t=e===null?null:e.child;return t===null?xu(e):t}function xu(e){let t={currentTNode:null,isParent:!0,lView:null,tView:null,selectedIndex:-1,contextLView:null,elementDepthCount:0,currentNamespace:null,currentDirectiveIndex:-1,bindingRootIndex:-1,bindingIndex:-1,currentQueryIndex:0,parent:e,child:null,inI18n:!1};return e!==null&&(e.child=t),t}function Ru(){let e=P.lFrame;return P.lFrame=e.parent,e.currentTNode=null,e.lView=null,e}var Ea=Ru;function Bo(){let e=Ru();e.isParent=!0,e.tView=null,e.selectedIndex=-1,e.contextLView=null,e.elementDepthCount=0,e.currentDirectiveIndex=-1,e.currentNamespace=null,e.bindingRootIndex=-1,e.bindingIndex=-1,e.currentQueryIndex=0}function nr(){return P.lFrame.selectedIndex}function dt(e){P.lFrame.selectedIndex=e}function L(){P.lFrame.currentNamespace=sa}function F(){og()}function og(){P.lFrame.currentNamespace=null}function Au(){return P.lFrame.currentNamespace}var Ou=!0;function Ia(){return Ou}function wa(e){Ou=e}function Os(e,t=null,n=null,r){let o=Sa(e,t,n,r);return o.resolveInjectorInitializers(),o}function Sa(e,t=null,n=null,r,o=new Set){let i=[n||st,ou(e)];return r=r||(typeof e=="object"?void 0:We(e)),new St(i,t||Gn(),r||null,o)}var we=class e{static THROW_IF_NOT_FOUND=Et;static NULL=new Hn;static create(t,n){if(Array.isArray(t))return Os({name:""},n,t,"");{let r=t.name??"";return Os({name:r},t.parent,t.providers,r)}}static \u0275prov=b({token:e,providedIn:"any",factory:()=>M(Ys)});static __NG_ELEMENT_ID__=-1},Q=new w(""),xt=(()=>{class e{static __NG_ELEMENT_ID__=ig;static __NG_ENV_ID__=n=>n}return e})(),ks=class extends xt{_lView;constructor(t){super(),this._lView=t}get destroyed(){return Nt(this._lView)}onDestroy(t){let n=this._lView;return da(n,t),()=>pu(n,t)}};function ig(){return new ks(fe())}var ke=class{_console=console;handleError(t){this._console.error("ERROR",t)}},xe=new w("",{providedIn:"root",factory:()=>{let e=v(ne),t;return n=>{e.destroyed&&!t?setTimeout(()=>{throw n}):(t??=e.get(ke),t.handleError(n))}}}),ku={provide:Ze,useValue:()=>void v(ke),multi:!0},sg=new w("",{providedIn:"root",factory:()=>{let e=v(Q).defaultView;if(!e)return;let t=v(xe),n=i=>{t(i.reason),i.preventDefault()},r=i=>{i.error?t(i.error):t(new Error(i.message,{cause:i})),i.preventDefault()},o=()=>{e.addEventListener("unhandledrejection",n),e.addEventListener("error",r)};typeof Zone<"u"?Zone.root.run(o):o(),v(xt).onDestroy(()=>{e.removeEventListener("error",r),e.removeEventListener("unhandledrejection",n)})}});function ba(){return Tt([ru(()=>void v(sg))])}function rr(e,t){let[n,r,o]=ds(e,t?.equal),i=n,s=i[$e];return i.set=r,i.update=o,i.asReadonly=Pu.bind(i),i}function Pu(){let e=this[$e];if(e.readonlyFn===void 0){let t=()=>this();t[$e]=e,e.readonlyFn=t}return e.readonlyFn}var bt=class{},or=new w("",{providedIn:"root",factory:()=>!1});var Ca=new w(""),Da=new w("");var Ye=(()=>{class e{taskId=0;pendingTasks=new Set;destroyed=!1;pendingTask=new te(!1);get hasPendingTasks(){return this.destroyed?!1:this.pendingTask.value}get hasPendingTasksObservable(){return this.destroyed?new k(n=>{n.next(!1),n.complete()}):this.pendingTask}add(){!this.hasPendingTasks&&!this.destroyed&&this.pendingTask.next(!0);let n=this.taskId++;return this.pendingTasks.add(n),n}has(n){return this.pendingTasks.has(n)}remove(n){this.pendingTasks.delete(n),this.pendingTasks.size===0&&this.hasPendingTasks&&this.pendingTask.next(!1)}ngOnDestroy(){this.pendingTasks.clear(),this.hasPendingTasks&&this.pendingTask.next(!1),this.destroyed=!0,this.pendingTask.unsubscribe()}static \u0275prov=b({token:e,providedIn:"root",factory:()=>new e})}return e})();function ir(...e){}var Ta=(()=>{class e{static \u0275prov=b({token:e,providedIn:"root",factory:()=>new Ps})}return e})(),Ps=class{dirtyEffectCount=0;queues=new Map;add(t){this.enqueue(t),this.schedule(t)}schedule(t){t.dirty&&this.dirtyEffectCount++}remove(t){let n=t.zone,r=this.queues.get(n);r.has(t)&&(r.delete(t),t.dirty&&this.dirtyEffectCount--)}enqueue(t){let n=t.zone;this.queues.has(n)||this.queues.set(n,new Set);let r=this.queues.get(n);r.has(t)||r.add(t)}flush(){for(;this.dirtyEffectCount>0;){let t=!1;for(let[n,r]of this.queues)n===null?t||=this.flushQueue(r):t||=n.run(()=>this.flushQueue(r));t||(this.dirtyEffectCount=0)}}flushQueue(t){let n=!1;for(let r of t)r.dirty&&(this.dirtyEffectCount--,n=!0,r.run());return n}};function si(e){return{toString:e}.toString()}function yg(e){return typeof e=="function"}var Zo=class{previousValue;currentValue;firstChange;constructor(t,n,r){this.previousValue=t,this.currentValue=n,this.firstChange=r}isFirstChange(){return this.firstChange}};function fd(e,t,n,r){t!==null?t.applyValueToInputSignal(t,r):e[n]=r}var pr=(()=>{let e=()=>pd;return e.ngInherit=!0,e})();function pd(e){return e.type.prototype.ngOnChanges&&(e.setInput=Ig),Eg}function Eg(){let e=gd(this),t=e?.current;if(t){let n=e.previous;if(n===Dt)e.previous=t;else for(let r in t)n[r]=t[r];e.current=null,this.ngOnChanges(t)}}function Ig(e,t,n,r,o){let i=this.declaredInputs[r],s=gd(e)||wg(e,{previous:Dt,current:null}),a=s.current||(s.current={}),c=s.previous,l=c[i];a[i]=new Zo(l&&l.currentValue,n,c===Dt),fd(e,t,o,n)}var hd="__ngSimpleChanges__";function gd(e){return e[hd]||null}function wg(e,t){return e[hd]=t}var Lu=[];var B=function(e,t=null,n){for(let r=0;r<Lu.length;r++){let o=Lu[r];o(e,t,n)}};function Sg(e,t,n){let{ngOnChanges:r,ngOnInit:o,ngDoCheck:i}=t.type.prototype;if(r){let s=pd(t);(n.preOrderHooks??=[]).push(e,s),(n.preOrderCheckHooks??=[]).push(e,s)}o&&(n.preOrderHooks??=[]).push(0-e,o),i&&((n.preOrderHooks??=[]).push(e,i),(n.preOrderCheckHooks??=[]).push(e,i))}function bg(e,t){for(let n=t.directiveStart,r=t.directiveEnd;n<r;n++){let i=e.data[n].type.prototype,{ngAfterContentInit:s,ngAfterContentChecked:a,ngAfterViewInit:c,ngAfterViewChecked:l,ngOnDestroy:f}=i;s&&(e.contentHooks??=[]).push(-n,s),a&&((e.contentHooks??=[]).push(n,a),(e.contentCheckHooks??=[]).push(n,a)),c&&(e.viewHooks??=[]).push(-n,c),l&&((e.viewHooks??=[]).push(n,l),(e.viewCheckHooks??=[]).push(n,l)),f!=null&&(e.destroyHooks??=[]).push(n,f)}}function Go(e,t,n){md(e,t,3,n)}function qo(e,t,n,r){(e[_]&3)===n&&md(e,t,n,r)}function Ma(e,t){let n=e[_];(n&3)===t&&(n&=16383,n+=1,e[_]=n)}function md(e,t,n,r){let o=r!==void 0?e[Mt]&65535:0,i=r??-1,s=t.length-1,a=0;for(let c=o;c<s;c++)if(typeof t[c+1]=="number"){if(a=t[c],r!=null&&a>=r)break}else t[c]<0&&(e[Mt]+=65536),(a<i||i==-1)&&(Cg(e,n,t,c),e[Mt]=(e[Mt]&**********)+c+2),c++}function Fu(e,t){B(4,e,t);let n=j(null);try{t.call(e)}finally{j(n),B(5,e,t)}}function Cg(e,t,n,r){let o=n[r]<0,i=n[r+1],s=o?-n[r]:n[r],a=e[s];o?e[_]>>14<e[Mt]>>16&&(e[_]&3)===t&&(e[_]+=16384,Fu(a,i)):Fu(a,i)}var un=-1,cr=class{factory;name;injectImpl;resolving=!1;canSeeViewProviders;multi;componentProviders;index;providerFactory;constructor(t,n,r,o){this.factory=t,this.name=o,this.canSeeViewProviders=n,this.injectImpl=r}};function Dg(e){return(e.flags&8)!==0}function Tg(e){return(e.flags&16)!==0}function Mg(e,t,n){let r=0;for(;r<n.length;){let o=n[r];if(typeof o=="number"){if(o!==0)break;r++;let i=n[r++],s=n[r++],a=n[r++];e.setAttribute(t,s,a,i)}else{let i=o,s=n[++r];Ng(i)?e.setProperty(t,i,s):e.setAttribute(t,i,s),r++}}return r}function _g(e){return e===3||e===4||e===6}function Ng(e){return e.charCodeAt(0)===64}function vd(e,t){if(!(t===null||t.length===0))if(e===null||e.length===0)e=t.slice();else{let n=-1;for(let r=0;r<t.length;r++){let o=t[r];typeof o=="number"?n=o:n===0||(n===-1||n===2?ju(e,n,o,null,t[++r]):ju(e,n,o,null,null))}}return e}function ju(e,t,n,r,o){let i=0,s=e.length;if(t===-1)s=-1;else for(;i<e.length;){let a=e[i++];if(typeof a=="number"){if(a===t){s=-1;break}else if(a>t){s=i-1;break}}}for(;i<e.length;){let a=e[i];if(typeof a=="number")break;if(a===n){o!==null&&(e[i+1]=o);return}i++,o!==null&&i++}s!==-1&&(e.splice(s,0,t),i=s+1),e.splice(i++,0,n),o!==null&&e.splice(i++,0,o)}function yd(e){return e!==un}function Qo(e){return e&32767}function xg(e){return e>>16}function Yo(e,t){let n=xg(e),r=t;for(;n>0;)r=r[on],n--;return r}var Oa=!0;function Vu(e){let t=Oa;return Oa=e,t}var Rg=256,Ed=Rg-1,Id=5,Ag=0,je={};function Og(e,t,n){let r;typeof n=="string"?r=n.charCodeAt(0)||0:n.hasOwnProperty(Ct)&&(r=n[Ct]),r==null&&(r=n[Ct]=Ag++);let o=r&Ed,i=1<<o;t.data[e+(o>>Id)]|=i}function wd(e,t){let n=Sd(e,t);if(n!==-1)return n;let r=t[N];r.firstCreatePass&&(e.injectorIndex=t.length,_a(r.data,e),_a(t,null),_a(r.blueprint,null));let o=Ja(e,t),i=e.injectorIndex;if(yd(o)){let s=Qo(o),a=Yo(o,t),c=a[N].data;for(let l=0;l<8;l++)t[i+l]=a[s+l]|c[s+l]}return t[i+8]=o,i}function _a(e,t){e.push(0,0,0,0,0,0,0,0,t)}function Sd(e,t){return e.injectorIndex===-1||e.parent&&e.parent.injectorIndex===e.injectorIndex||t[e.injectorIndex+8]===null?-1:e.injectorIndex}function Ja(e,t){if(e.parent&&e.parent.injectorIndex!==-1)return e.parent.injectorIndex;let n=0,r=null,o=t;for(;o!==null;){if(r=Md(o),r===null)return un;if(n++,o=o[on],r.injectorIndex!==-1)return r.injectorIndex|n<<16}return un}function kg(e,t,n){Og(e,t,n)}function bd(e,t,n){if(n&8||e!==void 0)return e;Ao(t,"NodeInjector")}function Cd(e,t,n,r){if(n&8&&r===void 0&&(r=null),(n&3)===0){let o=e[nn],i=he(void 0);try{return o?o.get(t,r,n&8):Zs(t,r,n&8)}finally{he(i)}}return bd(r,t,n)}function Dd(e,t,n,r=0,o){if(e!==null){if(t[_]&2048&&!(r&2)){let s=Vg(e,t,n,r,je);if(s!==je)return s}let i=Td(e,t,n,r,je);if(i!==je)return i}return Cd(t,n,r,o)}function Td(e,t,n,r,o){let i=Fg(n);if(typeof i=="function"){if(!ya(t,e,r))return r&1?bd(o,n,r):Cd(t,n,r,o);try{let s;if(s=i(r),s==null&&!(r&8))Ao(n);else return s}finally{Ea()}}else if(typeof i=="number"){let s=null,a=Sd(e,t),c=un,l=r&1?t[Ce][be]:null;for((a===-1||r&4)&&(c=a===-1?Ja(e,t):t[a+8],c===un||!Uu(r,!1)?a=-1:(s=t[N],a=Qo(c),t=Yo(c,t)));a!==-1;){let f=t[N];if(Hu(i,a,f.data)){let p=Pg(a,t,n,s,r,l);if(p!==je)return p}c=t[a+8],c!==un&&Uu(r,t[N].data[a+8]===l)&&Hu(i,a,t)?(s=f,a=Qo(c),t=Yo(c,t)):a=-1}}return o}function Pg(e,t,n,r,o,i){let s=t[N],a=s.data[e+8],c=r==null?an(a)&&Oa:r!=s&&(a.type&3)!==0,l=o&1&&i===a,f=Lg(a,s,n,c,l);return f!==null?ka(t,s,f,a,o):je}function Lg(e,t,n,r,o){let i=e.providerIndexes,s=t.data,a=i&1048575,c=e.directiveStart,l=e.directiveEnd,f=i>>20,p=r?a:a+f,y=o?a+f:l;for(let h=p;h<y;h++){let S=s[h];if(h<c&&n===S||h>=c&&S.type===n)return h}if(o){let h=s[c];if(h&&_t(h)&&h.type===n)return c}return null}function ka(e,t,n,r,o){let i=e[n],s=t.data;if(i instanceof cr){let a=i;if(a.resolving){let h=Mo(s[n]);throw Ws(h)}let c=Vu(a.canSeeViewProviders);a.resolving=!0;let l=s[n].type||s[n],f,p=a.injectImpl?he(a.injectImpl):null,y=ya(e,r,0);try{i=e[n]=a.factory(void 0,o,s,e,r),t.firstCreatePass&&n>=r.directiveStart&&Sg(n,s[n],t)}finally{p!==null&&he(p),Vu(c),a.resolving=!1,Ea()}}return i}function Fg(e){if(typeof e=="string")return e.charCodeAt(0)||0;let t=e.hasOwnProperty(Ct)?e[Ct]:void 0;return typeof t=="number"?t>=0?t&Ed:jg:t}function Hu(e,t,n){let r=1<<e;return!!(n[t+(e>>Id)]&r)}function Uu(e,t){return!(e&2)&&!(e&1&&t)}var Rt=class{_tNode;_lView;constructor(t,n){this._tNode=t,this._lView=n}get(t,n,r){return Dd(this._tNode,this._lView,t,It(r),n)}};function jg(){return new Rt(Fe(),fe())}function ai(e){return si(()=>{let t=e.prototype.constructor,n=t[Vn]||Pa(t),r=Object.prototype,o=Object.getPrototypeOf(e.prototype).constructor;for(;o&&o!==r;){let i=o[Vn]||Pa(o);if(i&&i!==n)return i;o=Object.getPrototypeOf(o)}return i=>new i})}function Pa(e){return js(e)?()=>{let t=Pa(ue(e));return t&&t()}:wt(e)}function Vg(e,t,n,r,o){let i=e,s=t;for(;i!==null&&s!==null&&s[_]&2048&&!cn(s);){let a=Td(i,s,n,r|2,je);if(a!==je)return a;let c=i.parent;if(!c){let l=s[ra];if(l){let f=l.get(n,je,r);if(f!==je)return f}c=Md(s),s=s[on]}i=c}return o}function Md(e){let t=e[N],n=t.type;return n===2?t.declTNode:n===1?e[be]:null}function Hg(){return Xa(Fe(),fe())}function Xa(e,t){return new ci(ut(e,t))}var ci=(()=>{class e{nativeElement;constructor(n){this.nativeElement=n}static __NG_ELEMENT_ID__=Hg}return e})();function _d(e){return(e.flags&128)===128}var ec=function(e){return e[e.OnPush=0]="OnPush",e[e.Default=1]="Default",e}(ec||{}),Nd=new Map,Ug=0;function $g(){return Ug++}function Bg(e){Nd.set(e[Zn],e)}function La(e){Nd.delete(e[Zn])}var $u="__ngContext__";function lr(e,t){Pe(t)?(e[$u]=t[Zn],Bg(t)):e[$u]=t}function xd(e){return Ad(e[rn])}function Rd(e){return Ad(e[Se])}function Ad(e){for(;e!==null&&!_e(e);)e=e[Se];return e}var Fa;function tc(e){Fa=e}function Od(){if(Fa!==void 0)return Fa;if(typeof document<"u")return document;throw new I(210,!1)}var li=new w("",{providedIn:"root",factory:()=>zg}),zg="ng",ui=new w(""),hn=new w("",{providedIn:"platform",factory:()=>"unknown"});var di=new w("",{providedIn:"root",factory:()=>Od().body?.querySelector("[ngCspNonce]")?.getAttribute("ngCspNonce")||null}),fi={breakpoints:[16,32,48,64,96,128,256,384,640,750,828,1080,1200,1920,2048,3840],placeholderResolution:30,disableImageSizeWarning:!1,disableImageLazyLoadWarning:!1},pi=new w("",{providedIn:"root",factory:()=>fi});var Gg="h",qg="b";var kd=!1,Pd=new w("",{providedIn:"root",factory:()=>kd});function nc(e){return(e.flags&32)===32}var Wg=()=>null;function Ld(e,t,n=!1){return Wg(e,t,n)}function Fd(e,t){let n=e.contentQueries;if(n!==null){let r=j(null);try{for(let o=0;o<n.length;o+=2){let i=n[o],s=n[o+1];if(s!==-1){let a=e.data[s];va(i),a.contentQueries(2,t[s],s)}}}finally{j(r)}}}function ja(e,t,n){va(0);let r=j(null);try{t(e,n)}finally{j(r)}}function jd(e,t,n){if(oa(t)){let r=j(null);try{let o=t.directiveStart,i=t.directiveEnd;for(let s=o;s<i;s++){let a=e.data[s];if(a.contentQueries){let c=n[s];a.contentQueries(1,c,s)}}}finally{j(r)}}}var Ke=function(e){return e[e.Emulated=0]="Emulated",e[e.None=2]="None",e[e.ShadowDom=3]="ShadowDom",e}(Ke||{});var Va=class{changingThisBreaksApplicationSecurity;constructor(t){this.changingThisBreaksApplicationSecurity=t}toString(){return`SafeValue must use [property]=binding: ${this.changingThisBreaksApplicationSecurity} (see ${Ls})`}};function hi(e){return e instanceof Va?e.changingThisBreaksApplicationSecurity:e}function Vd(e){return e instanceof Function?e():e}function Zg(e,t,n){let r=e.length;for(;;){let o=e.indexOf(t,n);if(o===-1)return o;if(o===0||e.charCodeAt(o-1)<=32){let i=t.length;if(o+i===r||e.charCodeAt(o+i)<=32)return o}n=o+1}}var Hd="ng-template";function Qg(e,t,n,r){let o=0;if(r){for(;o<t.length&&typeof t[o]=="string";o+=2)if(t[o]==="class"&&Zg(t[o+1].toLowerCase(),n,0)!==-1)return!0}else if(rc(e))return!1;if(o=t.indexOf(1,o),o>-1){let i;for(;++o<t.length&&typeof(i=t[o])=="string";)if(i.toLowerCase()===n)return!0}return!1}function rc(e){return e.type===4&&e.value!==Hd}function Yg(e,t,n){let r=e.type===4&&!n?Hd:e.value;return t===r}function Kg(e,t,n){let r=4,o=e.attrs,i=o!==null?em(o):0,s=!1;for(let a=0;a<t.length;a++){let c=t[a];if(typeof c=="number"){if(!s&&!Re(r)&&!Re(c))return!1;if(s&&Re(c))continue;s=!1,r=c|r&1;continue}if(!s)if(r&4){if(r=2|r&1,c!==""&&!Yg(e,c,n)||c===""&&t.length===1){if(Re(r))return!1;s=!0}}else if(r&8){if(o===null||!Qg(e,o,c,n)){if(Re(r))return!1;s=!0}}else{let l=t[++a],f=Jg(c,o,rc(e),n);if(f===-1){if(Re(r))return!1;s=!0;continue}if(l!==""){let p;if(f>i?p="":p=o[f+1].toLowerCase(),r&2&&l!==p){if(Re(r))return!1;s=!0}}}}return Re(r)||s}function Re(e){return(e&1)===0}function Jg(e,t,n,r){if(t===null)return-1;let o=0;if(r||!n){let i=!1;for(;o<t.length;){let s=t[o];if(s===e)return o;if(s===3||s===6)i=!0;else if(s===1||s===2){let a=t[++o];for(;typeof a=="string";)a=t[++o];continue}else{if(s===4)break;if(s===0){o+=4;continue}}o+=i?1:2}return-1}else return tm(t,e)}function Xg(e,t,n=!1){for(let r=0;r<t.length;r++)if(Kg(e,t[r],n))return!0;return!1}function em(e){for(let t=0;t<e.length;t++){let n=e[t];if(_g(n))return t}return e.length}function tm(e,t){let n=e.indexOf(4);if(n>-1)for(n++;n<e.length;){let r=e[n];if(typeof r=="number")return-1;if(r===t)return n;n++}return-1}function Bu(e,t){return e?":not("+t.trim()+")":t}function nm(e){let t=e[0],n=1,r=2,o="",i=!1;for(;n<e.length;){let s=e[n];if(typeof s=="string")if(r&2){let a=e[++n];o+="["+s+(a.length>0?'="'+a+'"':"")+"]"}else r&8?o+="."+s:r&4&&(o+=" "+s);else o!==""&&!Re(s)&&(t+=Bu(i,o),o=""),r=s,i=i||!Re(r);n++}return o!==""&&(t+=Bu(i,o)),t}function rm(e){return e.map(nm).join(",")}function om(e){let t=[],n=[],r=1,o=2;for(;r<e.length;){let i=e[r];if(typeof i=="string")o===2?i!==""&&t.push(i,e[++r]):o===8&&n.push(i);else{if(!Re(o))break;o=i}r++}return n.length&&t.push(1,...n),t}var gn={};function im(e,t){return e.createText(t)}function Ud(e,t,n){return e.createElement(t,n)}function Ko(e,t,n,r,o){e.insertBefore(t,n,r,o)}function $d(e,t,n){e.appendChild(t,n)}function zu(e,t,n,r,o){r!==null?Ko(e,t,n,r,o):$d(e,t,n)}function sm(e,t,n){e.removeChild(null,t,n)}function am(e,t,n){e.setAttribute(t,"style",n)}function cm(e,t,n){n===""?e.removeAttribute(t,"class"):e.setAttribute(t,"class",n)}function Bd(e,t,n){let{mergedAttrs:r,classes:o,styles:i}=n;r!==null&&Mg(e,t,r),o!==null&&cm(e,t,o),i!==null&&am(e,t,i)}function zd(e,t,n,r,o,i,s,a,c,l,f){let p=Me+r,y=p+o,h=lm(p,y),S=typeof l=="function"?l():l;return h[N]={type:e,blueprint:h,template:n,queries:null,viewQuery:a,declTNode:t,data:h.slice().fill(null,p),bindingStartIndex:p,expandoStartIndex:y,hostBindingOpCodes:null,firstCreatePass:!0,firstUpdatePass:!0,staticViewQueries:!1,staticContentQueries:!1,preOrderHooks:null,preOrderCheckHooks:null,contentHooks:null,contentCheckHooks:null,viewHooks:null,viewCheckHooks:null,destroyHooks:null,cleanup:null,contentQueries:null,components:null,directiveRegistry:typeof i=="function"?i():i,pipeRegistry:typeof s=="function"?s():s,firstChild:null,schemas:c,consts:S,incompleteFirstPass:!1,ssrId:f}}function lm(e,t){let n=[];for(let r=0;r<t;r++)n.push(r<e?null:gn);return n}function um(e){let t=e.tView;return t===null||t.incompleteFirstPass?e.tView=zd(1,null,e.template,e.decls,e.vars,e.directiveDefs,e.pipeDefs,e.viewQuery,e.schemas,e.consts,e.id):t}function Gd(e,t,n,r,o,i,s,a,c,l,f){let p=t.blueprint.slice();return p[Te]=o,p[_]=r|4|128|8|64|1024,(l!==null||e&&e[_]&2048)&&(p[_]|=2048),la(p),p[re]=p[on]=e,p[ge]=n,p[Qe]=s||e&&e[Qe],p[ie]=a||e&&e[ie],p[nn]=c||e&&e[nn]||null,p[be]=i,p[Zn]=$g(),p[qn]=f,p[ra]=l,p[Ce]=t.type==2?e[Ce]:p,p}function dm(e,t,n){let r=ut(t,e),o=um(n),i=e[Qe].rendererFactory,s=Zd(e,Gd(e,o,null,qd(n),r,t,null,i.createRenderer(r,n),null,null,null));return e[t.index]=s}function qd(e){let t=16;return e.signals?t=4096:e.onPush&&(t=64),t}function Wd(e,t,n,r){if(n===0)return-1;let o=t.length;for(let i=0;i<n;i++)t.push(r),e.blueprint.push(r),e.data.push(null);return o}function Zd(e,t){return e[rn]?e[na][Se]=t:e[rn]=t,e[na]=t,t}function fm(e,t,n,r){if(!r)if((t[_]&3)===3){let i=e.preOrderCheckHooks;i!==null&&Go(t,i,n)}else{let i=e.preOrderHooks;i!==null&&qo(t,i,0,n)}dt(n)}var gi=function(e){return e[e.None=0]="None",e[e.SignalBased=1]="SignalBased",e[e.HasDecoratorInputTransform=2]="HasDecoratorInputTransform",e}(gi||{});function Ha(e,t,n,r){let o=j(null);try{let[i,s,a]=e.inputs[n],c=null;(s&gi.SignalBased)!==0&&(c=t[i][$e]),c!==null&&c.transformFn!==void 0?r=c.transformFn(r):a!==null&&(r=a.call(t,r)),e.setInput!==null?e.setInput(t,c,r,n,i):fd(t,c,i,r)}finally{j(o)}}var Je=function(e){return e[e.Important=1]="Important",e[e.DashCase=2]="DashCase",e}(Je||{}),pm;function oc(e,t){return pm(e,t)}function ln(e,t,n,r,o){if(r!=null){let i,s=!1;_e(r)?i=r:Pe(r)&&(s=!0,r=r[Te]);let a=Ne(r);e===0&&n!==null?o==null?$d(t,n,a):Ko(t,n,a,o||null,!0):e===1&&n!==null?Ko(t,n,a,o||null,!0):e===2?sm(t,a,s):e===3&&t.destroyNode(a),i!=null&&Dm(t,e,i,n,o)}}function hm(e,t){Qd(e,t),t[Te]=null,t[be]=null}function gm(e,t,n,r,o,i){r[Te]=o,r[be]=t,mi(e,r,n,1,o,i)}function Qd(e,t){t[Qe].changeDetectionScheduler?.notify(9),mi(e,t,t[ie],2,null,null)}function mm(e){let t=e[rn];if(!t)return Na(e[N],e);for(;t;){let n=null;if(Pe(t))n=t[rn];else{let r=t[de];r&&(n=r)}if(!n){for(;t&&!t[Se]&&t!==e;)Pe(t)&&Na(t[N],t),t=t[re];t===null&&(t=e),Pe(t)&&Na(t[N],t),n=t&&t[Se]}t=n}}function ic(e,t){let n=e[Kn],r=n.indexOf(t);n.splice(r,1)}function Yd(e,t){if(Nt(t))return;let n=t[ie];n.destroyNode&&mi(e,t,n,3,null,null),mm(t)}function Na(e,t){if(Nt(t))return;let n=j(null);try{t[_]&=-129,t[_]|=256,t[me]&&ls(t[me]),ym(e,t),vm(e,t),t[N].type===1&&t[ie].destroy();let r=t[sn];if(r!==null&&_e(t[re])){r!==t[re]&&ic(r,t);let o=t[Wn];o!==null&&o.detachView(e)}La(t)}finally{j(n)}}function vm(e,t){let n=e.cleanup,r=t[Po];if(n!==null)for(let s=0;s<n.length-1;s+=2)if(typeof n[s]=="string"){let a=n[s+3];a>=0?r[a]():r[-a].unsubscribe(),s+=2}else{let a=r[n[s+1]];n[s].call(a)}r!==null&&(t[Po]=null);let o=t[qe];if(o!==null){t[qe]=null;for(let s=0;s<o.length;s++){let a=o[s];a()}}let i=t[Qn];if(i!==null){t[Qn]=null;for(let s of i)s.destroy()}}function ym(e,t){let n;if(e!=null&&(n=e.destroyHooks)!=null)for(let r=0;r<n.length;r+=2){let o=t[n[r]];if(!(o instanceof cr)){let i=n[r+1];if(Array.isArray(i))for(let s=0;s<i.length;s+=2){let a=o[i[s]],c=i[s+1];B(4,a,c);try{c.call(a)}finally{B(5,a,c)}}else{B(4,o,i);try{i.call(o)}finally{B(5,o,i)}}}}}function Em(e,t,n){return Im(e,t.parent,n)}function Im(e,t,n){let r=t;for(;r!==null&&r.type&168;)t=r,r=t.parent;if(r===null)return n[Te];if(an(r)){let{encapsulation:o}=e.data[r.directiveStart+r.componentOffset];if(o===Ke.None||o===Ke.Emulated)return null}return ut(r,n)}function wm(e,t,n){return bm(e,t,n)}function Sm(e,t,n){return e.type&40?ut(e,n):null}var bm=Sm,Gu;function Kd(e,t,n,r){let o=Em(e,r,t),i=t[ie],s=r.parent||t[be],a=wm(s,r,t);if(o!=null)if(Array.isArray(n))for(let c=0;c<n.length;c++)zu(i,o,n[c],a,!1);else zu(i,o,n,a,!1);Gu!==void 0&&Gu(i,r,t,n,o)}function sr(e,t){if(t!==null){let n=t.type;if(n&3)return ut(t,e);if(n&4)return Ua(-1,e[t.index]);if(n&8){let r=t.child;if(r!==null)return sr(e,r);{let o=e[t.index];return _e(o)?Ua(-1,o):Ne(o)}}else{if(n&128)return sr(e,t.next);if(n&32)return oc(t,e)()||Ne(e[t.index]);{let r=Jd(e,t);if(r!==null){if(Array.isArray(r))return r[0];let o=at(e[Ce]);return sr(o,r)}else return sr(e,t.next)}}}return null}function Jd(e,t){if(t!==null){let r=e[Ce][be],o=t.projection;return r.projection[o]}return null}function Ua(e,t){let n=de+e+1;if(n<t.length){let r=t[n],o=r[N].firstChild;if(o!==null)return sr(r,o)}return t[lt]}function sc(e,t,n,r,o,i,s){for(;n!=null;){if(n.type===128){n=n.next;continue}let a=r[n.index],c=n.type;if(s&&t===0&&(a&&lr(Ne(a),r),n.flags|=2),!nc(n))if(c&8)sc(e,t,n.child,r,o,i,!1),ln(t,e,o,a,i);else if(c&32){let l=oc(n,r),f;for(;f=l();)ln(t,e,o,f,i);ln(t,e,o,a,i)}else c&16?Cm(e,t,r,n,o,i):ln(t,e,o,a,i);n=s?n.projectionNext:n.next}}function mi(e,t,n,r,o,i){sc(n,r,e.firstChild,t,o,i,!1)}function Cm(e,t,n,r,o,i){let s=n[Ce],c=s[be].projection[r.projection];if(Array.isArray(c))for(let l=0;l<c.length;l++){let f=c[l];ln(t,e,o,f,i)}else{let l=c,f=s[re];_d(r)&&(l.flags|=128),sc(e,t,l,f,o,i,!0)}}function Dm(e,t,n,r,o){let i=n[lt],s=Ne(n);i!==s&&ln(t,e,r,i,o);for(let a=de;a<n.length;a++){let c=n[a];mi(c[N],c,e,t,r,i)}}function Tm(e,t,n,r,o){if(t)o?e.addClass(n,r):e.removeClass(n,r);else{let i=r.indexOf("-")===-1?void 0:Je.DashCase;o==null?e.removeStyle(n,r,i):(typeof o=="string"&&o.endsWith("!important")&&(o=o.slice(0,-10),i|=Je.Important),e.setStyle(n,r,o,i))}}function Xd(e,t,n,r,o){let i=nr(),s=r&2;try{dt(-1),s&&t.length>Me&&fm(e,t,Me,!1),B(s?2:0,o,n),n(r,o)}finally{dt(i),B(s?3:1,o,n)}}function ef(e,t,n){Rm(e,t,n),(n.flags&64)===64&&Am(e,t,n)}function Mm(e,t,n=ut){let r=t.localNames;if(r!==null){let o=t.index+1;for(let i=0;i<r.length;i+=2){let s=r[i+1],a=s===-1?n(t,e):e[s];e[o++]=a}}}function _m(e,t,n,r){let i=r.get(Pd,kd)||n===Ke.ShadowDom,s=e.selectRootElement(t,i);return Nm(s),s}function Nm(e){xm(e)}var xm=()=>null;function Rm(e,t,n){let r=n.directiveStart,o=n.directiveEnd;an(n)&&dm(t,n,e.data[r+n.componentOffset]),e.firstCreatePass||wd(n,t);let i=n.initialInputs;for(let s=r;s<o;s++){let a=e.data[s],c=ka(t,e,s,n);if(lr(c,t),i!==null&&Pm(t,s-r,c,a,n,i),_t(a)){let l=Le(n.index,t);l[ge]=ka(t,e,s,n)}}}function Am(e,t,n){let r=n.directiveStart,o=n.directiveEnd,i=n.index,s=Mu();try{dt(i);for(let a=r;a<o;a++){let c=e.data[a],l=t[a];Uo(a),(c.hostBindings!==null||c.hostVars!==0||c.hostAttrs!==null)&&Om(c,l)}}finally{dt(-1),Uo(s)}}function Om(e,t){e.hostBindings!==null&&e.hostBindings(1,t)}function km(e,t){let n=e.directiveRegistry,r=null;if(n)for(let o=0;o<n.length;o++){let i=n[o];Xg(t,i.selectors,!1)&&(r??=[],_t(i)?r.unshift(i):r.push(i))}return r}function Pm(e,t,n,r,o,i){let s=i[t];if(s!==null)for(let a=0;a<s.length;a+=2){let c=s[a],l=s[a+1];Ha(r,n,c,l)}}function Lm(e,t,n,r,o){let i=Me+n,s=t[N],a=o(s,t,e,r,n);t[i]=a,tr(e,!0);let c=e.type===2;return c?(Bd(t[ie],a,e),(hu()===0||ia(e))&&lr(a,t),gu()):lr(a,t),Ia()&&(!c||!nc(e))&&Kd(s,t,a,e),e}function Fm(e){let t=e;return pa()?Su():(t=t.parent,tr(t,!1)),t}function tf(e,t,n,r,o){let i=e.inputs?.[r],s=e.hostDirectiveInputs?.[r],a=!1;if(s)for(let c=0;c<s.length;c+=2){let l=s[c],f=s[c+1],p=t.data[l];Ha(p,n[l],f,o),a=!0}if(i)for(let c of i){let l=n[c],f=t.data[c];Ha(f,l,r,o),a=!0}return a}function jm(e,t){let n=Le(t,e),r=n[N];Vm(r,n);let o=n[Te];o!==null&&n[qn]===null&&(n[qn]=Ld(o,n[nn])),B(18),nf(r,n,n[ge]),B(19,n[ge])}function Vm(e,t){for(let n=t.length;n<e.blueprint.length;n++)t.push(e.blueprint[n])}function nf(e,t,n){$o(t);try{let r=e.viewQuery;r!==null&&ja(1,r,n);let o=e.template;o!==null&&Xd(e,t,o,1,n),e.firstCreatePass&&(e.firstCreatePass=!1),t[Wn]?.finishViewCreation(e),e.staticContentQueries&&Fd(e,t),e.staticViewQueries&&ja(2,e.viewQuery,n);let i=e.components;i!==null&&Hm(t,i)}catch(r){throw e.firstCreatePass&&(e.incompleteFirstPass=!0,e.firstCreatePass=!1),r}finally{t[_]&=-5,Bo()}}function Hm(e,t){for(let n=0;n<t.length;n++)jm(e,t[n])}function qu(e,t){return!t||t.firstChild===null||_d(e)}var Wu=!1,Um=new w("");function ur(e,t,n,r,o=!1){for(;n!==null;){if(n.type===128){n=o?n.projectionNext:n.next;continue}let i=t[n.index];i!==null&&r.push(Ne(i)),_e(i)&&rf(i,r);let s=n.type;if(s&8)ur(e,t,n.child,r);else if(s&32){let a=oc(n,t),c;for(;c=a();)r.push(c)}else if(s&16){let a=Jd(t,n);if(Array.isArray(a))r.push(...a);else{let c=at(t[Ce]);ur(c[N],c,a,r,!0)}}n=o?n.projectionNext:n.next}return r}function rf(e,t){for(let n=de;n<e.length;n++){let r=e[n],o=r[N].firstChild;o!==null&&ur(r[N],r,o,t)}e[lt]!==e[Te]&&t.push(e[lt])}function of(e){if(e[Fo]!==null){for(let t of e[Fo])t.impl.addSequence(t);e[Fo].length=0}}var sf=[];function $m(e){return e[me]??Bm(e)}function Bm(e){let t=sf.pop()??Object.create(Gm);return t.lView=e,t}function zm(e){e.lView[me]!==e&&(e.lView=null,sf.push(e))}var Gm=z(E({},Kr),{consumerIsAlwaysLive:!0,kind:"template",consumerMarkedDirty:e=>{Xn(e.lView)},consumerOnSignalRead(){this.lView[me]=this}});function qm(e){let t=e[me]??Object.create(Wm);return t.lView=e,t}var Wm=z(E({},Kr),{consumerIsAlwaysLive:!0,kind:"template",consumerMarkedDirty:e=>{let t=at(e.lView);for(;t&&!af(t[N]);)t=at(t);t&&ua(t)},consumerOnSignalRead(){this.lView[me]=this}});function af(e){return e.type!==2}function cf(e){if(e[Qn]===null)return;let t=!0;for(;t;){let n=!1;for(let r of e[Qn])r.dirty&&(n=!0,r.zone===null||Zone.current===r.zone?r.run():r.zone.run(()=>r.run()));t=n&&!!(e[_]&8192)}}var Zm=100;function ac(e,t=0){let r=e[Qe].rendererFactory,o=!1;o||r.begin?.();try{Qm(e,t)}finally{o||r.end?.()}}function Qm(e,t){let n=ga();try{ma(!0),$a(e,t);let r=0;for(;Jn(e);){if(r===Zm)throw new I(103,!1);r++,$a(e,1)}}finally{ma(n)}}function lf(e,t){ha(t?er.Exhaustive:er.OnlyDirtyViews);try{ac(e)}finally{ha(er.Off)}}function Ym(e,t,n,r){if(Nt(t))return;let o=t[_],i=!1,s=!1;$o(t);let a=!0,c=null,l=null;i||(af(e)?(l=$m(t),c=Xr(l)):Yr()===null?(a=!1,l=qm(t),c=Xr(l)):t[me]&&(ls(t[me]),t[me]=null));try{la(t),bu(e.bindingStartIndex),n!==null&&Xd(e,t,n,2,r);let f=(o&3)===3;if(!i)if(f){let h=e.preOrderCheckHooks;h!==null&&Go(t,h,null)}else{let h=e.preOrderHooks;h!==null&&qo(t,h,0,null),Ma(t,0)}if(s||Km(t),cf(t),uf(t,0),e.contentQueries!==null&&Fd(e,t),!i)if(f){let h=e.contentCheckHooks;h!==null&&Go(t,h)}else{let h=e.contentHooks;h!==null&&qo(t,h,1),Ma(t,1)}Xm(e,t);let p=e.components;p!==null&&ff(t,p,0);let y=e.viewQuery;if(y!==null&&ja(2,y,r),!i)if(f){let h=e.viewCheckHooks;h!==null&&Go(t,h)}else{let h=e.viewHooks;h!==null&&qo(t,h,2),Ma(t,2)}if(e.firstUpdatePass===!0&&(e.firstUpdatePass=!1),t[Lo]){for(let h of t[Lo])h();t[Lo]=null}i||(of(t),t[_]&=-73)}catch(f){throw i||Xn(t),f}finally{l!==null&&(cs(l,c),a&&zm(l)),Bo()}}function uf(e,t){for(let n=xd(e);n!==null;n=Rd(n))for(let r=de;r<n.length;r++){let o=n[r];df(o,t)}}function Km(e){for(let t=xd(e);t!==null;t=Rd(t)){if(!(t[_]&2))continue;let n=t[Kn];for(let r=0;r<n.length;r++){let o=n[r];ua(o)}}}function Jm(e,t,n){B(18);let r=Le(t,e);df(r,n),B(19,r[ge])}function df(e,t){jo(e)&&$a(e,t)}function $a(e,t){let r=e[N],o=e[_],i=e[me],s=!!(t===0&&o&16);if(s||=!!(o&64&&t===0),s||=!!(o&1024),s||=!!(i?.dirty&&eo(i)),s||=!1,i&&(i.dirty=!1),e[_]&=-9217,s)Ym(r,e,r.template,e[ge]);else if(o&8192){let a=j(null);try{cf(e),uf(e,1);let c=r.components;c!==null&&ff(e,c,1),of(e)}finally{j(a)}}}function ff(e,t,n){for(let r=0;r<t.length;r++)Jm(e,t[r],n)}function Xm(e,t){let n=e.hostBindingOpCodes;if(n!==null)try{for(let r=0;r<n.length;r++){let o=n[r];if(o<0)dt(~o);else{let i=o,s=n[++r],a=n[++r];Tu(s,i);let c=t[i];B(24,c),a(2,c),B(25,c)}}}finally{dt(-1)}}function pf(e,t){let n=ga()?64:1088;for(e[Qe].changeDetectionScheduler?.notify(t);e;){e[_]|=n;let r=at(e);if(cn(e)&&!r)return e;e=r}return null}function ev(e,t,n,r){return[e,!0,0,t,null,r,null,n,null,null]}function tv(e,t,n,r=!0){let o=t[N];if(nv(o,t,e,n),r){let s=Ua(n,e),a=t[ie],c=a.parentNode(e[lt]);c!==null&&gm(o,e[be],a,t,c,s)}let i=t[qn];i!==null&&i.firstChild!==null&&(i.firstChild=null)}function Ba(e,t){if(e.length<=de)return;let n=de+t,r=e[n];if(r){let o=r[sn];o!==null&&o!==e&&ic(o,r),t>0&&(e[n-1][Se]=r[Se]);let i=Bn(e,de+t);hm(r[N],r);let s=i[Wn];s!==null&&s.detachView(i[N]),r[re]=null,r[Se]=null,r[_]&=-129}return r}function nv(e,t,n,r){let o=de+r,i=n.length;r>0&&(n[o-1][Se]=t),r<i-de?(t[Se]=n[o],Qs(n,de+r,t)):(n.push(t),t[Se]=null),t[re]=n;let s=t[sn];s!==null&&n!==s&&hf(s,t);let a=t[Wn];a!==null&&a.insertView(e),Vo(t),t[_]|=128}function hf(e,t){let n=e[Kn],r=t[re];if(Pe(r))e[_]|=2;else{let o=r[re][Ce];t[Ce]!==o&&(e[_]|=2)}n===null?e[Kn]=[t]:n.push(t)}var At=class{_lView;_cdRefInjectingView;_appRef=null;_attachedToViewContainer=!1;exhaustive;get rootNodes(){let t=this._lView,n=t[N];return ur(n,t,n.firstChild,[])}constructor(t,n){this._lView=t,this._cdRefInjectingView=n}get context(){return this._lView[ge]}set context(t){this._lView[ge]=t}get destroyed(){return Nt(this._lView)}destroy(){if(this._appRef)this._appRef.detachView(this);else if(this._attachedToViewContainer){let t=this._lView[re];if(_e(t)){let n=t[Yn],r=n?n.indexOf(this):-1;r>-1&&(Ba(t,r),Bn(n,r))}this._attachedToViewContainer=!1}Yd(this._lView[N],this._lView)}onDestroy(t){da(this._lView,t)}markForCheck(){pf(this._cdRefInjectingView||this._lView,4)}detach(){this._lView[_]&=-129}reattach(){Vo(this._lView),this._lView[_]|=128}detectChanges(){this._lView[_]|=1024,ac(this._lView)}checkNoChanges(){return;try{this.exhaustive??=this._lView[nn].get(Um,Wu)}catch{this.exhaustive=Wu}}attachToViewContainerRef(){if(this._appRef)throw new I(902,!1);this._attachedToViewContainer=!0}detachFromAppRef(){this._appRef=null;let t=cn(this._lView),n=this._lView[sn];n!==null&&!t&&ic(n,this._lView),Qd(this._lView[N],this._lView)}attachToAppRef(t){if(this._attachedToViewContainer)throw new I(902,!1);this._appRef=t;let n=cn(this._lView),r=this._lView[sn];r!==null&&!n&&hf(r,this._lView),Vo(this._lView)}};function gf(e,t,n,r,o){let i=e.data[t];if(i===null)i=rv(e,t,n,r,o),Du()&&(i.flags|=32);else if(i.type&64){i.type=n,i.value=r,i.attrs=o;let s=wu();i.injectorIndex=s===null?-1:s.injectorIndex}return tr(i,!0),i}function rv(e,t,n,r,o){let i=fa(),s=pa(),a=s?i:i&&i.parent,c=e.data[t]=iv(e,a,n,t,r,o);return ov(e,c,i,s),c}function ov(e,t,n,r){e.firstChild===null&&(e.firstChild=t),n!==null&&(r?n.child==null&&t.parent!==null&&(n.child=t):n.next===null&&(n.next=t,t.prev=n))}function iv(e,t,n,r,o,i){let s=t?t.injectorIndex:-1,a=0;return yu()&&(a|=128),{type:n,index:r,insertBeforeIndex:null,injectorIndex:s,directiveStart:-1,directiveEnd:-1,directiveStylingLast:-1,componentOffset:-1,propertyBindings:null,flags:a,providerIndexes:0,value:o,attrs:i,mergedAttrs:null,localNames:null,initialInputs:null,inputs:null,hostDirectiveInputs:null,outputs:null,hostDirectiveOutputs:null,directiveToIndex:null,tView:null,next:null,prev:null,projectionNext:null,child:null,parent:t,projection:null,styles:null,stylesWithoutHost:null,residualStyles:void 0,classes:null,classesWithoutHost:null,residualClasses:void 0,classBindings:0,styleBindings:0}}var cM=new RegExp(`^(\\d+)*(${qg}|${Gg})*(.*)`);var sv=()=>null;function Zu(e,t){return sv(e,t)}var mf=class{},vi=class{},za=class{resolveComponentFactory(t){throw new I(917,!1)}},hr=class{static NULL=new za},Ot=class{},cc=(()=>{class e{destroyNode=null;static __NG_ELEMENT_ID__=()=>av()}return e})();function av(){let e=fe(),t=Fe(),n=Le(t.index,e);return(Pe(n)?n:e)[ie]}var vf=(()=>{class e{static \u0275prov=b({token:e,providedIn:"root",factory:()=>null})}return e})();var Wo={},Ga=class{injector;parentInjector;constructor(t,n){this.injector=t,this.parentInjector=n}get(t,n,r){let o=this.injector.get(t,Wo,r);return o!==Wo||n===Wo?o:this.parentInjector.get(t,n,r)}};function Qu(e,t,n){let r=n?e.styles:null,o=n?e.classes:null,i=0;if(t!==null)for(let s=0;s<t.length;s++){let a=t[s];if(typeof a=="number")i=a;else if(i==1)o=Fs(o,a);else if(i==2){let c=a,l=t[++s];r=Fs(r,c+": "+l+";")}}n?e.styles=r:e.stylesWithoutHost=r,n?e.classes=o:e.classesWithoutHost=o}function yf(e,t=0){let n=fe();if(n===null)return M(e,t);let r=Fe();return Dd(r,n,ue(e),t)}function cv(e,t,n,r,o){let i=r===null?null:{"":-1},s=o(e,n);if(s!==null){let a=s,c=null,l=null;for(let f of s)if(f.resolveHostDirectives!==null){[a,c,l]=f.resolveHostDirectives(s);break}dv(e,t,n,a,i,c,l)}i!==null&&r!==null&&lv(n,r,i)}function lv(e,t,n){let r=e.localNames=[];for(let o=0;o<t.length;o+=2){let i=n[t[o+1]];if(i==null)throw new I(-301,!1);r.push(t[o],i)}}function uv(e,t,n){t.componentOffset=n,(e.components??=[]).push(t.index)}function dv(e,t,n,r,o,i,s){let a=r.length,c=!1;for(let y=0;y<a;y++){let h=r[y];!c&&_t(h)&&(c=!0,uv(e,n,y)),kg(wd(n,t),e,h.type)}vv(n,e.data.length,a);for(let y=0;y<a;y++){let h=r[y];h.providersResolver&&h.providersResolver(h)}let l=!1,f=!1,p=Wd(e,t,a,null);a>0&&(n.directiveToIndex=new Map);for(let y=0;y<a;y++){let h=r[y];if(n.mergedAttrs=vd(n.mergedAttrs,h.hostAttrs),pv(e,n,t,p,h),mv(p,h,o),s!==null&&s.has(h)){let[O,$]=s.get(h);n.directiveToIndex.set(h.type,[p,O+n.directiveStart,$+n.directiveStart])}else(i===null||!i.has(h))&&n.directiveToIndex.set(h.type,p);h.contentQueries!==null&&(n.flags|=4),(h.hostBindings!==null||h.hostAttrs!==null||h.hostVars!==0)&&(n.flags|=64);let S=h.type.prototype;!l&&(S.ngOnChanges||S.ngOnInit||S.ngDoCheck)&&((e.preOrderHooks??=[]).push(n.index),l=!0),!f&&(S.ngOnChanges||S.ngDoCheck)&&((e.preOrderCheckHooks??=[]).push(n.index),f=!0),p++}fv(e,n,i)}function fv(e,t,n){for(let r=t.directiveStart;r<t.directiveEnd;r++){let o=e.data[r];if(n===null||!n.has(o))Yu(0,t,o,r),Yu(1,t,o,r),Ju(t,r,!1);else{let i=n.get(o);Ku(0,t,i,r),Ku(1,t,i,r),Ju(t,r,!0)}}}function Yu(e,t,n,r){let o=e===0?n.inputs:n.outputs;for(let i in o)if(o.hasOwnProperty(i)){let s;e===0?s=t.inputs??={}:s=t.outputs??={},s[i]??=[],s[i].push(r),Ef(t,i)}}function Ku(e,t,n,r){let o=e===0?n.inputs:n.outputs;for(let i in o)if(o.hasOwnProperty(i)){let s=o[i],a;e===0?a=t.hostDirectiveInputs??={}:a=t.hostDirectiveOutputs??={},a[s]??=[],a[s].push(r,i),Ef(t,s)}}function Ef(e,t){t==="class"?e.flags|=8:t==="style"&&(e.flags|=16)}function Ju(e,t,n){let{attrs:r,inputs:o,hostDirectiveInputs:i}=e;if(r===null||!n&&o===null||n&&i===null||rc(e)){e.initialInputs??=[],e.initialInputs.push(null);return}let s=null,a=0;for(;a<r.length;){let c=r[a];if(c===0){a+=4;continue}else if(c===5){a+=2;continue}else if(typeof c=="number")break;if(!n&&o.hasOwnProperty(c)){let l=o[c];for(let f of l)if(f===t){s??=[],s.push(c,r[a+1]);break}}else if(n&&i.hasOwnProperty(c)){let l=i[c];for(let f=0;f<l.length;f+=2)if(l[f]===t){s??=[],s.push(l[f+1],r[a+1]);break}}a+=2}e.initialInputs??=[],e.initialInputs.push(s)}function pv(e,t,n,r,o){e.data[r]=o;let i=o.factory||(o.factory=wt(o.type,!0)),s=new cr(i,_t(o),yf,null);e.blueprint[r]=s,n[r]=s,hv(e,t,r,Wd(e,n,o.hostVars,gn),o)}function hv(e,t,n,r,o){let i=o.hostBindings;if(i){let s=e.hostBindingOpCodes;s===null&&(s=e.hostBindingOpCodes=[]);let a=~t.index;gv(s)!=a&&s.push(a),s.push(n,r,i)}}function gv(e){let t=e.length;for(;t>0;){let n=e[--t];if(typeof n=="number"&&n<0)return n}return 0}function mv(e,t,n){if(n){if(t.exportAs)for(let r=0;r<t.exportAs.length;r++)n[t.exportAs[r]]=e;_t(t)&&(n[""]=e)}}function vv(e,t,n){e.flags|=1,e.directiveStart=t,e.directiveEnd=t+n,e.providerIndexes=t}function If(e,t,n,r,o,i,s,a){let c=t[N],l=c.consts,f=ca(l,s),p=gf(c,e,n,r,f);return i&&cv(c,t,p,ca(l,a),o),p.mergedAttrs=vd(p.mergedAttrs,p.attrs),p.attrs!==null&&Qu(p,p.attrs,!1),p.mergedAttrs!==null&&Qu(p,p.mergedAttrs,!0),c.queries!==null&&c.queries.elementStart(c,p),p}function wf(e,t){bg(e,t),oa(t)&&e.queries.elementEnd(t)}function yv(e,t,n){if(n===gn)return!1;let r=e[t];return Object.is(r,n)?!1:(e[t]=n,!0)}var qa=Symbol("BINDING");var Jo=class extends hr{ngModule;constructor(t){super(),this.ngModule=t}resolveComponentFactory(t){let n=ct(t);return new dn(n,this.ngModule)}};function Ev(e){return Object.keys(e).map(t=>{let[n,r,o]=e[t],i={propName:n,templateName:t,isSignal:(r&gi.SignalBased)!==0};return o&&(i.transform=o),i})}function Iv(e){return Object.keys(e).map(t=>({propName:e[t],templateName:t}))}function wv(e,t,n){let r=t instanceof ne?t:t?.injector;return r&&e.getStandaloneInjector!==null&&(r=e.getStandaloneInjector(r)||r),r?new Ga(n,r):n}function Sv(e){let t=e.get(Ot,null);if(t===null)throw new I(407,!1);let n=e.get(vf,null),r=e.get(bt,null);return{rendererFactory:t,sanitizer:n,changeDetectionScheduler:r,ngReflect:!1}}function bv(e,t){let n=(e.selectors[0][0]||"div").toLowerCase();return Ud(t,n,n==="svg"?sa:n==="math"?uu:null)}var dn=class extends vi{componentDef;ngModule;selector;componentType;ngContentSelectors;isBoundToModule;cachedInputs=null;cachedOutputs=null;get inputs(){return this.cachedInputs??=Ev(this.componentDef.inputs),this.cachedInputs}get outputs(){return this.cachedOutputs??=Iv(this.componentDef.outputs),this.cachedOutputs}constructor(t,n){super(),this.componentDef=t,this.ngModule=n,this.componentType=t.type,this.selector=rm(t.selectors),this.ngContentSelectors=t.ngContentSelectors??[],this.isBoundToModule=!!n}create(t,n,r,o,i,s){B(22);let a=j(null);try{let c=this.componentDef,l=Cv(r,c,s,i),f=wv(c,o||this.ngModule,t),p=Sv(f),y=p.rendererFactory.createRenderer(null,c),h=r?_m(y,r,c.encapsulation,f):bv(c,y),S=s?.some(Xu)||i?.some(U=>typeof U!="function"&&U.bindings.some(Xu)),O=Gd(null,l,null,512|qd(c),null,null,p,y,f,null,Ld(h,f,!0));O[Me]=h,$o(O);let $=null;try{let U=If(Me,O,2,"#host",()=>l.directiveRegistry,!0,0);h&&(Bd(y,h,U),lr(h,O)),ef(l,O,U),jd(l,U,O),wf(l,U),n!==void 0&&Tv(U,this.ngContentSelectors,n),$=Le(U.index,O),O[ge]=$[ge],nf(l,O,null)}catch(U){throw $!==null&&La($),La(O),U}finally{B(23),Bo()}return new Xo(this.componentType,O,!!S)}finally{j(a)}}};function Cv(e,t,n,r){let o=e?["ng-version","20.1.1"]:om(t.selectors[0]),i=null,s=null,a=0;if(n)for(let f of n)a+=f[qa].requiredVars,f.create&&(f.targetIdx=0,(i??=[]).push(f)),f.update&&(f.targetIdx=0,(s??=[]).push(f));if(r)for(let f=0;f<r.length;f++){let p=r[f];if(typeof p!="function")for(let y of p.bindings){a+=y[qa].requiredVars;let h=f+1;y.create&&(y.targetIdx=h,(i??=[]).push(y)),y.update&&(y.targetIdx=h,(s??=[]).push(y))}}let c=[t];if(r)for(let f of r){let p=typeof f=="function"?f:f.type,y=Xs(p);c.push(y)}return zd(0,null,Dv(i,s),1,a,c,null,null,null,[o],null)}function Dv(e,t){return!e&&!t?null:n=>{if(n&1&&e)for(let r of e)r.create();if(n&2&&t)for(let r of t)r.update()}}function Xu(e){let t=e[qa].kind;return t==="input"||t==="twoWay"}var Xo=class extends mf{_rootLView;_hasInputBindings;instance;hostView;changeDetectorRef;componentType;location;previousInputValues=null;_tNode;constructor(t,n,r){super(),this._rootLView=n,this._hasInputBindings=r,this._tNode=aa(n[N],Me),this.location=Xa(this._tNode,n),this.instance=Le(this._tNode.index,n)[ge],this.hostView=this.changeDetectorRef=new At(n,void 0),this.componentType=t}setInput(t,n){this._hasInputBindings;let r=this._tNode;if(this.previousInputValues??=new Map,this.previousInputValues.has(t)&&Object.is(this.previousInputValues.get(t),n))return;let o=this._rootLView,i=tf(r,o[N],o,t,n);this.previousInputValues.set(t,n);let s=Le(r.index,o);pf(s,1)}get injector(){return new Rt(this._tNode,this._rootLView)}destroy(){this.hostView.destroy()}onDestroy(t){this.hostView.onDestroy(t)}};function Tv(e,t,n){let r=e.projection=[];for(let o=0;o<t.length;o++){let i=n[o];r.push(i!=null&&i.length?Array.from(i):null)}}var yi=(()=>{class e{static __NG_ELEMENT_ID__=Mv}return e})();function Mv(){let e=Fe();return Nv(e,fe())}var _v=yi,Sf=class extends _v{_lContainer;_hostTNode;_hostLView;constructor(t,n,r){super(),this._lContainer=t,this._hostTNode=n,this._hostLView=r}get element(){return Xa(this._hostTNode,this._hostLView)}get injector(){return new Rt(this._hostTNode,this._hostLView)}get parentInjector(){let t=Ja(this._hostTNode,this._hostLView);if(yd(t)){let n=Yo(t,this._hostLView),r=Qo(t),o=n[N].data[r+8];return new Rt(o,n)}else return new Rt(null,this._hostLView)}clear(){for(;this.length>0;)this.remove(this.length-1)}get(t){let n=ed(this._lContainer);return n!==null&&n[t]||null}get length(){return this._lContainer.length-de}createEmbeddedView(t,n,r){let o,i;typeof r=="number"?o=r:r!=null&&(o=r.index,i=r.injector);let s=Zu(this._lContainer,t.ssrId),a=t.createEmbeddedViewImpl(n||{},i,s);return this.insertImpl(a,o,qu(this._hostTNode,s)),a}createComponent(t,n,r,o,i,s,a){let c=t&&!yg(t),l;if(c)l=n;else{let $=n||{};l=$.index,r=$.injector,o=$.projectableNodes,i=$.environmentInjector||$.ngModuleRef,s=$.directives,a=$.bindings}let f=c?t:new dn(ct(t)),p=r||this.parentInjector;if(!i&&f.ngModule==null){let U=(c?p:this.parentInjector).get(ne,null);U&&(i=U)}let y=ct(f.componentType??{}),h=Zu(this._lContainer,y?.id??null),S=h?.firstChild??null,O=f.create(p,o,S,i,s,a);return this.insertImpl(O.hostView,l,qu(this._hostTNode,h)),O}insert(t,n){return this.insertImpl(t,n,!0)}insertImpl(t,n,r){let o=t._lView;if(fu(o)){let a=this.indexOf(t);if(a!==-1)this.detach(a);else{let c=o[re],l=new Sf(c,c[be],c[re]);l.detach(l.indexOf(t))}}let i=this._adjustIndex(n),s=this._lContainer;return tv(s,o,i,r),t.attachToViewContainerRef(),Qs(xa(s),i,t),t}move(t,n){return this.insert(t,n)}indexOf(t){let n=ed(this._lContainer);return n!==null?n.indexOf(t):-1}remove(t){let n=this._adjustIndex(t,-1),r=Ba(this._lContainer,n);r&&(Bn(xa(this._lContainer),n),Yd(r[N],r))}detach(t){let n=this._adjustIndex(t,-1),r=Ba(this._lContainer,n);return r&&Bn(xa(this._lContainer),n)!=null?new At(r):null}_adjustIndex(t,n=0){return t??this.length+n}};function ed(e){return e[Yn]}function xa(e){return e[Yn]||(e[Yn]=[])}function Nv(e,t){let n,r=t[e.index];return _e(r)?n=r:(n=ev(r,t,null,e),t[e.index]=n,Zd(t,n)),Rv(n,t,e,r),new Sf(n,e,t)}function xv(e,t){let n=e[ie],r=n.createComment(""),o=ut(t,e),i=n.parentNode(o);return Ko(n,i,r,n.nextSibling(o),!1),r}var Rv=Av;function Av(e,t,n,r){if(e[lt])return;let o;n.type&8?o=Ne(r):o=xv(t,n),e[lt]=o}var td=new Set;function mn(e){td.has(e)||(td.add(e),performance?.mark?.("mark_feature_usage",{detail:{feature:e}}))}var fn=class{},Ei=class{};var ei=class extends fn{ngModuleType;_parent;_bootstrapComponents=[];_r3Injector;instance;destroyCbs=[];componentFactoryResolver=new Jo(this);constructor(t,n,r,o=!0){super(),this.ngModuleType=t,this._parent=n;let i=Js(t);this._bootstrapComponents=Vd(i.bootstrap),this._r3Injector=Sa(t,n,[{provide:fn,useValue:this},{provide:hr,useValue:this.componentFactoryResolver},...r],We(t),new Set(["environment"])),o&&this.resolveInjectorInitializers()}resolveInjectorInitializers(){this._r3Injector.resolveInjectorInitializers(),this.instance=this._r3Injector.get(this.ngModuleType)}get injector(){return this._r3Injector}destroy(){let t=this._r3Injector;!t.destroyed&&t.destroy(),this.destroyCbs.forEach(n=>n()),this.destroyCbs=null}onDestroy(t){this.destroyCbs.push(t)}},ti=class extends Ei{moduleType;constructor(t){super(),this.moduleType=t}create(t){return new ei(this.moduleType,t,[])}};var dr=class extends fn{injector;componentFactoryResolver=new Jo(this);instance=null;constructor(t){super();let n=new St([...t.providers,{provide:fn,useValue:this},{provide:hr,useValue:this.componentFactoryResolver}],t.parent||Gn(),t.debugName,new Set(["environment"]));this.injector=n,t.runEnvironmentInitializers&&n.resolveInjectorInitializers()}destroy(){this.injector.destroy()}onDestroy(t){this.injector.onDestroy(t)}};function gr(e,t,n=null){return new dr({providers:e,parent:t,debugName:n,runEnvironmentInitializers:!0}).injector}var Ov=(()=>{class e{_injector;cachedInjectors=new Map;constructor(n){this._injector=n}getOrCreateStandaloneInjector(n){if(!n.standalone)return null;if(!this.cachedInjectors.has(n)){let r=ea(!1,n.type),o=r.length>0?gr([r],this._injector,`Standalone[${n.type.name}]`):null;this.cachedInjectors.set(n,o)}return this.cachedInjectors.get(n)}ngOnDestroy(){try{for(let n of this.cachedInjectors.values())n!==null&&n.destroy()}finally{this.cachedInjectors.clear()}}static \u0275prov=b({token:e,providedIn:"environment",factory:()=>new e(M(ne))})}return e})();function Pt(e){return si(()=>{let t=bf(e),n=z(E({},t),{decls:e.decls,vars:e.vars,template:e.template,consts:e.consts||null,ngContentSelectors:e.ngContentSelectors,onPush:e.changeDetection===ec.OnPush,directiveDefs:null,pipeDefs:null,dependencies:t.standalone&&e.dependencies||null,getStandaloneInjector:t.standalone?o=>o.get(Ov).getOrCreateStandaloneInjector(n):null,getExternalStyles:null,signals:e.signals??!1,data:e.data||{},encapsulation:e.encapsulation||Ke.Emulated,styles:e.styles||st,_:null,schemas:e.schemas||null,tView:null,id:""});t.standalone&&mn("NgStandalone"),Cf(n);let r=e.dependencies;return n.directiveDefs=nd(r,kv),n.pipeDefs=nd(r,nu),n.id=Fv(n),n})}function kv(e){return ct(e)||Xs(e)}function Pv(e,t){if(e==null)return Dt;let n={};for(let r in e)if(e.hasOwnProperty(r)){let o=e[r],i,s,a,c;Array.isArray(o)?(a=o[0],i=o[1],s=o[2]??i,c=o[3]||null):(i=o,s=o,a=gi.None,c=null),n[i]=[r,a,c],t[i]=s}return n}function Lv(e){if(e==null)return Dt;let t={};for(let n in e)e.hasOwnProperty(n)&&(t[e[n]]=n);return t}function mr(e){return si(()=>{let t=bf(e);return Cf(t),t})}function bf(e){let t={};return{type:e.type,providersResolver:null,factory:null,hostBindings:e.hostBindings||null,hostVars:e.hostVars||0,hostAttrs:e.hostAttrs||null,contentQueries:e.contentQueries||null,declaredInputs:t,inputConfig:e.inputs||Dt,exportAs:e.exportAs||null,standalone:e.standalone??!0,signals:e.signals===!0,selectors:e.selectors||st,viewQuery:e.viewQuery||null,features:e.features||null,setInput:null,resolveHostDirectives:null,hostDirectives:null,inputs:Pv(e.inputs,t),outputs:Lv(e.outputs),debugInfo:null}}function Cf(e){e.features?.forEach(t=>t(e))}function nd(e,t){return e?()=>{let n=typeof e=="function"?e():e,r=[];for(let o of n){let i=t(o);i!==null&&r.push(i)}return r}:null}function Fv(e){let t=0,n=typeof e.consts=="function"?"":e.consts,r=[e.selectors,e.ngContentSelectors,e.hostVars,e.hostAttrs,n,e.vars,e.decls,e.encapsulation,e.standalone,e.signals,e.exportAs,JSON.stringify(e.inputs),JSON.stringify(e.outputs),Object.getOwnPropertyNames(e.type.prototype),!!e.contentQueries,!!e.viewQuery];for(let i of r.join("|"))t=Math.imul(31,t)+i.charCodeAt(0)<<0;return t+=2147483648,"c"+t}var lc=function(e){return e[e.CHANGE_DETECTION=0]="CHANGE_DETECTION",e[e.AFTER_NEXT_RENDER=1]="AFTER_NEXT_RENDER",e}(lc||{}),vr=new w(""),Df=!1,Wa=class extends ee{__isAsync;destroyRef=void 0;pendingTasks=void 0;constructor(t=!1){super(),this.__isAsync=t,cu()&&(this.destroyRef=v(xt,{optional:!0})??void 0,this.pendingTasks=v(Ye,{optional:!0})??void 0)}emit(t){let n=j(null);try{super.next(t)}finally{j(n)}}subscribe(t,n,r){let o=t,i=n||(()=>null),s=r;if(t&&typeof t=="object"){let c=t;o=c.next?.bind(c),i=c.error?.bind(c),s=c.complete?.bind(c)}this.__isAsync&&(i=this.wrapInTimeout(i),o&&(o=this.wrapInTimeout(o)),s&&(s=this.wrapInTimeout(s)));let a=super.subscribe({next:o,error:i,complete:s});return t instanceof G&&t.add(a),a}wrapInTimeout(t){return n=>{let r=this.pendingTasks?.add();setTimeout(()=>{try{t(n)}finally{r!==void 0&&this.pendingTasks?.remove(r)}})}}},se=Wa;function Tf(e){let t,n;function r(){e=ir;try{n!==void 0&&typeof cancelAnimationFrame=="function"&&cancelAnimationFrame(n),t!==void 0&&clearTimeout(t)}catch{}}return t=setTimeout(()=>{e(),r()}),typeof requestAnimationFrame=="function"&&(n=requestAnimationFrame(()=>{e(),r()})),()=>r()}function rd(e){return queueMicrotask(()=>e()),()=>{e=ir}}var uc="isAngularZone",ni=uc+"_ID",jv=0,Y=class e{hasPendingMacrotasks=!1;hasPendingMicrotasks=!1;isStable=!0;onUnstable=new se(!1);onMicrotaskEmpty=new se(!1);onStable=new se(!1);onError=new se(!1);constructor(t){let{enableLongStackTrace:n=!1,shouldCoalesceEventChangeDetection:r=!1,shouldCoalesceRunChangeDetection:o=!1,scheduleInRootZone:i=Df}=t;if(typeof Zone>"u")throw new I(908,!1);Zone.assertZonePatched();let s=this;s._nesting=0,s._outer=s._inner=Zone.current,Zone.TaskTrackingZoneSpec&&(s._inner=s._inner.fork(new Zone.TaskTrackingZoneSpec)),n&&Zone.longStackTraceZoneSpec&&(s._inner=s._inner.fork(Zone.longStackTraceZoneSpec)),s.shouldCoalesceEventChangeDetection=!o&&r,s.shouldCoalesceRunChangeDetection=o,s.callbackScheduled=!1,s.scheduleInRootZone=i,Uv(s)}static isInAngularZone(){return typeof Zone<"u"&&Zone.current.get(uc)===!0}static assertInAngularZone(){if(!e.isInAngularZone())throw new I(909,!1)}static assertNotInAngularZone(){if(e.isInAngularZone())throw new I(909,!1)}run(t,n,r){return this._inner.run(t,n,r)}runTask(t,n,r,o){let i=this._inner,s=i.scheduleEventTask("NgZoneEvent: "+o,t,Vv,ir,ir);try{return i.runTask(s,n,r)}finally{i.cancelTask(s)}}runGuarded(t,n,r){return this._inner.runGuarded(t,n,r)}runOutsideAngular(t){return this._outer.run(t)}},Vv={};function dc(e){if(e._nesting==0&&!e.hasPendingMicrotasks&&!e.isStable)try{e._nesting++,e.onMicrotaskEmpty.emit(null)}finally{if(e._nesting--,!e.hasPendingMicrotasks)try{e.runOutsideAngular(()=>e.onStable.emit(null))}finally{e.isStable=!0}}}function Hv(e){if(e.isCheckStableRunning||e.callbackScheduled)return;e.callbackScheduled=!0;function t(){Tf(()=>{e.callbackScheduled=!1,Za(e),e.isCheckStableRunning=!0,dc(e),e.isCheckStableRunning=!1})}e.scheduleInRootZone?Zone.root.run(()=>{t()}):e._outer.run(()=>{t()}),Za(e)}function Uv(e){let t=()=>{Hv(e)},n=jv++;e._inner=e._inner.fork({name:"angular",properties:{[uc]:!0,[ni]:n,[ni+n]:!0},onInvokeTask:(r,o,i,s,a,c)=>{if($v(c))return r.invokeTask(i,s,a,c);try{return od(e),r.invokeTask(i,s,a,c)}finally{(e.shouldCoalesceEventChangeDetection&&s.type==="eventTask"||e.shouldCoalesceRunChangeDetection)&&t(),id(e)}},onInvoke:(r,o,i,s,a,c,l)=>{try{return od(e),r.invoke(i,s,a,c,l)}finally{e.shouldCoalesceRunChangeDetection&&!e.callbackScheduled&&!Bv(c)&&t(),id(e)}},onHasTask:(r,o,i,s)=>{r.hasTask(i,s),o===i&&(s.change=="microTask"?(e._hasPendingMicrotasks=s.microTask,Za(e),dc(e)):s.change=="macroTask"&&(e.hasPendingMacrotasks=s.macroTask))},onHandleError:(r,o,i,s)=>(r.handleError(i,s),e.runOutsideAngular(()=>e.onError.emit(s)),!1)})}function Za(e){e._hasPendingMicrotasks||(e.shouldCoalesceEventChangeDetection||e.shouldCoalesceRunChangeDetection)&&e.callbackScheduled===!0?e.hasPendingMicrotasks=!0:e.hasPendingMicrotasks=!1}function od(e){e._nesting++,e.isStable&&(e.isStable=!1,e.onUnstable.emit(null))}function id(e){e._nesting--,dc(e)}var ri=class{hasPendingMicrotasks=!1;hasPendingMacrotasks=!1;isStable=!0;onUnstable=new se;onMicrotaskEmpty=new se;onStable=new se;onError=new se;run(t,n,r){return t.apply(n,r)}runGuarded(t,n,r){return t.apply(n,r)}runOutsideAngular(t){return t()}runTask(t,n,r,o){return t.apply(n,r)}};function $v(e){return Mf(e,"__ignore_ng_zone__")}function Bv(e){return Mf(e,"__scheduler_tick__")}function Mf(e,t){return!Array.isArray(e)||e.length!==1?!1:e[0]?.data?.[t]===!0}var _f=(()=>{class e{impl=null;execute(){this.impl?.execute()}static \u0275prov=b({token:e,providedIn:"root",factory:()=>new e})}return e})();var fc=(()=>{class e{log(n){console.log(n)}warn(n){console.warn(n)}static \u0275fac=function(r){return new(r||e)};static \u0275prov=b({token:e,factory:e.\u0275fac,providedIn:"platform"})}return e})();var pc=new w("");function yr(e){return!!e&&typeof e.then=="function"}function Nf(e){return!!e&&typeof e.subscribe=="function"}var xf=new w("");var hc=(()=>{class e{resolve;reject;initialized=!1;done=!1;donePromise=new Promise((n,r)=>{this.resolve=n,this.reject=r});appInits=v(xf,{optional:!0})??[];injector=v(we);constructor(){}runInitializers(){if(this.initialized)return;let n=[];for(let o of this.appInits){let i=oe(this.injector,o);if(yr(i))n.push(i);else if(Nf(i)){let s=new Promise((a,c)=>{i.subscribe({complete:a,error:c})});n.push(s)}}let r=()=>{this.done=!0,this.resolve()};Promise.all(n).then(()=>{r()}).catch(o=>{this.reject(o)}),n.length===0&&r(),this.initialized=!0}static \u0275fac=function(r){return new(r||e)};static \u0275prov=b({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),Ii=new w("");function Rf(){us(()=>{let e="";throw new I(600,e)})}function Af(e){return e.isBoundToModule}var zv=10;var Lt=(()=>{class e{_runningTick=!1;_destroyed=!1;_destroyListeners=[];_views=[];internalErrorHandler=v(xe);afterRenderManager=v(_f);zonelessEnabled=v(or);rootEffectScheduler=v(Ta);dirtyFlags=0;tracingSnapshot=null;allTestViews=new Set;autoDetectTestViews=new Set;includeAllTestViews=!1;afterTick=new ee;get allViews(){return[...(this.includeAllTestViews?this.allTestViews:this.autoDetectTestViews).keys(),...this._views]}get destroyed(){return this._destroyed}componentTypes=[];components=[];internalPendingTask=v(Ye);get isStable(){return this.internalPendingTask.hasPendingTasksObservable.pipe(A(n=>!n))}constructor(){v(vr,{optional:!0})}whenStable(){let n;return new Promise(r=>{n=this.isStable.subscribe({next:o=>{o&&r()}})}).finally(()=>{n.unsubscribe()})}_injector=v(ne);_rendererFactory=null;get injector(){return this._injector}bootstrap(n,r){return this.bootstrapImpl(n,r)}bootstrapImpl(n,r,o=we.NULL){return this._injector.get(Y).run(()=>{B(10);let s=n instanceof vi;if(!this._injector.get(hc).done){let S="";throw new I(405,S)}let c;s?c=n:c=this._injector.get(hr).resolveComponentFactory(n),this.componentTypes.push(c.componentType);let l=Af(c)?void 0:this._injector.get(fn),f=r||c.selector,p=c.create(o,[],f,l),y=p.location.nativeElement,h=p.injector.get(pc,null);return h?.registerApplication(y),p.onDestroy(()=>{this.detachView(p.hostView),ar(this.components,p),h?.unregisterApplication(y)}),this._loadComponent(p),B(11,p),p})}tick(){this.zonelessEnabled||(this.dirtyFlags|=1),this._tick()}_tick(){B(12),this.tracingSnapshot!==null?this.tracingSnapshot.run(lc.CHANGE_DETECTION,this.tickImpl):this.tickImpl()}tickImpl=()=>{if(this._runningTick)throw new I(101,!1);let n=j(null);try{this._runningTick=!0,this.synchronize()}finally{this._runningTick=!1,this.tracingSnapshot?.dispose(),this.tracingSnapshot=null,j(n),this.afterTick.next(),B(13)}};synchronize(){this._rendererFactory===null&&!this._injector.destroyed&&(this._rendererFactory=this._injector.get(Ot,null,{optional:!0}));let n=0;for(;this.dirtyFlags!==0&&n++<zv;)B(14),this.synchronizeOnce(),B(15)}synchronizeOnce(){this.dirtyFlags&16&&(this.dirtyFlags&=-17,this.rootEffectScheduler.flush());let n=!1;if(this.dirtyFlags&7){let r=!!(this.dirtyFlags&1);this.dirtyFlags&=-8,this.dirtyFlags|=8;for(let{_lView:o}of this.allViews){if(!r&&!Jn(o))continue;let i=r&&!this.zonelessEnabled?0:1;ac(o,i),n=!0}if(this.dirtyFlags&=-5,this.syncDirtyFlagsWithViews(),this.dirtyFlags&23)return}n||(this._rendererFactory?.begin?.(),this._rendererFactory?.end?.()),this.dirtyFlags&8&&(this.dirtyFlags&=-9,this.afterRenderManager.execute()),this.syncDirtyFlagsWithViews()}syncDirtyFlagsWithViews(){if(this.allViews.some(({_lView:n})=>Jn(n))){this.dirtyFlags|=2;return}else this.dirtyFlags&=-8}attachView(n){let r=n;this._views.push(r),r.attachToAppRef(this)}detachView(n){let r=n;ar(this._views,r),r.detachFromAppRef()}_loadComponent(n){this.attachView(n.hostView);try{this.tick()}catch(o){this.internalErrorHandler(o)}this.components.push(n),this._injector.get(Ii,[]).forEach(o=>o(n))}ngOnDestroy(){if(!this._destroyed)try{this._destroyListeners.forEach(n=>n()),this._views.slice().forEach(n=>n.destroy())}finally{this._destroyed=!0,this._views=[],this._destroyListeners=[]}}onDestroy(n){return this._destroyListeners.push(n),()=>ar(this._destroyListeners,n)}destroy(){if(this._destroyed)throw new I(406,!1);let n=this._injector;n.destroy&&!n.destroyed&&n.destroy()}get viewCount(){return this._views.length}static \u0275fac=function(r){return new(r||e)};static \u0275prov=b({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function ar(e,t){let n=e.indexOf(t);n>-1&&e.splice(n,1)}function sd(e,t,n,r,o){tf(t,e,n,o?"class":"style",r)}function u(e,t,n,r){let o=fe(),i=o[N],s=e+Me,a=i.firstCreatePass?If(s,o,2,t,km,vu(),n,r):i.data[s];if(Lm(a,o,e,t,Gv),ia(a)){let c=o[N];ef(c,o,a),jd(c,a,o)}return r!=null&&Mm(o,a),u}function d(){let e=Ho(),t=Fe(),n=Fm(t);return e.firstCreatePass&&wf(e,n),Eu(n)&&Iu(),mu(),n.classesWithoutHost!=null&&Dg(n)&&sd(e,n,fe(),n.classesWithoutHost,!0),n.stylesWithoutHost!=null&&Tg(n)&&sd(e,n,fe(),n.stylesWithoutHost,!1),d}function m(e,t,n,r){return u(e,t,n,r),d(),m}var Gv=(e,t,n,r,o)=>(wa(!0),Ud(t[ie],r,Au()));var Er="en-US";var qv=Er;function Of(e){typeof e=="string"&&(qv=e.toLowerCase().replace(/_/g,"-"))}function zo(e,t){return e<<17|t<<2}function kt(e){return e>>17&32767}function Wv(e){return(e&2)==2}function Zv(e,t){return e&131071|t<<17}function Qa(e){return e|2}function pn(e){return(e&131068)>>2}function Ra(e,t){return e&-131069|t<<2}function Qv(e){return(e&1)===1}function Ya(e){return e|1}function Yv(e,t,n,r,o,i){let s=i?t.classBindings:t.styleBindings,a=kt(s),c=pn(s);e[r]=n;let l=!1,f;if(Array.isArray(n)){let p=n;f=p[1],(f===null||tn(p,f)>0)&&(l=!0)}else f=n;if(o)if(c!==0){let y=kt(e[a+1]);e[r+1]=zo(y,a),y!==0&&(e[y+1]=Ra(e[y+1],r)),e[a+1]=Zv(e[a+1],r)}else e[r+1]=zo(a,0),a!==0&&(e[a+1]=Ra(e[a+1],r)),a=r;else e[r+1]=zo(c,0),a===0?a=r:e[c+1]=Ra(e[c+1],r),c=r;l&&(e[r+1]=Qa(e[r+1])),ad(e,f,r,!0),ad(e,f,r,!1),Kv(t,f,e,r,i),s=zo(a,c),i?t.classBindings=s:t.styleBindings=s}function Kv(e,t,n,r,o){let i=o?e.residualClasses:e.residualStyles;i!=null&&typeof t=="string"&&tn(i,t)>=0&&(n[r+1]=Ya(n[r+1]))}function ad(e,t,n,r){let o=e[n+1],i=t===null,s=r?kt(o):pn(o),a=!1;for(;s!==0&&(a===!1||i);){let c=e[s],l=e[s+1];Jv(c,t)&&(a=!0,e[s+1]=r?Ya(l):Qa(l)),s=r?kt(l):pn(l)}a&&(e[n+1]=r?Qa(o):Ya(o))}function Jv(e,t){return e===null||t==null||(Array.isArray(e)?e[1]:e)===t?!0:Array.isArray(e)&&typeof t=="string"?tn(e,t)>=0:!1}function wi(e,t,n){return Xv(e,t,n,!1),wi}function Xv(e,t,n,r){let o=fe(),i=Ho(),s=Cu(2);if(i.firstUpdatePass&&ty(i,e,s,r),t!==gn&&yv(o,s,t)){let a=i.data[nr()];sy(i,a,o,o[ie],e,o[s+1]=ay(t,n),r,s)}}function ey(e,t){return t>=e.expandoStartIndex}function ty(e,t,n,r){let o=e.data;if(o[n+1]===null){let i=o[nr()],s=ey(e,n);cy(i,r)&&t===null&&!s&&(t=!1),t=ny(o,i,t,r),Yv(o,i,t,n,s,r)}}function ny(e,t,n,r){let o=_u(e),i=r?t.residualClasses:t.residualStyles;if(o===null)(r?t.classBindings:t.styleBindings)===0&&(n=Aa(null,e,t,n,r),n=fr(n,t.attrs,r),i=null);else{let s=t.directiveStylingLast;if(s===-1||e[s]!==o)if(n=Aa(o,e,t,n,r),i===null){let c=ry(e,t,r);c!==void 0&&Array.isArray(c)&&(c=Aa(null,e,t,c[1],r),c=fr(c,t.attrs,r),oy(e,t,r,c))}else i=iy(e,t,r)}return i!==void 0&&(r?t.residualClasses=i:t.residualStyles=i),n}function ry(e,t,n){let r=n?t.classBindings:t.styleBindings;if(pn(r)!==0)return e[kt(r)]}function oy(e,t,n,r){let o=n?t.classBindings:t.styleBindings;e[kt(o)]=r}function iy(e,t,n){let r,o=t.directiveEnd;for(let i=1+t.directiveStylingLast;i<o;i++){let s=e[i].hostAttrs;r=fr(r,s,n)}return fr(r,t.attrs,n)}function Aa(e,t,n,r,o){let i=null,s=n.directiveEnd,a=n.directiveStylingLast;for(a===-1?a=n.directiveStart:a++;a<s&&(i=t[a],r=fr(r,i.hostAttrs,o),i!==e);)a++;return e!==null&&(n.directiveStylingLast=a),r}function fr(e,t,n){let r=n?1:2,o=-1;if(t!==null)for(let i=0;i<t.length;i++){let s=t[i];typeof s=="number"?o=s:o===r&&(Array.isArray(e)||(e=e===void 0?[]:["",e]),tu(e,s,n?!0:t[++i]))}return e===void 0?null:e}function sy(e,t,n,r,o,i,s,a){if(!(t.type&3))return;let c=e.data,l=c[a+1],f=Qv(l)?cd(c,t,n,o,pn(l),s):void 0;if(!oi(f)){oi(i)||Wv(l)&&(i=cd(c,null,n,o,a,s));let p=du(nr(),n);Tm(r,s,p,o,i)}}function cd(e,t,n,r,o,i){let s=t===null,a;for(;o>0;){let c=e[o],l=Array.isArray(c),f=l?c[1]:c,p=f===null,y=n[o+1];y===gn&&(y=p?st:void 0);let h=p?ko(y,r):f===r?y:void 0;if(l&&!oi(h)&&(h=ko(c,r)),oi(h)&&(a=h,s))return a;let S=e[o+1];o=s?kt(S):pn(S)}if(t!==null){let c=i?t.residualClasses:t.residualStyles;c!=null&&(a=ko(c,r))}return a}function oi(e){return e!==void 0}function ay(e,t){return e==null||e===""||(typeof t=="string"?e=e+t:typeof e=="object"&&(e=We(hi(e)))),e}function cy(e,t){return(e.flags&(t?8:16))!==0}function g(e,t=""){let n=fe(),r=Ho(),o=e+Me,i=r.firstCreatePass?gf(r,o,1,t,null):r.data[o],s=ly(r,n,i,t,e);n[o]=s,Ia()&&Kd(r,n,s,i),tr(i,!1)}var ly=(e,t,n,r,o)=>(wa(!0),im(t[ie],r));var ii=class{ngModuleFactory;componentFactories;constructor(t,n){this.ngModuleFactory=t,this.componentFactories=n}},gc=(()=>{class e{compileModuleSync(n){return new ti(n)}compileModuleAsync(n){return Promise.resolve(this.compileModuleSync(n))}compileModuleAndAllComponentsSync(n){let r=this.compileModuleSync(n),o=Js(n),i=Vd(o.declarations).reduce((s,a)=>{let c=ct(a);return c&&s.push(new dn(c)),s},[]);return new ii(r,i)}compileModuleAndAllComponentsAsync(n){return Promise.resolve(this.compileModuleAndAllComponentsSync(n))}clearCache(){}clearCacheFor(n){}getModuleId(n){}static \u0275fac=function(r){return new(r||e)};static \u0275prov=b({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var uy=(()=>{class e{zone=v(Y);changeDetectionScheduler=v(bt);applicationRef=v(Lt);applicationErrorHandler=v(xe);_onMicrotaskEmptySubscription;initialize(){this._onMicrotaskEmptySubscription||(this._onMicrotaskEmptySubscription=this.zone.onMicrotaskEmpty.subscribe({next:()=>{this.changeDetectionScheduler.runningTick||this.zone.run(()=>{try{this.applicationRef.dirtyFlags|=1,this.applicationRef._tick()}catch(n){this.applicationErrorHandler(n)}})}}))}ngOnDestroy(){this._onMicrotaskEmptySubscription?.unsubscribe()}static \u0275fac=function(r){return new(r||e)};static \u0275prov=b({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),kf=new w("",{factory:()=>!1});function mc({ngZoneFactory:e,ignoreChangesOutsideZone:t,scheduleInRootZone:n}){return e??=()=>new Y(z(E({},yc()),{scheduleInRootZone:n})),[{provide:Y,useFactory:e},{provide:Ze,multi:!0,useFactory:()=>{let r=v(uy,{optional:!0});return()=>r.initialize()}},{provide:Ze,multi:!0,useFactory:()=>{let r=v(dy);return()=>{r.initialize()}}},t===!0?{provide:Ca,useValue:!0}:[],{provide:Da,useValue:n??Df},{provide:xe,useFactory:()=>{let r=v(Y),o=v(ne),i;return s=>{r.runOutsideAngular(()=>{o.destroyed&&!i?setTimeout(()=>{throw s}):(i??=o.get(ke),i.handleError(s))})}}}]}function vc(e){let t=e?.ignoreChangesOutsideZone,n=e?.scheduleInRootZone,r=mc({ngZoneFactory:()=>{let o=yc(e);return o.scheduleInRootZone=n,o.shouldCoalesceEventChangeDetection&&mn("NgZone_CoalesceEvent"),new Y(o)},ignoreChangesOutsideZone:t,scheduleInRootZone:n});return Tt([{provide:kf,useValue:!0},{provide:or,useValue:!1},r])}function yc(e){return{enableLongStackTrace:!1,shouldCoalesceEventChangeDetection:e?.eventCoalescing??!1,shouldCoalesceRunChangeDetection:e?.runCoalescing??!1}}var dy=(()=>{class e{subscription=new G;initialized=!1;zone=v(Y);pendingTasks=v(Ye);initialize(){if(this.initialized)return;this.initialized=!0;let n=null;!this.zone.isStable&&!this.zone.hasPendingMacrotasks&&!this.zone.hasPendingMicrotasks&&(n=this.pendingTasks.add()),this.zone.runOutsideAngular(()=>{this.subscription.add(this.zone.onStable.subscribe(()=>{Y.assertNotInAngularZone(),queueMicrotask(()=>{n!==null&&!this.zone.hasPendingMacrotasks&&!this.zone.hasPendingMicrotasks&&(this.pendingTasks.remove(n),n=null)})}))}),this.subscription.add(this.zone.onUnstable.subscribe(()=>{Y.assertInAngularZone(),n??=this.pendingTasks.add()}))}ngOnDestroy(){this.subscription.unsubscribe()}static \u0275fac=function(r){return new(r||e)};static \u0275prov=b({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var Pf=(()=>{class e{applicationErrorHandler=v(xe);appRef=v(Lt);taskService=v(Ye);ngZone=v(Y);zonelessEnabled=v(or);tracing=v(vr,{optional:!0});disableScheduling=v(Ca,{optional:!0})??!1;zoneIsDefined=typeof Zone<"u"&&!!Zone.root.run;schedulerTickApplyArgs=[{data:{__scheduler_tick__:!0}}];subscriptions=new G;angularZoneId=this.zoneIsDefined?this.ngZone._inner?.get(ni):null;scheduleInRootZone=!this.zonelessEnabled&&this.zoneIsDefined&&(v(Da,{optional:!0})??!1);cancelScheduledCallback=null;useMicrotaskScheduler=!1;runningTick=!1;pendingRenderTaskId=null;constructor(){this.subscriptions.add(this.appRef.afterTick.subscribe(()=>{this.runningTick||this.cleanup()})),this.subscriptions.add(this.ngZone.onUnstable.subscribe(()=>{this.runningTick||this.cleanup()})),this.disableScheduling||=!this.zonelessEnabled&&(this.ngZone instanceof ri||!this.zoneIsDefined)}notify(n){if(!this.zonelessEnabled&&n===5)return;let r=!1;switch(n){case 0:{this.appRef.dirtyFlags|=2;break}case 3:case 2:case 4:case 5:case 1:{this.appRef.dirtyFlags|=4;break}case 6:{this.appRef.dirtyFlags|=2,r=!0;break}case 12:{this.appRef.dirtyFlags|=16,r=!0;break}case 13:{this.appRef.dirtyFlags|=2,r=!0;break}case 11:{r=!0;break}case 9:case 8:case 7:case 10:default:this.appRef.dirtyFlags|=8}if(this.appRef.tracingSnapshot=this.tracing?.snapshot(this.appRef.tracingSnapshot)??null,!this.shouldScheduleTick(r))return;let o=this.useMicrotaskScheduler?rd:Tf;this.pendingRenderTaskId=this.taskService.add(),this.scheduleInRootZone?this.cancelScheduledCallback=Zone.root.run(()=>o(()=>this.tick())):this.cancelScheduledCallback=this.ngZone.runOutsideAngular(()=>o(()=>this.tick()))}shouldScheduleTick(n){return!(this.disableScheduling&&!n||this.appRef.destroyed||this.pendingRenderTaskId!==null||this.runningTick||this.appRef._runningTick||!this.zonelessEnabled&&this.zoneIsDefined&&Zone.current.get(ni+this.angularZoneId))}tick(){if(this.runningTick||this.appRef.destroyed)return;if(this.appRef.dirtyFlags===0){this.cleanup();return}!this.zonelessEnabled&&this.appRef.dirtyFlags&7&&(this.appRef.dirtyFlags|=1);let n=this.taskService.add();try{this.ngZone.run(()=>{this.runningTick=!0,this.appRef._tick()},void 0,this.schedulerTickApplyArgs)}catch(r){this.taskService.remove(n),this.applicationErrorHandler(r)}finally{this.cleanup()}this.useMicrotaskScheduler=!0,rd(()=>{this.useMicrotaskScheduler=!1,this.taskService.remove(n)})}ngOnDestroy(){this.subscriptions.unsubscribe(),this.cleanup()}cleanup(){if(this.runningTick=!1,this.cancelScheduledCallback?.(),this.cancelScheduledCallback=null,this.pendingRenderTaskId!==null){let n=this.pendingRenderTaskId;this.pendingRenderTaskId=null,this.taskService.remove(n)}}static \u0275fac=function(r){return new(r||e)};static \u0275prov=b({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function fy(){return typeof $localize<"u"&&$localize.locale||Er}var Ec=new w("",{providedIn:"root",factory:()=>v(Ec,{optional:!0,skipSelf:!0})||fy()});var Vf=Symbol("InputSignalNode#UNSET"),wy=z(E({},ro),{transformFn:void 0,applyValueToInputSignal(e,t){An(e,t)}});function Hf(e,t){let n=Object.create(wy);n.value=e,n.transformFn=t?.transform;function r(){if(Jr(n),n.value===Vf){let o=null;throw new I(-950,o)}return n.value}return r[$e]=n,r}var Sy=new w("");Sy.__NG_ELEMENT_ID__=e=>{let t=Fe();if(t===null)throw new I(204,!1);if(t.type&2)return t.value;if(e&8)return null;throw new I(204,!1)};function Lf(e,t){return Hf(e,t)}function by(e){return Hf(Vf,e)}var Uf=(Lf.required=by,Lf);var Ic=new w(""),Cy=new w("");function Ir(e){return!e.moduleRef}function Dy(e){let t=Ir(e)?e.r3Injector:e.moduleRef.injector,n=t.get(Y);return n.run(()=>{Ir(e)?e.r3Injector.resolveInjectorInitializers():e.moduleRef.resolveInjectorInitializers();let r=t.get(xe),o;if(n.runOutsideAngular(()=>{o=n.onError.subscribe({next:r})}),Ir(e)){let i=()=>t.destroy(),s=e.platformInjector.get(Ic);s.add(i),t.onDestroy(()=>{o.unsubscribe(),s.delete(i)})}else{let i=()=>e.moduleRef.destroy(),s=e.platformInjector.get(Ic);s.add(i),e.moduleRef.onDestroy(()=>{ar(e.allPlatformModules,e.moduleRef),o.unsubscribe(),s.delete(i)})}return My(r,n,()=>{let i=t.get(Ye),s=i.add(),a=t.get(hc);return a.runInitializers(),a.donePromise.then(()=>{try{let c=t.get(Ec,Er);if(Of(c||Er),!t.get(Cy,!0))return Ir(e)?t.get(Lt):(e.allPlatformModules.push(e.moduleRef),e.moduleRef);if(Ir(e)){let f=t.get(Lt);return e.rootComponent!==void 0&&f.bootstrap(e.rootComponent),f}else return Ty?.(e.moduleRef,e.allPlatformModules),e.moduleRef}finally{i.remove(s)}})})})}var Ty;function My(e,t,n){try{let r=n();return yr(r)?r.catch(o=>{throw t.runOutsideAngular(()=>e(o)),o}):r}catch(r){throw t.runOutsideAngular(()=>e(r)),r}}var Si=null;function _y(e=[],t){return we.create({name:t,providers:[{provide:zn,useValue:"platform"},{provide:Ic,useValue:new Set([()=>Si=null])},...e]})}function Ny(e=[]){if(Si)return Si;let t=_y(e);return Si=t,Rf(),xy(t),t}function xy(e){let t=e.get(ui,null);oe(e,()=>{t?.forEach(n=>n())})}var bi=(()=>{class e{static __NG_ELEMENT_ID__=Ry}return e})();function Ry(e){return Ay(Fe(),fe(),(e&16)===16)}function Ay(e,t,n){if(an(e)&&!n){let r=Le(e.index,t);return new At(r,r)}else if(e.type&175){let r=t[Ce];return new At(r,t)}return null}function $f(e){B(8);try{let{rootComponent:t,appProviders:n,platformProviders:r}=e,o=Ny(r),i=[mc({}),{provide:bt,useExisting:Pf},ku,...n||[]],s=new dr({providers:i,parent:o,debugName:"",runEnvironmentInitializers:!1});return Dy({r3Injector:s.injector,platformInjector:o,rootComponent:t})}catch(t){return Promise.reject(t)}finally{B(9)}}function vn(e){return typeof e=="boolean"?e:e!=null&&e!=="false"}function wc(e,t=NaN){return!isNaN(parseFloat(e))&&!isNaN(Number(e))?Number(e):t}var Gf=null;function Xe(){return Gf}function Sc(e){Gf??=e}var wr=class{},bc=(()=>{class e{historyGo(n){throw new Error("")}static \u0275fac=function(r){return new(r||e)};static \u0275prov=b({token:e,factory:()=>v(qf),providedIn:"platform"})}return e})();var qf=(()=>{class e extends bc{_location;_history;_doc=v(Q);constructor(){super(),this._location=window.location,this._history=window.history}getBaseHrefFromDOM(){return Xe().getBaseHref(this._doc)}onPopState(n){let r=Xe().getGlobalEventTarget(this._doc,"window");return r.addEventListener("popstate",n,!1),()=>r.removeEventListener("popstate",n)}onHashChange(n){let r=Xe().getGlobalEventTarget(this._doc,"window");return r.addEventListener("hashchange",n,!1),()=>r.removeEventListener("hashchange",n)}get href(){return this._location.href}get protocol(){return this._location.protocol}get hostname(){return this._location.hostname}get port(){return this._location.port}get pathname(){return this._location.pathname}get search(){return this._location.search}get hash(){return this._location.hash}set pathname(n){this._location.pathname=n}pushState(n,r,o){this._history.pushState(n,r,o)}replaceState(n,r,o){this._history.replaceState(n,r,o)}forward(){this._history.forward()}back(){this._history.back()}historyGo(n=0){this._history.go(n)}getState(){return this._history.state}static \u0275fac=function(r){return new(r||e)};static \u0275prov=b({token:e,factory:()=>new e,providedIn:"platform"})}return e})();function Wf(e,t){return e?t?e.endsWith("/")?t.startsWith("/")?e+t.slice(1):e+t:t.startsWith("/")?e+t:`${e}/${t}`:e:t}function Bf(e){let t=e.search(/#|\?|$/);return e[t-1]==="/"?e.slice(0,t-1)+e.slice(t):e}function pt(e){return e&&e[0]!=="?"?`?${e}`:e}var Ci=(()=>{class e{historyGo(n){throw new Error("")}static \u0275fac=function(r){return new(r||e)};static \u0275prov=b({token:e,factory:()=>v(Qf),providedIn:"root"})}return e})(),Zf=new w(""),Qf=(()=>{class e extends Ci{_platformLocation;_baseHref;_removeListenerFns=[];constructor(n,r){super(),this._platformLocation=n,this._baseHref=r??this._platformLocation.getBaseHrefFromDOM()??v(Q).location?.origin??""}ngOnDestroy(){for(;this._removeListenerFns.length;)this._removeListenerFns.pop()()}onPopState(n){this._removeListenerFns.push(this._platformLocation.onPopState(n),this._platformLocation.onHashChange(n))}getBaseHref(){return this._baseHref}prepareExternalUrl(n){return Wf(this._baseHref,n)}path(n=!1){let r=this._platformLocation.pathname+pt(this._platformLocation.search),o=this._platformLocation.hash;return o&&n?`${r}${o}`:r}pushState(n,r,o,i){let s=this.prepareExternalUrl(o+pt(i));this._platformLocation.pushState(n,r,s)}replaceState(n,r,o,i){let s=this.prepareExternalUrl(o+pt(i));this._platformLocation.replaceState(n,r,s)}forward(){this._platformLocation.forward()}back(){this._platformLocation.back()}getState(){return this._platformLocation.getState()}historyGo(n=0){this._platformLocation.historyGo?.(n)}static \u0275fac=function(r){return new(r||e)(M(bc),M(Zf,8))};static \u0275prov=b({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),yn=(()=>{class e{_subject=new ee;_basePath;_locationStrategy;_urlChangeListeners=[];_urlChangeSubscription=null;constructor(n){this._locationStrategy=n;let r=this._locationStrategy.getBaseHref();this._basePath=Py(Bf(zf(r))),this._locationStrategy.onPopState(o=>{this._subject.next({url:this.path(!0),pop:!0,state:o.state,type:o.type})})}ngOnDestroy(){this._urlChangeSubscription?.unsubscribe(),this._urlChangeListeners=[]}path(n=!1){return this.normalize(this._locationStrategy.path(n))}getState(){return this._locationStrategy.getState()}isCurrentPathEqualTo(n,r=""){return this.path()==this.normalize(n+pt(r))}normalize(n){return e.stripTrailingSlash(ky(this._basePath,zf(n)))}prepareExternalUrl(n){return n&&n[0]!=="/"&&(n="/"+n),this._locationStrategy.prepareExternalUrl(n)}go(n,r="",o=null){this._locationStrategy.pushState(o,"",n,r),this._notifyUrlChangeListeners(this.prepareExternalUrl(n+pt(r)),o)}replaceState(n,r="",o=null){this._locationStrategy.replaceState(o,"",n,r),this._notifyUrlChangeListeners(this.prepareExternalUrl(n+pt(r)),o)}forward(){this._locationStrategy.forward()}back(){this._locationStrategy.back()}historyGo(n=0){this._locationStrategy.historyGo?.(n)}onUrlChange(n){return this._urlChangeListeners.push(n),this._urlChangeSubscription??=this.subscribe(r=>{this._notifyUrlChangeListeners(r.url,r.state)}),()=>{let r=this._urlChangeListeners.indexOf(n);this._urlChangeListeners.splice(r,1),this._urlChangeListeners.length===0&&(this._urlChangeSubscription?.unsubscribe(),this._urlChangeSubscription=null)}}_notifyUrlChangeListeners(n="",r){this._urlChangeListeners.forEach(o=>o(n,r))}subscribe(n,r,o){return this._subject.subscribe({next:n,error:r??void 0,complete:o??void 0})}static normalizeQueryParams=pt;static joinWithSlash=Wf;static stripTrailingSlash=Bf;static \u0275fac=function(r){return new(r||e)(M(Ci))};static \u0275prov=b({token:e,factory:()=>Oy(),providedIn:"root"})}return e})();function Oy(){return new yn(M(Ci))}function ky(e,t){if(!e||!t.startsWith(e))return t;let n=t.substring(e.length);return n===""||["/",";","?","#"].includes(n[0])?n:t}function zf(e){return e.replace(/\/index.html$/,"")}function Py(e){if(new RegExp("^(https?:)?//").test(e)){let[,n]=e.split(/\/\/[^\/]+/);return n}return e}function Cc(e,t){t=encodeURIComponent(t);for(let n of e.split(";")){let r=n.indexOf("="),[o,i]=r==-1?[n,""]:[n.slice(0,r),n.slice(r+1)];if(o.trim()===t)return decodeURIComponent(i)}return null}var Sr=class{};var Kf="browser";var Jf=e=>e.src,Ly=new w("",{providedIn:"root",factory:()=>Jf});var Yf=/^((\s*\d+w\s*(,|$)){1,})$/;var Fy=[1,2],jy=640;var Vy=1920,Hy=1080;var Xf=(()=>{class e{imageLoader=v(Ly);config=Uy(v(pi));renderer=v(cc);imgElement=v(ci).nativeElement;injector=v(we);lcpObserver;_renderedSrc=null;ngSrc;ngSrcset;sizes;width;height;decoding;loading;priority=!1;loaderParams;disableOptimizedSrcset=!1;fill=!1;placeholder;placeholderConfig;src;srcset;constructor(){}ngOnInit(){mn("NgOptimizedImage"),this.placeholder&&this.removePlaceholderOnLoad(this.imgElement),this.setHostAttributes()}setHostAttributes(){this.fill?this.sizes||="100vw":(this.setHostAttribute("width",this.width.toString()),this.setHostAttribute("height",this.height.toString())),this.setHostAttribute("loading",this.getLoadingBehavior()),this.setHostAttribute("fetchpriority",this.getFetchPriority()),this.setHostAttribute("decoding",this.getDecoding()),this.setHostAttribute("ng-img","true");let n=this.updateSrcAndSrcset();this.sizes?this.getLoadingBehavior()==="lazy"?this.setHostAttribute("sizes","auto, "+this.sizes):this.setHostAttribute("sizes",this.sizes):this.ngSrcset&&Yf.test(this.ngSrcset)&&this.getLoadingBehavior()==="lazy"&&this.setHostAttribute("sizes","auto, 100vw")}ngOnChanges(n){if(n.ngSrc&&!n.ngSrc.isFirstChange()){let r=this._renderedSrc;this.updateSrcAndSrcset(!0)}}callImageLoader(n){let r=n;return this.loaderParams&&(r.loaderParams=this.loaderParams),this.imageLoader(r)}getLoadingBehavior(){return!this.priority&&this.loading!==void 0?this.loading:this.priority?"eager":"lazy"}getFetchPriority(){return this.priority?"high":"auto"}getDecoding(){return this.priority?"sync":this.decoding??"auto"}getRewrittenSrc(){if(!this._renderedSrc){let n={src:this.ngSrc};this._renderedSrc=this.callImageLoader(n)}return this._renderedSrc}getRewrittenSrcset(){let n=Yf.test(this.ngSrcset);return this.ngSrcset.split(",").filter(o=>o!=="").map(o=>{o=o.trim();let i=n?parseFloat(o):parseFloat(o)*this.width;return`${this.callImageLoader({src:this.ngSrc,width:i})} ${o}`}).join(", ")}getAutomaticSrcset(){return this.sizes?this.getResponsiveSrcset():this.getFixedSrcset()}getResponsiveSrcset(){let{breakpoints:n}=this.config,r=n;return this.sizes?.trim()==="100vw"&&(r=n.filter(i=>i>=jy)),r.map(i=>`${this.callImageLoader({src:this.ngSrc,width:i})} ${i}w`).join(", ")}updateSrcAndSrcset(n=!1){n&&(this._renderedSrc=null);let r=this.getRewrittenSrc();this.setHostAttribute("src",r);let o;return this.ngSrcset?o=this.getRewrittenSrcset():this.shouldGenerateAutomaticSrcset()&&(o=this.getAutomaticSrcset()),o&&this.setHostAttribute("srcset",o),o}getFixedSrcset(){return Fy.map(r=>`${this.callImageLoader({src:this.ngSrc,width:this.width*r})} ${r}x`).join(", ")}shouldGenerateAutomaticSrcset(){let n=!1;return this.sizes||(n=this.width>Vy||this.height>Hy),!this.disableOptimizedSrcset&&!this.srcset&&this.imageLoader!==Jf&&!n}generatePlaceholder(n){let{placeholderResolution:r}=this.config;return n===!0?`url(${this.callImageLoader({src:this.ngSrc,width:r,isPlaceholder:!0})})`:typeof n=="string"?`url(${n})`:null}shouldBlurPlaceholder(n){return!n||!n.hasOwnProperty("blur")?!0:!!n.blur}removePlaceholderOnLoad(n){let r=()=>{let s=this.injector.get(bi);o(),i(),this.placeholder=!1,s.markForCheck()},o=this.renderer.listen(n,"load",r),i=this.renderer.listen(n,"error",r);$y(n,r)}setHostAttribute(n,r){this.renderer.setAttribute(this.imgElement,n,r)}static \u0275fac=function(r){return new(r||e)};static \u0275dir=mr({type:e,selectors:[["img","ngSrc",""]],hostVars:18,hostBindings:function(r,o){r&2&&wi("position",o.fill?"absolute":null)("width",o.fill?"100%":null)("height",o.fill?"100%":null)("inset",o.fill?"0":null)("background-size",o.placeholder?"cover":null)("background-position",o.placeholder?"50% 50%":null)("background-repeat",o.placeholder?"no-repeat":null)("background-image",o.placeholder?o.generatePlaceholder(o.placeholder):null)("filter",o.placeholder&&o.shouldBlurPlaceholder(o.placeholderConfig)?"blur(15px)":null)},inputs:{ngSrc:[2,"ngSrc","ngSrc",By],ngSrcset:"ngSrcset",sizes:"sizes",width:[2,"width","width",wc],height:[2,"height","height",wc],decoding:"decoding",loading:"loading",priority:[2,"priority","priority",vn],loaderParams:"loaderParams",disableOptimizedSrcset:[2,"disableOptimizedSrcset","disableOptimizedSrcset",vn],fill:[2,"fill","fill",vn],placeholder:[2,"placeholder","placeholder",zy],placeholderConfig:"placeholderConfig",src:"src",srcset:"srcset"},features:[pr]})}return e})();function Uy(e){let t={};return e.breakpoints&&(t.breakpoints=e.breakpoints.sort((n,r)=>n-r)),Object.assign({},fi,e,t)}function $y(e,t){e.complete&&e.naturalWidth&&t()}function By(e){return typeof e=="string"?e:hi(e)}function zy(e){return typeof e=="string"&&e!=="true"&&e!=="false"&&e!==""?e:vn(e)}var Ti=new w(""),Nc=(()=>{class e{_zone;_plugins;_eventNameToPlugin=new Map;constructor(n,r){this._zone=r,n.forEach(o=>{o.manager=this}),this._plugins=n.slice().reverse()}addEventListener(n,r,o,i){return this._findPluginFor(r).addEventListener(n,r,o,i)}getZone(){return this._zone}_findPluginFor(n){let r=this._eventNameToPlugin.get(n);if(r)return r;if(r=this._plugins.find(i=>i.supports(n)),!r)throw new I(5101,!1);return this._eventNameToPlugin.set(n,r),r}static \u0275fac=function(r){return new(r||e)(M(Ti),M(Y))};static \u0275prov=b({token:e,factory:e.\u0275fac})}return e})(),br=class{_doc;constructor(t){this._doc=t}manager},Dc="ng-app-id";function ep(e){for(let t of e)t.remove()}function tp(e,t){let n=t.createElement("style");return n.textContent=e,n}function Gy(e,t,n,r){let o=e.head?.querySelectorAll(`style[${Dc}="${t}"],link[${Dc}="${t}"]`);if(o)for(let i of o)i.removeAttribute(Dc),i instanceof HTMLLinkElement?r.set(i.href.slice(i.href.lastIndexOf("/")+1),{usage:0,elements:[i]}):i.textContent&&n.set(i.textContent,{usage:0,elements:[i]})}function Mc(e,t){let n=t.createElement("link");return n.setAttribute("rel","stylesheet"),n.setAttribute("href",e),n}var xc=(()=>{class e{doc;appId;nonce;inline=new Map;external=new Map;hosts=new Set;constructor(n,r,o,i={}){this.doc=n,this.appId=r,this.nonce=o,Gy(n,r,this.inline,this.external),this.hosts.add(n.head)}addStyles(n,r){for(let o of n)this.addUsage(o,this.inline,tp);r?.forEach(o=>this.addUsage(o,this.external,Mc))}removeStyles(n,r){for(let o of n)this.removeUsage(o,this.inline);r?.forEach(o=>this.removeUsage(o,this.external))}addUsage(n,r,o){let i=r.get(n);i?i.usage++:r.set(n,{usage:1,elements:[...this.hosts].map(s=>this.addElement(s,o(n,this.doc)))})}removeUsage(n,r){let o=r.get(n);o&&(o.usage--,o.usage<=0&&(ep(o.elements),r.delete(n)))}ngOnDestroy(){for(let[,{elements:n}]of[...this.inline,...this.external])ep(n);this.hosts.clear()}addHost(n){this.hosts.add(n);for(let[r,{elements:o}]of this.inline)o.push(this.addElement(n,tp(r,this.doc)));for(let[r,{elements:o}]of this.external)o.push(this.addElement(n,Mc(r,this.doc)))}removeHost(n){this.hosts.delete(n)}addElement(n,r){return this.nonce&&r.setAttribute("nonce",this.nonce),n.appendChild(r)}static \u0275fac=function(r){return new(r||e)(M(Q),M(li),M(di,8),M(hn))};static \u0275prov=b({token:e,factory:e.\u0275fac})}return e})(),Tc={svg:"http://www.w3.org/2000/svg",xhtml:"http://www.w3.org/1999/xhtml",xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/",math:"http://www.w3.org/1998/Math/MathML"},Rc=/%COMP%/g;var rp="%COMP%",qy=`_nghost-${rp}`,Wy=`_ngcontent-${rp}`,Zy=!0,Qy=new w("",{providedIn:"root",factory:()=>Zy});function Yy(e){return Wy.replace(Rc,e)}function Ky(e){return qy.replace(Rc,e)}function op(e,t){return t.map(n=>n.replace(Rc,e))}var Ac=(()=>{class e{eventManager;sharedStylesHost;appId;removeStylesOnCompDestroy;doc;platformId;ngZone;nonce;tracingService;rendererByCompId=new Map;defaultRenderer;platformIsServer;constructor(n,r,o,i,s,a,c,l=null,f=null){this.eventManager=n,this.sharedStylesHost=r,this.appId=o,this.removeStylesOnCompDestroy=i,this.doc=s,this.platformId=a,this.ngZone=c,this.nonce=l,this.tracingService=f,this.platformIsServer=!1,this.defaultRenderer=new Cr(n,s,c,this.platformIsServer,this.tracingService)}createRenderer(n,r){if(!n||!r)return this.defaultRenderer;let o=this.getOrCreateRenderer(n,r);return o instanceof Di?o.applyToHost(n):o instanceof Dr&&o.applyStyles(),o}getOrCreateRenderer(n,r){let o=this.rendererByCompId,i=o.get(r.id);if(!i){let s=this.doc,a=this.ngZone,c=this.eventManager,l=this.sharedStylesHost,f=this.removeStylesOnCompDestroy,p=this.platformIsServer,y=this.tracingService;switch(r.encapsulation){case Ke.Emulated:i=new Di(c,l,r,this.appId,f,s,a,p,y);break;case Ke.ShadowDom:return new _c(c,l,n,r,s,a,this.nonce,p,y);default:i=new Dr(c,l,r,f,s,a,p,y);break}o.set(r.id,i)}return i}ngOnDestroy(){this.rendererByCompId.clear()}componentReplaced(n){this.rendererByCompId.delete(n)}static \u0275fac=function(r){return new(r||e)(M(Nc),M(xc),M(li),M(Qy),M(Q),M(hn),M(Y),M(di),M(vr,8))};static \u0275prov=b({token:e,factory:e.\u0275fac})}return e})(),Cr=class{eventManager;doc;ngZone;platformIsServer;tracingService;data=Object.create(null);throwOnSyntheticProps=!0;constructor(t,n,r,o,i){this.eventManager=t,this.doc=n,this.ngZone=r,this.platformIsServer=o,this.tracingService=i}destroy(){}destroyNode=null;createElement(t,n){return n?this.doc.createElementNS(Tc[n]||n,t):this.doc.createElement(t)}createComment(t){return this.doc.createComment(t)}createText(t){return this.doc.createTextNode(t)}appendChild(t,n){(np(t)?t.content:t).appendChild(n)}insertBefore(t,n,r){t&&(np(t)?t.content:t).insertBefore(n,r)}removeChild(t,n){n.remove()}selectRootElement(t,n){let r=typeof t=="string"?this.doc.querySelector(t):t;if(!r)throw new I(-5104,!1);return n||(r.textContent=""),r}parentNode(t){return t.parentNode}nextSibling(t){return t.nextSibling}setAttribute(t,n,r,o){if(o){n=o+":"+n;let i=Tc[o];i?t.setAttributeNS(i,n,r):t.setAttribute(n,r)}else t.setAttribute(n,r)}removeAttribute(t,n,r){if(r){let o=Tc[r];o?t.removeAttributeNS(o,n):t.removeAttribute(`${r}:${n}`)}else t.removeAttribute(n)}addClass(t,n){t.classList.add(n)}removeClass(t,n){t.classList.remove(n)}setStyle(t,n,r,o){o&(Je.DashCase|Je.Important)?t.style.setProperty(n,r,o&Je.Important?"important":""):t.style[n]=r}removeStyle(t,n,r){r&Je.DashCase?t.style.removeProperty(n):t.style[n]=""}setProperty(t,n,r){t!=null&&(t[n]=r)}setValue(t,n){t.nodeValue=n}listen(t,n,r,o){if(typeof t=="string"&&(t=Xe().getGlobalEventTarget(this.doc,t),!t))throw new I(5102,!1);let i=this.decoratePreventDefault(r);return this.tracingService?.wrapEventListener&&(i=this.tracingService.wrapEventListener(t,n,i)),this.eventManager.addEventListener(t,n,i,o)}decoratePreventDefault(t){return n=>{if(n==="__ngUnwrap__")return t;t(n)===!1&&n.preventDefault()}}};function np(e){return e.tagName==="TEMPLATE"&&e.content!==void 0}var _c=class extends Cr{sharedStylesHost;hostEl;shadowRoot;constructor(t,n,r,o,i,s,a,c,l){super(t,i,s,c,l),this.sharedStylesHost=n,this.hostEl=r,this.shadowRoot=r.attachShadow({mode:"open"}),this.sharedStylesHost.addHost(this.shadowRoot);let f=o.styles;f=op(o.id,f);for(let y of f){let h=document.createElement("style");a&&h.setAttribute("nonce",a),h.textContent=y,this.shadowRoot.appendChild(h)}let p=o.getExternalStyles?.();if(p)for(let y of p){let h=Mc(y,i);a&&h.setAttribute("nonce",a),this.shadowRoot.appendChild(h)}}nodeOrShadowRoot(t){return t===this.hostEl?this.shadowRoot:t}appendChild(t,n){return super.appendChild(this.nodeOrShadowRoot(t),n)}insertBefore(t,n,r){return super.insertBefore(this.nodeOrShadowRoot(t),n,r)}removeChild(t,n){return super.removeChild(null,n)}parentNode(t){return this.nodeOrShadowRoot(super.parentNode(this.nodeOrShadowRoot(t)))}destroy(){this.sharedStylesHost.removeHost(this.shadowRoot)}},Dr=class extends Cr{sharedStylesHost;removeStylesOnCompDestroy;styles;styleUrls;constructor(t,n,r,o,i,s,a,c,l){super(t,i,s,a,c),this.sharedStylesHost=n,this.removeStylesOnCompDestroy=o;let f=r.styles;this.styles=l?op(l,f):f,this.styleUrls=r.getExternalStyles?.(l)}applyStyles(){this.sharedStylesHost.addStyles(this.styles,this.styleUrls)}destroy(){this.removeStylesOnCompDestroy&&this.sharedStylesHost.removeStyles(this.styles,this.styleUrls)}},Di=class extends Dr{contentAttr;hostAttr;constructor(t,n,r,o,i,s,a,c,l){let f=o+"-"+r.id;super(t,n,r,i,s,a,c,l,f),this.contentAttr=Yy(f),this.hostAttr=Ky(f)}applyToHost(t){this.applyStyles(),this.setAttribute(t,this.hostAttr,"")}createElement(t,n){let r=super.createElement(t,n);return super.setAttribute(r,this.contentAttr,""),r}};var Mi=class e extends wr{supportsDOMEvents=!0;static makeCurrent(){Sc(new e)}onAndCancel(t,n,r,o){return t.addEventListener(n,r,o),()=>{t.removeEventListener(n,r,o)}}dispatchEvent(t,n){t.dispatchEvent(n)}remove(t){t.remove()}createElement(t,n){return n=n||this.getDefaultDocument(),n.createElement(t)}createHtmlDocument(){return document.implementation.createHTMLDocument("fakeTitle")}getDefaultDocument(){return document}isElementNode(t){return t.nodeType===Node.ELEMENT_NODE}isShadowRoot(t){return t instanceof DocumentFragment}getGlobalEventTarget(t,n){return n==="window"?window:n==="document"?t:n==="body"?t.body:null}getBaseHref(t){let n=Jy();return n==null?null:Xy(n)}resetBaseElement(){Tr=null}getUserAgent(){return window.navigator.userAgent}getCookie(t){return Cc(document.cookie,t)}},Tr=null;function Jy(){return Tr=Tr||document.head.querySelector("base"),Tr?Tr.getAttribute("href"):null}function Xy(e){return new URL(e,document.baseURI).pathname}var eE=(()=>{class e{build(){return new XMLHttpRequest}static \u0275fac=function(r){return new(r||e)};static \u0275prov=b({token:e,factory:e.\u0275fac})}return e})(),sp=(()=>{class e extends br{constructor(n){super(n)}supports(n){return!0}addEventListener(n,r,o,i){return n.addEventListener(r,o,i),()=>this.removeEventListener(n,r,o,i)}removeEventListener(n,r,o,i){return n.removeEventListener(r,o,i)}static \u0275fac=function(r){return new(r||e)(M(Q))};static \u0275prov=b({token:e,factory:e.\u0275fac})}return e})(),ip=["alt","control","meta","shift"],tE={"\b":"Backspace","	":"Tab","\x7F":"Delete","\x1B":"Escape",Del:"Delete",Esc:"Escape",Left:"ArrowLeft",Right:"ArrowRight",Up:"ArrowUp",Down:"ArrowDown",Menu:"ContextMenu",Scroll:"ScrollLock",Win:"OS"},nE={alt:e=>e.altKey,control:e=>e.ctrlKey,meta:e=>e.metaKey,shift:e=>e.shiftKey},ap=(()=>{class e extends br{constructor(n){super(n)}supports(n){return e.parseEventName(n)!=null}addEventListener(n,r,o,i){let s=e.parseEventName(r),a=e.eventCallback(s.fullKey,o,this.manager.getZone());return this.manager.getZone().runOutsideAngular(()=>Xe().onAndCancel(n,s.domEventName,a,i))}static parseEventName(n){let r=n.toLowerCase().split("."),o=r.shift();if(r.length===0||!(o==="keydown"||o==="keyup"))return null;let i=e._normalizeKey(r.pop()),s="",a=r.indexOf("code");if(a>-1&&(r.splice(a,1),s="code."),ip.forEach(l=>{let f=r.indexOf(l);f>-1&&(r.splice(f,1),s+=l+".")}),s+=i,r.length!=0||i.length===0)return null;let c={};return c.domEventName=o,c.fullKey=s,c}static matchEventFullKeyCode(n,r){let o=tE[n.key]||n.key,i="";return r.indexOf("code.")>-1&&(o=n.code,i="code."),o==null||!o?!1:(o=o.toLowerCase(),o===" "?o="space":o==="."&&(o="dot"),ip.forEach(s=>{if(s!==o){let a=nE[s];a(n)&&(i+=s+".")}}),i+=o,i===r)}static eventCallback(n,r,o){return i=>{e.matchEventFullKeyCode(i,n)&&o.runGuarded(()=>r(i))}}static _normalizeKey(n){return n==="esc"?"escape":n}static \u0275fac=function(r){return new(r||e)(M(Q))};static \u0275prov=b({token:e,factory:e.\u0275fac})}return e})();function Oc(e,t){return $f(E({rootComponent:e},rE(t)))}function rE(e){return{appProviders:[...cE,...e?.providers??[]],platformProviders:aE}}function oE(){Mi.makeCurrent()}function iE(){return new ke}function sE(){return tc(document),document}var aE=[{provide:hn,useValue:Kf},{provide:ui,useValue:oE,multi:!0},{provide:Q,useFactory:sE}];var cE=[{provide:zn,useValue:"root"},{provide:ke,useFactory:iE},{provide:Ti,useClass:sp,multi:!0,deps:[Q]},{provide:Ti,useClass:ap,multi:!0,deps:[Q]},Ac,xc,Nc,{provide:Ot,useExisting:Ac},{provide:Sr,useClass:eE},[]];var cp=(()=>{class e{_doc;constructor(n){this._doc=n}getTitle(){return this._doc.title}setTitle(n){this._doc.title=n||""}static \u0275fac=function(r){return new(r||e)(M(Q))};static \u0275prov=b({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var T="primary",Ur=Symbol("RouteTitle"),jc=class{params;constructor(t){this.params=t||{}}has(t){return Object.prototype.hasOwnProperty.call(this.params,t)}get(t){if(this.has(t)){let n=this.params[t];return Array.isArray(n)?n[0]:n}return null}getAll(t){if(this.has(t)){let n=this.params[t];return Array.isArray(n)?n:[n]}return[]}get keys(){return Object.keys(this.params)}};function Vt(e){return new jc(e)}function mp(e,t,n){let r=n.path.split("/");if(r.length>e.length||n.pathMatch==="full"&&(t.hasChildren()||r.length<e.length))return null;let o={};for(let i=0;i<r.length;i++){let s=r[i],a=e[i];if(s[0]===":")o[s.substring(1)]=a;else if(s!==a.path)return null}return{consumed:e.slice(0,r.length),posParams:o}}function uE(e,t){if(e.length!==t.length)return!1;for(let n=0;n<e.length;++n)if(!Ve(e[n],t[n]))return!1;return!0}function Ve(e,t){let n=e?Vc(e):void 0,r=t?Vc(t):void 0;if(!n||!r||n.length!=r.length)return!1;let o;for(let i=0;i<n.length;i++)if(o=n[i],!vp(e[o],t[o]))return!1;return!0}function Vc(e){return[...Object.keys(e),...Object.getOwnPropertySymbols(e)]}function vp(e,t){if(Array.isArray(e)&&Array.isArray(t)){if(e.length!==t.length)return!1;let n=[...e].sort(),r=[...t].sort();return n.every((o,i)=>r[i]===o)}else return e===t}function yp(e){return e.length>0?e[e.length-1]:null}function nt(e){return ws(e)?e:yr(e)?q(Promise.resolve(e)):C(e)}var dE={exact:Ip,subset:wp},Ep={exact:fE,subset:pE,ignored:()=>!0};function lp(e,t,n){return dE[n.paths](e.root,t.root,n.matrixParams)&&Ep[n.queryParams](e.queryParams,t.queryParams)&&!(n.fragment==="exact"&&e.fragment!==t.fragment)}function fE(e,t){return Ve(e,t)}function Ip(e,t,n){if(!Ft(e.segments,t.segments)||!xi(e.segments,t.segments,n)||e.numberOfChildren!==t.numberOfChildren)return!1;for(let r in t.children)if(!e.children[r]||!Ip(e.children[r],t.children[r],n))return!1;return!0}function pE(e,t){return Object.keys(t).length<=Object.keys(e).length&&Object.keys(t).every(n=>vp(e[n],t[n]))}function wp(e,t,n){return Sp(e,t,t.segments,n)}function Sp(e,t,n,r){if(e.segments.length>n.length){let o=e.segments.slice(0,n.length);return!(!Ft(o,n)||t.hasChildren()||!xi(o,n,r))}else if(e.segments.length===n.length){if(!Ft(e.segments,n)||!xi(e.segments,n,r))return!1;for(let o in t.children)if(!e.children[o]||!wp(e.children[o],t.children[o],r))return!1;return!0}else{let o=n.slice(0,e.segments.length),i=n.slice(e.segments.length);return!Ft(e.segments,o)||!xi(e.segments,o,r)||!e.children[T]?!1:Sp(e.children[T],t,i,r)}}function xi(e,t,n){return t.every((r,o)=>Ep[n](e[o].parameters,r.parameters))}var Ue=class{root;queryParams;fragment;_queryParamMap;constructor(t=new V([],{}),n={},r=null){this.root=t,this.queryParams=n,this.fragment=r}get queryParamMap(){return this._queryParamMap??=Vt(this.queryParams),this._queryParamMap}toString(){return mE.serialize(this)}},V=class{segments;children;parent=null;constructor(t,n){this.segments=t,this.children=n,Object.values(n).forEach(r=>r.parent=this)}hasChildren(){return this.numberOfChildren>0}get numberOfChildren(){return Object.keys(this.children).length}toString(){return Ri(this)}},ht=class{path;parameters;_parameterMap;constructor(t,n){this.path=t,this.parameters=n}get parameterMap(){return this._parameterMap??=Vt(this.parameters),this._parameterMap}toString(){return Cp(this)}};function hE(e,t){return Ft(e,t)&&e.every((n,r)=>Ve(n.parameters,t[r].parameters))}function Ft(e,t){return e.length!==t.length?!1:e.every((n,r)=>n.path===t[r].path)}function gE(e,t){let n=[];return Object.entries(e.children).forEach(([r,o])=>{r===T&&(n=n.concat(t(o,r)))}),Object.entries(e.children).forEach(([r,o])=>{r!==T&&(n=n.concat(t(o,r)))}),n}var $r=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275prov=b({token:e,factory:()=>new Ht,providedIn:"root"})}return e})(),Ht=class{parse(t){let n=new Uc(t);return new Ue(n.parseRootSegment(),n.parseQueryParams(),n.parseFragment())}serialize(t){let n=`/${Mr(t.root,!0)}`,r=EE(t.queryParams),o=typeof t.fragment=="string"?`#${vE(t.fragment)}`:"";return`${n}${r}${o}`}},mE=new Ht;function Ri(e){return e.segments.map(t=>Cp(t)).join("/")}function Mr(e,t){if(!e.hasChildren())return Ri(e);if(t){let n=e.children[T]?Mr(e.children[T],!1):"",r=[];return Object.entries(e.children).forEach(([o,i])=>{o!==T&&r.push(`${o}:${Mr(i,!1)}`)}),r.length>0?`${n}(${r.join("//")})`:n}else{let n=gE(e,(r,o)=>o===T?[Mr(e.children[T],!1)]:[`${o}:${Mr(r,!1)}`]);return Object.keys(e.children).length===1&&e.children[T]!=null?`${Ri(e)}/${n[0]}`:`${Ri(e)}/(${n.join("//")})`}}function bp(e){return encodeURIComponent(e).replace(/%40/g,"@").replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",")}function _i(e){return bp(e).replace(/%3B/gi,";")}function vE(e){return encodeURI(e)}function Hc(e){return bp(e).replace(/\(/g,"%28").replace(/\)/g,"%29").replace(/%26/gi,"&")}function Ai(e){return decodeURIComponent(e)}function up(e){return Ai(e.replace(/\+/g,"%20"))}function Cp(e){return`${Hc(e.path)}${yE(e.parameters)}`}function yE(e){return Object.entries(e).map(([t,n])=>`;${Hc(t)}=${Hc(n)}`).join("")}function EE(e){let t=Object.entries(e).map(([n,r])=>Array.isArray(r)?r.map(o=>`${_i(n)}=${_i(o)}`).join("&"):`${_i(n)}=${_i(r)}`).filter(n=>n);return t.length?`?${t.join("&")}`:""}var IE=/^[^\/()?;#]+/;function kc(e){let t=e.match(IE);return t?t[0]:""}var wE=/^[^\/()?;=#]+/;function SE(e){let t=e.match(wE);return t?t[0]:""}var bE=/^[^=?&#]+/;function CE(e){let t=e.match(bE);return t?t[0]:""}var DE=/^[^&#]+/;function TE(e){let t=e.match(DE);return t?t[0]:""}var Uc=class{url;remaining;constructor(t){this.url=t,this.remaining=t}parseRootSegment(){return this.consumeOptional("/"),this.remaining===""||this.peekStartsWith("?")||this.peekStartsWith("#")?new V([],{}):new V([],this.parseChildren())}parseQueryParams(){let t={};if(this.consumeOptional("?"))do this.parseQueryParam(t);while(this.consumeOptional("&"));return t}parseFragment(){return this.consumeOptional("#")?decodeURIComponent(this.remaining):null}parseChildren(){if(this.remaining==="")return{};this.consumeOptional("/");let t=[];for(this.peekStartsWith("(")||t.push(this.parseSegment());this.peekStartsWith("/")&&!this.peekStartsWith("//")&&!this.peekStartsWith("/(");)this.capture("/"),t.push(this.parseSegment());let n={};this.peekStartsWith("/(")&&(this.capture("/"),n=this.parseParens(!0));let r={};return this.peekStartsWith("(")&&(r=this.parseParens(!1)),(t.length>0||Object.keys(n).length>0)&&(r[T]=new V(t,n)),r}parseSegment(){let t=kc(this.remaining);if(t===""&&this.peekStartsWith(";"))throw new I(4009,!1);return this.capture(t),new ht(Ai(t),this.parseMatrixParams())}parseMatrixParams(){let t={};for(;this.consumeOptional(";");)this.parseParam(t);return t}parseParam(t){let n=SE(this.remaining);if(!n)return;this.capture(n);let r="";if(this.consumeOptional("=")){let o=kc(this.remaining);o&&(r=o,this.capture(r))}t[Ai(n)]=Ai(r)}parseQueryParam(t){let n=CE(this.remaining);if(!n)return;this.capture(n);let r="";if(this.consumeOptional("=")){let s=TE(this.remaining);s&&(r=s,this.capture(r))}let o=up(n),i=up(r);if(t.hasOwnProperty(o)){let s=t[o];Array.isArray(s)||(s=[s],t[o]=s),s.push(i)}else t[o]=i}parseParens(t){let n={};for(this.capture("(");!this.consumeOptional(")")&&this.remaining.length>0;){let r=kc(this.remaining),o=this.remaining[r.length];if(o!=="/"&&o!==")"&&o!==";")throw new I(4010,!1);let i;r.indexOf(":")>-1?(i=r.slice(0,r.indexOf(":")),this.capture(i),this.capture(":")):t&&(i=T);let s=this.parseChildren();n[i]=Object.keys(s).length===1?s[T]:new V([],s),this.consumeOptional("//")}return n}peekStartsWith(t){return this.remaining.startsWith(t)}consumeOptional(t){return this.peekStartsWith(t)?(this.remaining=this.remaining.substring(t.length),!0):!1}capture(t){if(!this.consumeOptional(t))throw new I(4011,!1)}};function Dp(e){return e.segments.length>0?new V([],{[T]:e}):e}function Tp(e){let t={};for(let[r,o]of Object.entries(e.children)){let i=Tp(o);if(r===T&&i.segments.length===0&&i.hasChildren())for(let[s,a]of Object.entries(i.children))t[s]=a;else(i.segments.length>0||i.hasChildren())&&(t[r]=i)}let n=new V(e.segments,t);return ME(n)}function ME(e){if(e.numberOfChildren===1&&e.children[T]){let t=e.children[T];return new V(e.segments.concat(t.segments),t.children)}return e}function bn(e){return e instanceof Ue}function Mp(e,t,n=null,r=null){let o=_p(e);return Np(o,t,n,r)}function _p(e){let t;function n(i){let s={};for(let c of i.children){let l=n(c);s[c.outlet]=l}let a=new V(i.url,s);return i===e&&(t=a),a}let r=n(e.root),o=Dp(r);return t??o}function Np(e,t,n,r){let o=e;for(;o.parent;)o=o.parent;if(t.length===0)return Pc(o,o,o,n,r);let i=_E(t);if(i.toRoot())return Pc(o,o,new V([],{}),n,r);let s=NE(i,o,e),a=s.processChildren?Nr(s.segmentGroup,s.index,i.commands):Rp(s.segmentGroup,s.index,i.commands);return Pc(o,s.segmentGroup,a,n,r)}function Oi(e){return typeof e=="object"&&e!=null&&!e.outlets&&!e.segmentPath}function Ar(e){return typeof e=="object"&&e!=null&&e.outlets}function Pc(e,t,n,r,o){let i={};r&&Object.entries(r).forEach(([c,l])=>{i[c]=Array.isArray(l)?l.map(f=>`${f}`):`${l}`});let s;e===t?s=n:s=xp(e,t,n);let a=Dp(Tp(s));return new Ue(a,i,o)}function xp(e,t,n){let r={};return Object.entries(e.children).forEach(([o,i])=>{i===t?r[o]=n:r[o]=xp(i,t,n)}),new V(e.segments,r)}var ki=class{isAbsolute;numberOfDoubleDots;commands;constructor(t,n,r){if(this.isAbsolute=t,this.numberOfDoubleDots=n,this.commands=r,t&&r.length>0&&Oi(r[0]))throw new I(4003,!1);let o=r.find(Ar);if(o&&o!==yp(r))throw new I(4004,!1)}toRoot(){return this.isAbsolute&&this.commands.length===1&&this.commands[0]=="/"}};function _E(e){if(typeof e[0]=="string"&&e.length===1&&e[0]==="/")return new ki(!0,0,e);let t=0,n=!1,r=e.reduce((o,i,s)=>{if(typeof i=="object"&&i!=null){if(i.outlets){let a={};return Object.entries(i.outlets).forEach(([c,l])=>{a[c]=typeof l=="string"?l.split("/"):l}),[...o,{outlets:a}]}if(i.segmentPath)return[...o,i.segmentPath]}return typeof i!="string"?[...o,i]:s===0?(i.split("/").forEach((a,c)=>{c==0&&a==="."||(c==0&&a===""?n=!0:a===".."?t++:a!=""&&o.push(a))}),o):[...o,i]},[]);return new ki(n,t,r)}var wn=class{segmentGroup;processChildren;index;constructor(t,n,r){this.segmentGroup=t,this.processChildren=n,this.index=r}};function NE(e,t,n){if(e.isAbsolute)return new wn(t,!0,0);if(!n)return new wn(t,!1,NaN);if(n.parent===null)return new wn(n,!0,0);let r=Oi(e.commands[0])?0:1,o=n.segments.length-1+r;return xE(n,o,e.numberOfDoubleDots)}function xE(e,t,n){let r=e,o=t,i=n;for(;i>o;){if(i-=o,r=r.parent,!r)throw new I(4005,!1);o=r.segments.length}return new wn(r,!1,o-i)}function RE(e){return Ar(e[0])?e[0].outlets:{[T]:e}}function Rp(e,t,n){if(e??=new V([],{}),e.segments.length===0&&e.hasChildren())return Nr(e,t,n);let r=AE(e,t,n),o=n.slice(r.commandIndex);if(r.match&&r.pathIndex<e.segments.length){let i=new V(e.segments.slice(0,r.pathIndex),{});return i.children[T]=new V(e.segments.slice(r.pathIndex),e.children),Nr(i,0,o)}else return r.match&&o.length===0?new V(e.segments,{}):r.match&&!e.hasChildren()?$c(e,t,n):r.match?Nr(e,0,o):$c(e,t,n)}function Nr(e,t,n){if(n.length===0)return new V(e.segments,{});{let r=RE(n),o={};if(Object.keys(r).some(i=>i!==T)&&e.children[T]&&e.numberOfChildren===1&&e.children[T].segments.length===0){let i=Nr(e.children[T],t,n);return new V(e.segments,i.children)}return Object.entries(r).forEach(([i,s])=>{typeof s=="string"&&(s=[s]),s!==null&&(o[i]=Rp(e.children[i],t,s))}),Object.entries(e.children).forEach(([i,s])=>{r[i]===void 0&&(o[i]=s)}),new V(e.segments,o)}}function AE(e,t,n){let r=0,o=t,i={match:!1,pathIndex:0,commandIndex:0};for(;o<e.segments.length;){if(r>=n.length)return i;let s=e.segments[o],a=n[r];if(Ar(a))break;let c=`${a}`,l=r<n.length-1?n[r+1]:null;if(o>0&&c===void 0)break;if(c&&l&&typeof l=="object"&&l.outlets===void 0){if(!fp(c,l,s))return i;r+=2}else{if(!fp(c,{},s))return i;r++}o++}return{match:!0,pathIndex:o,commandIndex:r}}function $c(e,t,n){let r=e.segments.slice(0,t),o=0;for(;o<n.length;){let i=n[o];if(Ar(i)){let c=OE(i.outlets);return new V(r,c)}if(o===0&&Oi(n[0])){let c=e.segments[t];r.push(new ht(c.path,dp(n[0]))),o++;continue}let s=Ar(i)?i.outlets[T]:`${i}`,a=o<n.length-1?n[o+1]:null;s&&a&&Oi(a)?(r.push(new ht(s,dp(a))),o+=2):(r.push(new ht(s,{})),o++)}return new V(r,{})}function OE(e){let t={};return Object.entries(e).forEach(([n,r])=>{typeof r=="string"&&(r=[r]),r!==null&&(t[n]=$c(new V([],{}),0,r))}),t}function dp(e){let t={};return Object.entries(e).forEach(([n,r])=>t[n]=`${r}`),t}function fp(e,t,n){return e==n.path&&Ve(t,n.parameters)}var xr="imperative",X=function(e){return e[e.NavigationStart=0]="NavigationStart",e[e.NavigationEnd=1]="NavigationEnd",e[e.NavigationCancel=2]="NavigationCancel",e[e.NavigationError=3]="NavigationError",e[e.RoutesRecognized=4]="RoutesRecognized",e[e.ResolveStart=5]="ResolveStart",e[e.ResolveEnd=6]="ResolveEnd",e[e.GuardsCheckStart=7]="GuardsCheckStart",e[e.GuardsCheckEnd=8]="GuardsCheckEnd",e[e.RouteConfigLoadStart=9]="RouteConfigLoadStart",e[e.RouteConfigLoadEnd=10]="RouteConfigLoadEnd",e[e.ChildActivationStart=11]="ChildActivationStart",e[e.ChildActivationEnd=12]="ChildActivationEnd",e[e.ActivationStart=13]="ActivationStart",e[e.ActivationEnd=14]="ActivationEnd",e[e.Scroll=15]="Scroll",e[e.NavigationSkipped=16]="NavigationSkipped",e}(X||{}),ye=class{id;url;constructor(t,n){this.id=t,this.url=n}},Ut=class extends ye{type=X.NavigationStart;navigationTrigger;restoredState;constructor(t,n,r="imperative",o=null){super(t,n),this.navigationTrigger=r,this.restoredState=o}toString(){return`NavigationStart(id: ${this.id}, url: '${this.url}')`}},et=class extends ye{urlAfterRedirects;type=X.NavigationEnd;constructor(t,n,r){super(t,n),this.urlAfterRedirects=r}toString(){return`NavigationEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}')`}},ae=function(e){return e[e.Redirect=0]="Redirect",e[e.SupersededByNewNavigation=1]="SupersededByNewNavigation",e[e.NoDataFromResolver=2]="NoDataFromResolver",e[e.GuardRejected=3]="GuardRejected",e[e.Aborted=4]="Aborted",e}(ae||{}),Or=function(e){return e[e.IgnoredSameUrlNavigation=0]="IgnoredSameUrlNavigation",e[e.IgnoredByUrlHandlingStrategy=1]="IgnoredByUrlHandlingStrategy",e}(Or||{}),He=class extends ye{reason;code;type=X.NavigationCancel;constructor(t,n,r,o){super(t,n),this.reason=r,this.code=o}toString(){return`NavigationCancel(id: ${this.id}, url: '${this.url}')`}},tt=class extends ye{reason;code;type=X.NavigationSkipped;constructor(t,n,r,o){super(t,n),this.reason=r,this.code=o}},Cn=class extends ye{error;target;type=X.NavigationError;constructor(t,n,r,o){super(t,n),this.error=r,this.target=o}toString(){return`NavigationError(id: ${this.id}, url: '${this.url}', error: ${this.error})`}},kr=class extends ye{urlAfterRedirects;state;type=X.RoutesRecognized;constructor(t,n,r,o){super(t,n),this.urlAfterRedirects=r,this.state=o}toString(){return`RoutesRecognized(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},Pi=class extends ye{urlAfterRedirects;state;type=X.GuardsCheckStart;constructor(t,n,r,o){super(t,n),this.urlAfterRedirects=r,this.state=o}toString(){return`GuardsCheckStart(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},Li=class extends ye{urlAfterRedirects;state;shouldActivate;type=X.GuardsCheckEnd;constructor(t,n,r,o,i){super(t,n),this.urlAfterRedirects=r,this.state=o,this.shouldActivate=i}toString(){return`GuardsCheckEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state}, shouldActivate: ${this.shouldActivate})`}},Fi=class extends ye{urlAfterRedirects;state;type=X.ResolveStart;constructor(t,n,r,o){super(t,n),this.urlAfterRedirects=r,this.state=o}toString(){return`ResolveStart(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},ji=class extends ye{urlAfterRedirects;state;type=X.ResolveEnd;constructor(t,n,r,o){super(t,n),this.urlAfterRedirects=r,this.state=o}toString(){return`ResolveEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},Vi=class{route;type=X.RouteConfigLoadStart;constructor(t){this.route=t}toString(){return`RouteConfigLoadStart(path: ${this.route.path})`}},Hi=class{route;type=X.RouteConfigLoadEnd;constructor(t){this.route=t}toString(){return`RouteConfigLoadEnd(path: ${this.route.path})`}},Ui=class{snapshot;type=X.ChildActivationStart;constructor(t){this.snapshot=t}toString(){return`ChildActivationStart(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},$i=class{snapshot;type=X.ChildActivationEnd;constructor(t){this.snapshot=t}toString(){return`ChildActivationEnd(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},Bi=class{snapshot;type=X.ActivationStart;constructor(t){this.snapshot=t}toString(){return`ActivationStart(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},zi=class{snapshot;type=X.ActivationEnd;constructor(t){this.snapshot=t}toString(){return`ActivationEnd(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}};var Pr=class{},Dn=class{url;navigationBehaviorOptions;constructor(t,n){this.url=t,this.navigationBehaviorOptions=n}};function kE(e){return!(e instanceof Pr)&&!(e instanceof Dn)}function PE(e,t){return e.providers&&!e._injector&&(e._injector=gr(e.providers,t,`Route: ${e.path}`)),e._injector??t}function Ae(e){return e.outlet||T}function LE(e,t){let n=e.filter(r=>Ae(r)===t);return n.push(...e.filter(r=>Ae(r)!==t)),n}function _n(e){if(!e)return null;if(e.routeConfig?._injector)return e.routeConfig._injector;for(let t=e.parent;t;t=t.parent){let n=t.routeConfig;if(n?._loadedInjector)return n._loadedInjector;if(n?._injector)return n._injector}return null}var Gi=class{rootInjector;outlet=null;route=null;children;attachRef=null;get injector(){return _n(this.route?.snapshot)??this.rootInjector}constructor(t){this.rootInjector=t,this.children=new Nn(this.rootInjector)}},Nn=(()=>{class e{rootInjector;contexts=new Map;constructor(n){this.rootInjector=n}onChildOutletCreated(n,r){let o=this.getOrCreateContext(n);o.outlet=r,this.contexts.set(n,o)}onChildOutletDestroyed(n){let r=this.getContext(n);r&&(r.outlet=null,r.attachRef=null)}onOutletDeactivated(){let n=this.contexts;return this.contexts=new Map,n}onOutletReAttached(n){this.contexts=n}getOrCreateContext(n){let r=this.getContext(n);return r||(r=new Gi(this.rootInjector),this.contexts.set(n,r)),r}getContext(n){return this.contexts.get(n)||null}static \u0275fac=function(r){return new(r||e)(M(ne))};static \u0275prov=b({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),qi=class{_root;constructor(t){this._root=t}get root(){return this._root.value}parent(t){let n=this.pathFromRoot(t);return n.length>1?n[n.length-2]:null}children(t){let n=Bc(t,this._root);return n?n.children.map(r=>r.value):[]}firstChild(t){let n=Bc(t,this._root);return n&&n.children.length>0?n.children[0].value:null}siblings(t){let n=zc(t,this._root);return n.length<2?[]:n[n.length-2].children.map(o=>o.value).filter(o=>o!==t)}pathFromRoot(t){return zc(t,this._root).map(n=>n.value)}};function Bc(e,t){if(e===t.value)return t;for(let n of t.children){let r=Bc(e,n);if(r)return r}return null}function zc(e,t){if(e===t.value)return[t];for(let n of t.children){let r=zc(e,n);if(r.length)return r.unshift(t),r}return[]}var ve=class{value;children;constructor(t,n){this.value=t,this.children=n}toString(){return`TreeNode(${this.value})`}};function In(e){let t={};return e&&e.children.forEach(n=>t[n.value.outlet]=n),t}var Lr=class extends qi{snapshot;constructor(t,n){super(t),this.snapshot=n,Jc(this,t)}toString(){return this.snapshot.toString()}};function Ap(e){let t=FE(e),n=new te([new ht("",{})]),r=new te({}),o=new te({}),i=new te({}),s=new te(""),a=new gt(n,r,i,s,o,T,e,t.root);return a.snapshot=t.root,new Lr(new ve(a,[]),t)}function FE(e){let t={},n={},r={},o="",i=new jt([],t,r,o,n,T,e,null,{});return new Fr("",new ve(i,[]))}var gt=class{urlSubject;paramsSubject;queryParamsSubject;fragmentSubject;dataSubject;outlet;component;snapshot;_futureSnapshot;_routerState;_paramMap;_queryParamMap;title;url;params;queryParams;fragment;data;constructor(t,n,r,o,i,s,a,c){this.urlSubject=t,this.paramsSubject=n,this.queryParamsSubject=r,this.fragmentSubject=o,this.dataSubject=i,this.outlet=s,this.component=a,this._futureSnapshot=c,this.title=this.dataSubject?.pipe(A(l=>l[Ur]))??C(void 0),this.url=t,this.params=n,this.queryParams=r,this.fragment=o,this.data=i}get routeConfig(){return this._futureSnapshot.routeConfig}get root(){return this._routerState.root}get parent(){return this._routerState.parent(this)}get firstChild(){return this._routerState.firstChild(this)}get children(){return this._routerState.children(this)}get pathFromRoot(){return this._routerState.pathFromRoot(this)}get paramMap(){return this._paramMap??=this.params.pipe(A(t=>Vt(t))),this._paramMap}get queryParamMap(){return this._queryParamMap??=this.queryParams.pipe(A(t=>Vt(t))),this._queryParamMap}toString(){return this.snapshot?this.snapshot.toString():`Future(${this._futureSnapshot})`}};function Wi(e,t,n="emptyOnly"){let r,{routeConfig:o}=e;return t!==null&&(n==="always"||o?.path===""||!t.component&&!t.routeConfig?.loadComponent)?r={params:E(E({},t.params),e.params),data:E(E({},t.data),e.data),resolve:E(E(E(E({},e.data),t.data),o?.data),e._resolvedData)}:r={params:E({},e.params),data:E({},e.data),resolve:E(E({},e.data),e._resolvedData??{})},o&&kp(o)&&(r.resolve[Ur]=o.title),r}var jt=class{url;params;queryParams;fragment;data;outlet;component;routeConfig;_resolve;_resolvedData;_routerState;_paramMap;_queryParamMap;get title(){return this.data?.[Ur]}constructor(t,n,r,o,i,s,a,c,l){this.url=t,this.params=n,this.queryParams=r,this.fragment=o,this.data=i,this.outlet=s,this.component=a,this.routeConfig=c,this._resolve=l}get root(){return this._routerState.root}get parent(){return this._routerState.parent(this)}get firstChild(){return this._routerState.firstChild(this)}get children(){return this._routerState.children(this)}get pathFromRoot(){return this._routerState.pathFromRoot(this)}get paramMap(){return this._paramMap??=Vt(this.params),this._paramMap}get queryParamMap(){return this._queryParamMap??=Vt(this.queryParams),this._queryParamMap}toString(){let t=this.url.map(r=>r.toString()).join("/"),n=this.routeConfig?this.routeConfig.path:"";return`Route(url:'${t}', path:'${n}')`}},Fr=class extends qi{url;constructor(t,n){super(n),this.url=t,Jc(this,n)}toString(){return Op(this._root)}};function Jc(e,t){t.value._routerState=e,t.children.forEach(n=>Jc(e,n))}function Op(e){let t=e.children.length>0?` { ${e.children.map(Op).join(", ")} } `:"";return`${e.value}${t}`}function Lc(e){if(e.snapshot){let t=e.snapshot,n=e._futureSnapshot;e.snapshot=n,Ve(t.queryParams,n.queryParams)||e.queryParamsSubject.next(n.queryParams),t.fragment!==n.fragment&&e.fragmentSubject.next(n.fragment),Ve(t.params,n.params)||e.paramsSubject.next(n.params),uE(t.url,n.url)||e.urlSubject.next(n.url),Ve(t.data,n.data)||e.dataSubject.next(n.data)}else e.snapshot=e._futureSnapshot,e.dataSubject.next(e._futureSnapshot.data)}function Gc(e,t){let n=Ve(e.params,t.params)&&hE(e.url,t.url),r=!e.parent!=!t.parent;return n&&!r&&(!e.parent||Gc(e.parent,t.parent))}function kp(e){return typeof e.title=="string"||e.title===null}var Pp=new w(""),Br=(()=>{class e{activated=null;get activatedComponentRef(){return this.activated}_activatedRoute=null;name=T;activateEvents=new se;deactivateEvents=new se;attachEvents=new se;detachEvents=new se;routerOutletData=Uf(void 0);parentContexts=v(Nn);location=v(yi);changeDetector=v(bi);inputBinder=v(Ki,{optional:!0});supportsBindingToComponentInputs=!0;ngOnChanges(n){if(n.name){let{firstChange:r,previousValue:o}=n.name;if(r)return;this.isTrackedInParentContexts(o)&&(this.deactivate(),this.parentContexts.onChildOutletDestroyed(o)),this.initializeOutletWithName()}}ngOnDestroy(){this.isTrackedInParentContexts(this.name)&&this.parentContexts.onChildOutletDestroyed(this.name),this.inputBinder?.unsubscribeFromRouteData(this)}isTrackedInParentContexts(n){return this.parentContexts.getContext(n)?.outlet===this}ngOnInit(){this.initializeOutletWithName()}initializeOutletWithName(){if(this.parentContexts.onChildOutletCreated(this.name,this),this.activated)return;let n=this.parentContexts.getContext(this.name);n?.route&&(n.attachRef?this.attach(n.attachRef,n.route):this.activateWith(n.route,n.injector))}get isActivated(){return!!this.activated}get component(){if(!this.activated)throw new I(4012,!1);return this.activated.instance}get activatedRoute(){if(!this.activated)throw new I(4012,!1);return this._activatedRoute}get activatedRouteData(){return this._activatedRoute?this._activatedRoute.snapshot.data:{}}detach(){if(!this.activated)throw new I(4012,!1);this.location.detach();let n=this.activated;return this.activated=null,this._activatedRoute=null,this.detachEvents.emit(n.instance),n}attach(n,r){this.activated=n,this._activatedRoute=r,this.location.insert(n.hostView),this.inputBinder?.bindActivatedRouteToOutletComponent(this),this.attachEvents.emit(n.instance)}deactivate(){if(this.activated){let n=this.component;this.activated.destroy(),this.activated=null,this._activatedRoute=null,this.deactivateEvents.emit(n)}}activateWith(n,r){if(this.isActivated)throw new I(4013,!1);this._activatedRoute=n;let o=this.location,s=n.snapshot.component,a=this.parentContexts.getOrCreateContext(this.name).children,c=new qc(n,a,o.injector,this.routerOutletData);this.activated=o.createComponent(s,{index:o.length,injector:c,environmentInjector:r}),this.changeDetector.markForCheck(),this.inputBinder?.bindActivatedRouteToOutletComponent(this),this.activateEvents.emit(this.activated.instance)}static \u0275fac=function(r){return new(r||e)};static \u0275dir=mr({type:e,selectors:[["router-outlet"]],inputs:{name:"name",routerOutletData:[1,"routerOutletData"]},outputs:{activateEvents:"activate",deactivateEvents:"deactivate",attachEvents:"attach",detachEvents:"detach"},exportAs:["outlet"],features:[pr]})}return e})(),qc=class{route;childContexts;parent;outletData;constructor(t,n,r,o){this.route=t,this.childContexts=n,this.parent=r,this.outletData=o}get(t,n){return t===gt?this.route:t===Nn?this.childContexts:t===Pp?this.outletData:this.parent.get(t,n)}},Ki=new w("");var Xc=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275cmp=Pt({type:e,selectors:[["ng-component"]],exportAs:["emptyRouterOutlet"],decls:1,vars:0,template:function(r,o){r&1&&m(0,"router-outlet")},dependencies:[Br],encapsulation:2})}return e})();function el(e){let t=e.children&&e.children.map(el),n=t?z(E({},e),{children:t}):E({},e);return!n.component&&!n.loadComponent&&(t||n.loadChildren)&&n.outlet&&n.outlet!==T&&(n.component=Xc),n}function jE(e,t,n){let r=jr(e,t._root,n?n._root:void 0);return new Lr(r,t)}function jr(e,t,n){if(n&&e.shouldReuseRoute(t.value,n.value.snapshot)){let r=n.value;r._futureSnapshot=t.value;let o=VE(e,t,n);return new ve(r,o)}else{if(e.shouldAttach(t.value)){let i=e.retrieve(t.value);if(i!==null){let s=i.route;return s.value._futureSnapshot=t.value,s.children=t.children.map(a=>jr(e,a)),s}}let r=HE(t.value),o=t.children.map(i=>jr(e,i));return new ve(r,o)}}function VE(e,t,n){return t.children.map(r=>{for(let o of n.children)if(e.shouldReuseRoute(r.value,o.value.snapshot))return jr(e,r,o);return jr(e,r)})}function HE(e){return new gt(new te(e.url),new te(e.params),new te(e.queryParams),new te(e.fragment),new te(e.data),e.outlet,e.component,e)}var Tn=class{redirectTo;navigationBehaviorOptions;constructor(t,n){this.redirectTo=t,this.navigationBehaviorOptions=n}},Lp="ngNavigationCancelingError";function Zi(e,t){let{redirectTo:n,navigationBehaviorOptions:r}=bn(t)?{redirectTo:t,navigationBehaviorOptions:void 0}:t,o=Fp(!1,ae.Redirect);return o.url=n,o.navigationBehaviorOptions=r,o}function Fp(e,t){let n=new Error(`NavigationCancelingError: ${e||""}`);return n[Lp]=!0,n.cancellationCode=t,n}function UE(e){return jp(e)&&bn(e.url)}function jp(e){return!!e&&e[Lp]}var $E=(e,t,n,r)=>A(o=>(new Wc(t,o.targetRouterState,o.currentRouterState,n,r).activate(e),o)),Wc=class{routeReuseStrategy;futureState;currState;forwardEvent;inputBindingEnabled;constructor(t,n,r,o,i){this.routeReuseStrategy=t,this.futureState=n,this.currState=r,this.forwardEvent=o,this.inputBindingEnabled=i}activate(t){let n=this.futureState._root,r=this.currState?this.currState._root:null;this.deactivateChildRoutes(n,r,t),Lc(this.futureState.root),this.activateChildRoutes(n,r,t)}deactivateChildRoutes(t,n,r){let o=In(n);t.children.forEach(i=>{let s=i.value.outlet;this.deactivateRoutes(i,o[s],r),delete o[s]}),Object.values(o).forEach(i=>{this.deactivateRouteAndItsChildren(i,r)})}deactivateRoutes(t,n,r){let o=t.value,i=n?n.value:null;if(o===i)if(o.component){let s=r.getContext(o.outlet);s&&this.deactivateChildRoutes(t,n,s.children)}else this.deactivateChildRoutes(t,n,r);else i&&this.deactivateRouteAndItsChildren(n,r)}deactivateRouteAndItsChildren(t,n){t.value.component&&this.routeReuseStrategy.shouldDetach(t.value.snapshot)?this.detachAndStoreRouteSubtree(t,n):this.deactivateRouteAndOutlet(t,n)}detachAndStoreRouteSubtree(t,n){let r=n.getContext(t.value.outlet),o=r&&t.value.component?r.children:n,i=In(t);for(let s of Object.values(i))this.deactivateRouteAndItsChildren(s,o);if(r&&r.outlet){let s=r.outlet.detach(),a=r.children.onOutletDeactivated();this.routeReuseStrategy.store(t.value.snapshot,{componentRef:s,route:t,contexts:a})}}deactivateRouteAndOutlet(t,n){let r=n.getContext(t.value.outlet),o=r&&t.value.component?r.children:n,i=In(t);for(let s of Object.values(i))this.deactivateRouteAndItsChildren(s,o);r&&(r.outlet&&(r.outlet.deactivate(),r.children.onOutletDeactivated()),r.attachRef=null,r.route=null)}activateChildRoutes(t,n,r){let o=In(n);t.children.forEach(i=>{this.activateRoutes(i,o[i.value.outlet],r),this.forwardEvent(new zi(i.value.snapshot))}),t.children.length&&this.forwardEvent(new $i(t.value.snapshot))}activateRoutes(t,n,r){let o=t.value,i=n?n.value:null;if(Lc(o),o===i)if(o.component){let s=r.getOrCreateContext(o.outlet);this.activateChildRoutes(t,n,s.children)}else this.activateChildRoutes(t,n,r);else if(o.component){let s=r.getOrCreateContext(o.outlet);if(this.routeReuseStrategy.shouldAttach(o.snapshot)){let a=this.routeReuseStrategy.retrieve(o.snapshot);this.routeReuseStrategy.store(o.snapshot,null),s.children.onOutletReAttached(a.contexts),s.attachRef=a.componentRef,s.route=a.route.value,s.outlet&&s.outlet.attach(a.componentRef,a.route.value),Lc(a.route.value),this.activateChildRoutes(t,null,s.children)}else s.attachRef=null,s.route=o,s.outlet&&s.outlet.activateWith(o,s.injector),this.activateChildRoutes(t,null,s.children)}else this.activateChildRoutes(t,null,r)}},Qi=class{path;route;constructor(t){this.path=t,this.route=this.path[this.path.length-1]}},Sn=class{component;route;constructor(t,n){this.component=t,this.route=n}};function BE(e,t,n){let r=e._root,o=t?t._root:null;return _r(r,o,n,[r.value])}function zE(e){let t=e.routeConfig?e.routeConfig.canActivateChild:null;return!t||t.length===0?null:{node:e,guards:t}}function xn(e,t){let n=Symbol(),r=t.get(e,n);return r===n?typeof e=="function"&&!Hs(e)?e:t.get(e):r}function _r(e,t,n,r,o={canDeactivateChecks:[],canActivateChecks:[]}){let i=In(t);return e.children.forEach(s=>{GE(s,i[s.value.outlet],n,r.concat([s.value]),o),delete i[s.value.outlet]}),Object.entries(i).forEach(([s,a])=>Rr(a,n.getContext(s),o)),o}function GE(e,t,n,r,o={canDeactivateChecks:[],canActivateChecks:[]}){let i=e.value,s=t?t.value:null,a=n?n.getContext(e.value.outlet):null;if(s&&i.routeConfig===s.routeConfig){let c=qE(s,i,i.routeConfig.runGuardsAndResolvers);c?o.canActivateChecks.push(new Qi(r)):(i.data=s.data,i._resolvedData=s._resolvedData),i.component?_r(e,t,a?a.children:null,r,o):_r(e,t,n,r,o),c&&a&&a.outlet&&a.outlet.isActivated&&o.canDeactivateChecks.push(new Sn(a.outlet.component,s))}else s&&Rr(t,a,o),o.canActivateChecks.push(new Qi(r)),i.component?_r(e,null,a?a.children:null,r,o):_r(e,null,n,r,o);return o}function qE(e,t,n){if(typeof n=="function")return n(e,t);switch(n){case"pathParamsChange":return!Ft(e.url,t.url);case"pathParamsOrQueryParamsChange":return!Ft(e.url,t.url)||!Ve(e.queryParams,t.queryParams);case"always":return!0;case"paramsOrQueryParamsChange":return!Gc(e,t)||!Ve(e.queryParams,t.queryParams);case"paramsChange":default:return!Gc(e,t)}}function Rr(e,t,n){let r=In(e),o=e.value;Object.entries(r).forEach(([i,s])=>{o.component?t?Rr(s,t.children.getContext(i),n):Rr(s,null,n):Rr(s,t,n)}),o.component?t&&t.outlet&&t.outlet.isActivated?n.canDeactivateChecks.push(new Sn(t.outlet.component,o)):n.canDeactivateChecks.push(new Sn(null,o)):n.canDeactivateChecks.push(new Sn(null,o))}function zr(e){return typeof e=="function"}function WE(e){return typeof e=="boolean"}function ZE(e){return e&&zr(e.canLoad)}function QE(e){return e&&zr(e.canActivate)}function YE(e){return e&&zr(e.canActivateChild)}function KE(e){return e&&zr(e.canDeactivate)}function JE(e){return e&&zr(e.canMatch)}function Vp(e){return e instanceof Be||e?.name==="EmptyError"}var Ni=Symbol("INITIAL_VALUE");function Mn(){return Ie(e=>So(e.map(t=>t.pipe(ze(1),Cs(Ni)))).pipe(A(t=>{for(let n of t)if(n!==!0){if(n===Ni)return Ni;if(n===!1||XE(n))return n}return!0}),Ee(t=>t!==Ni),ze(1)))}function XE(e){return bn(e)||e instanceof Tn}function eI(e,t){return Z(n=>{let{targetSnapshot:r,currentSnapshot:o,guards:{canActivateChecks:i,canDeactivateChecks:s}}=n;return s.length===0&&i.length===0?C(z(E({},n),{guardsResult:!0})):tI(s,r,o,e).pipe(Z(a=>a&&WE(a)?nI(r,i,e,t):C(a)),A(a=>z(E({},n),{guardsResult:a})))})}function tI(e,t,n,r){return q(e).pipe(Z(o=>aI(o.component,o.route,n,t,r)),Ge(o=>o!==!0,!0))}function nI(e,t,n,r){return q(t).pipe(Jt(o=>Kt(oI(o.route.parent,r),rI(o.route,r),sI(e,o.path,n),iI(e,o.route,n))),Ge(o=>o!==!0,!0))}function rI(e,t){return e!==null&&t&&t(new Bi(e)),C(!0)}function oI(e,t){return e!==null&&t&&t(new Ui(e)),C(!0)}function iI(e,t,n){let r=t.routeConfig?t.routeConfig.canActivate:null;if(!r||r.length===0)return C(!0);let o=r.map(i=>Pn(()=>{let s=_n(t)??n,a=xn(i,s),c=QE(a)?a.canActivate(t,e):oe(s,()=>a(t,e));return nt(c).pipe(Ge())}));return C(o).pipe(Mn())}function sI(e,t,n){let r=t[t.length-1],i=t.slice(0,t.length-1).reverse().map(s=>zE(s)).filter(s=>s!==null).map(s=>Pn(()=>{let a=s.guards.map(c=>{let l=_n(s.node)??n,f=xn(c,l),p=YE(f)?f.canActivateChild(r,e):oe(l,()=>f(r,e));return nt(p).pipe(Ge())});return C(a).pipe(Mn())}));return C(i).pipe(Mn())}function aI(e,t,n,r,o){let i=t&&t.routeConfig?t.routeConfig.canDeactivate:null;if(!i||i.length===0)return C(!0);let s=i.map(a=>{let c=_n(t)??o,l=xn(a,c),f=KE(l)?l.canDeactivate(e,t,n,r):oe(c,()=>l(e,t,n,r));return nt(f).pipe(Ge())});return C(s).pipe(Mn())}function cI(e,t,n,r){let o=t.canLoad;if(o===void 0||o.length===0)return C(!0);let i=o.map(s=>{let a=xn(s,e),c=ZE(a)?a.canLoad(t,n):oe(e,()=>a(t,n));return nt(c)});return C(i).pipe(Mn(),Hp(r))}function Hp(e){return vs(J(t=>{if(typeof t!="boolean")throw Zi(e,t)}),A(t=>t===!0))}function lI(e,t,n,r){let o=t.canMatch;if(!o||o.length===0)return C(!0);let i=o.map(s=>{let a=xn(s,e),c=JE(a)?a.canMatch(t,n):oe(e,()=>a(t,n));return nt(c)});return C(i).pipe(Mn(),Hp(r))}var Vr=class{segmentGroup;constructor(t){this.segmentGroup=t||null}},Hr=class extends Error{urlTree;constructor(t){super(),this.urlTree=t}};function En(e){return Yt(new Vr(e))}function uI(e){return Yt(new I(4e3,!1))}function dI(e){return Yt(Fp(!1,ae.GuardRejected))}var Zc=class{urlSerializer;urlTree;constructor(t,n){this.urlSerializer=t,this.urlTree=n}lineralizeSegments(t,n){let r=[],o=n.root;for(;;){if(r=r.concat(o.segments),o.numberOfChildren===0)return C(r);if(o.numberOfChildren>1||!o.children[T])return uI(`${t.redirectTo}`);o=o.children[T]}}applyRedirectCommands(t,n,r,o,i){return fI(n,o,i).pipe(A(s=>{if(s instanceof Ue)throw new Hr(s);let a=this.applyRedirectCreateUrlTree(s,this.urlSerializer.parse(s),t,r);if(s[0]==="/")throw new Hr(a);return a}))}applyRedirectCreateUrlTree(t,n,r,o){let i=this.createSegmentGroup(t,n.root,r,o);return new Ue(i,this.createQueryParams(n.queryParams,this.urlTree.queryParams),n.fragment)}createQueryParams(t,n){let r={};return Object.entries(t).forEach(([o,i])=>{if(typeof i=="string"&&i[0]===":"){let a=i.substring(1);r[o]=n[a]}else r[o]=i}),r}createSegmentGroup(t,n,r,o){let i=this.createSegments(t,n.segments,r,o),s={};return Object.entries(n.children).forEach(([a,c])=>{s[a]=this.createSegmentGroup(t,c,r,o)}),new V(i,s)}createSegments(t,n,r,o){return n.map(i=>i.path[0]===":"?this.findPosParam(t,i,o):this.findOrReturn(i,r))}findPosParam(t,n,r){let o=r[n.path.substring(1)];if(!o)throw new I(4001,!1);return o}findOrReturn(t,n){let r=0;for(let o of n){if(o.path===t.path)return n.splice(r),o;r++}return t}};function fI(e,t,n){if(typeof e=="string")return C(e);let r=e,{queryParams:o,fragment:i,routeConfig:s,url:a,outlet:c,params:l,data:f,title:p}=t;return nt(oe(n,()=>r({params:l,data:f,queryParams:o,fragment:i,routeConfig:s,url:a,outlet:c,title:p})))}var Qc={matched:!1,consumedSegments:[],remainingSegments:[],parameters:{},positionalParamSegments:{}};function pI(e,t,n,r,o){let i=Up(e,t,n);return i.matched?(r=PE(t,r),lI(r,t,n,o).pipe(A(s=>s===!0?i:E({},Qc)))):C(i)}function Up(e,t,n){if(t.path==="**")return hI(n);if(t.path==="")return t.pathMatch==="full"&&(e.hasChildren()||n.length>0)?E({},Qc):{matched:!0,consumedSegments:[],remainingSegments:n,parameters:{},positionalParamSegments:{}};let o=(t.matcher||mp)(n,e,t);if(!o)return E({},Qc);let i={};Object.entries(o.posParams??{}).forEach(([a,c])=>{i[a]=c.path});let s=o.consumed.length>0?E(E({},i),o.consumed[o.consumed.length-1].parameters):i;return{matched:!0,consumedSegments:o.consumed,remainingSegments:n.slice(o.consumed.length),parameters:s,positionalParamSegments:o.posParams??{}}}function hI(e){return{matched:!0,parameters:e.length>0?yp(e).parameters:{},consumedSegments:e,remainingSegments:[],positionalParamSegments:{}}}function pp(e,t,n,r){return n.length>0&&vI(e,n,r)?{segmentGroup:new V(t,mI(r,new V(n,e.children))),slicedSegments:[]}:n.length===0&&yI(e,n,r)?{segmentGroup:new V(e.segments,gI(e,n,r,e.children)),slicedSegments:n}:{segmentGroup:new V(e.segments,e.children),slicedSegments:n}}function gI(e,t,n,r){let o={};for(let i of n)if(Ji(e,t,i)&&!r[Ae(i)]){let s=new V([],{});o[Ae(i)]=s}return E(E({},r),o)}function mI(e,t){let n={};n[T]=t;for(let r of e)if(r.path===""&&Ae(r)!==T){let o=new V([],{});n[Ae(r)]=o}return n}function vI(e,t,n){return n.some(r=>Ji(e,t,r)&&Ae(r)!==T)}function yI(e,t,n){return n.some(r=>Ji(e,t,r))}function Ji(e,t,n){return(e.hasChildren()||t.length>0)&&n.pathMatch==="full"?!1:n.path===""}function EI(e,t,n){return t.length===0&&!e.children[n]}var Yc=class{};function II(e,t,n,r,o,i,s="emptyOnly"){return new Kc(e,t,n,r,o,s,i).recognize()}var wI=31,Kc=class{injector;configLoader;rootComponentType;config;urlTree;paramsInheritanceStrategy;urlSerializer;applyRedirects;absoluteRedirectCount=0;allowRedirects=!0;constructor(t,n,r,o,i,s,a){this.injector=t,this.configLoader=n,this.rootComponentType=r,this.config=o,this.urlTree=i,this.paramsInheritanceStrategy=s,this.urlSerializer=a,this.applyRedirects=new Zc(this.urlSerializer,this.urlTree)}noMatchError(t){return new I(4002,`'${t.segmentGroup}'`)}recognize(){let t=pp(this.urlTree.root,[],[],this.config).segmentGroup;return this.match(t).pipe(A(({children:n,rootSnapshot:r})=>{let o=new ve(r,n),i=new Fr("",o),s=Mp(r,[],this.urlTree.queryParams,this.urlTree.fragment);return s.queryParams=this.urlTree.queryParams,i.url=this.urlSerializer.serialize(s),{state:i,tree:s}}))}match(t){let n=new jt([],Object.freeze({}),Object.freeze(E({},this.urlTree.queryParams)),this.urlTree.fragment,Object.freeze({}),T,this.rootComponentType,null,{});return this.processSegmentGroup(this.injector,this.config,t,T,n).pipe(A(r=>({children:r,rootSnapshot:n})),ot(r=>{if(r instanceof Hr)return this.urlTree=r.urlTree,this.match(r.urlTree.root);throw r instanceof Vr?this.noMatchError(r):r}))}processSegmentGroup(t,n,r,o,i){return r.segments.length===0&&r.hasChildren()?this.processChildren(t,n,r,i):this.processSegment(t,n,r,r.segments,o,!0,i).pipe(A(s=>s instanceof ve?[s]:[]))}processChildren(t,n,r,o){let i=[];for(let s of Object.keys(r.children))s==="primary"?i.unshift(s):i.push(s);return q(i).pipe(Jt(s=>{let a=r.children[s],c=LE(n,s);return this.processSegmentGroup(t,c,a,s,o)}),bs((s,a)=>(s.push(...a),s)),it(null),Ss(),Z(s=>{if(s===null)return En(r);let a=$p(s);return SI(a),C(a)}))}processSegment(t,n,r,o,i,s,a){return q(n).pipe(Jt(c=>this.processSegmentAgainstRoute(c._injector??t,n,c,r,o,i,s,a).pipe(ot(l=>{if(l instanceof Vr)return C(null);throw l}))),Ge(c=>!!c),ot(c=>{if(Vp(c))return EI(r,o,i)?C(new Yc):En(r);throw c}))}processSegmentAgainstRoute(t,n,r,o,i,s,a,c){return Ae(r)!==s&&(s===T||!Ji(o,i,r))?En(o):r.redirectTo===void 0?this.matchSegmentAgainstRoute(t,o,r,i,s,c):this.allowRedirects&&a?this.expandSegmentAgainstRouteUsingRedirect(t,o,n,r,i,s,c):En(o)}expandSegmentAgainstRouteUsingRedirect(t,n,r,o,i,s,a){let{matched:c,parameters:l,consumedSegments:f,positionalParamSegments:p,remainingSegments:y}=Up(n,o,i);if(!c)return En(n);typeof o.redirectTo=="string"&&o.redirectTo[0]==="/"&&(this.absoluteRedirectCount++,this.absoluteRedirectCount>wI&&(this.allowRedirects=!1));let h=new jt(i,l,Object.freeze(E({},this.urlTree.queryParams)),this.urlTree.fragment,hp(o),Ae(o),o.component??o._loadedComponent??null,o,gp(o)),S=Wi(h,a,this.paramsInheritanceStrategy);return h.params=Object.freeze(S.params),h.data=Object.freeze(S.data),this.applyRedirects.applyRedirectCommands(f,o.redirectTo,p,h,t).pipe(Ie($=>this.applyRedirects.lineralizeSegments(o,$)),Z($=>this.processSegment(t,r,n,$.concat(y),s,!1,a)))}matchSegmentAgainstRoute(t,n,r,o,i,s){let a=pI(n,r,o,t,this.urlSerializer);return r.path==="**"&&(n.children={}),a.pipe(Ie(c=>c.matched?(t=r._injector??t,this.getChildConfig(t,r,o).pipe(Ie(({routes:l})=>{let f=r._loadedInjector??t,{parameters:p,consumedSegments:y,remainingSegments:h}=c,S=new jt(y,p,Object.freeze(E({},this.urlTree.queryParams)),this.urlTree.fragment,hp(r),Ae(r),r.component??r._loadedComponent??null,r,gp(r)),O=Wi(S,s,this.paramsInheritanceStrategy);S.params=Object.freeze(O.params),S.data=Object.freeze(O.data);let{segmentGroup:$,slicedSegments:U}=pp(n,y,h,l);if(U.length===0&&$.hasChildren())return this.processChildren(f,l,$,S).pipe(A(Wr=>new ve(S,Wr)));if(l.length===0&&U.length===0)return C(new ve(S,[]));let th=Ae(r)===i;return this.processSegment(f,l,$,U,th?T:i,!0,S).pipe(A(Wr=>new ve(S,Wr instanceof ve?[Wr]:[])))}))):En(n)))}getChildConfig(t,n,r){return n.children?C({routes:n.children,injector:t}):n.loadChildren?n._loadedRoutes!==void 0?C({routes:n._loadedRoutes,injector:n._loadedInjector}):cI(t,n,r,this.urlSerializer).pipe(Z(o=>o?this.configLoader.loadChildren(t,n).pipe(J(i=>{n._loadedRoutes=i.routes,n._loadedInjector=i.injector})):dI(n))):C({routes:[],injector:t})}};function SI(e){e.sort((t,n)=>t.value.outlet===T?-1:n.value.outlet===T?1:t.value.outlet.localeCompare(n.value.outlet))}function bI(e){let t=e.value.routeConfig;return t&&t.path===""}function $p(e){let t=[],n=new Set;for(let r of e){if(!bI(r)){t.push(r);continue}let o=t.find(i=>r.value.routeConfig===i.value.routeConfig);o!==void 0?(o.children.push(...r.children),n.add(o)):t.push(r)}for(let r of n){let o=$p(r.children);t.push(new ve(r.value,o))}return t.filter(r=>!n.has(r))}function hp(e){return e.data||{}}function gp(e){return e.resolve||{}}function CI(e,t,n,r,o,i){return Z(s=>II(e,t,n,r,s.extractedUrl,o,i).pipe(A(({state:a,tree:c})=>z(E({},s),{targetSnapshot:a,urlAfterRedirects:c}))))}function DI(e,t){return Z(n=>{let{targetSnapshot:r,guards:{canActivateChecks:o}}=n;if(!o.length)return C(n);let i=new Set(o.map(c=>c.route)),s=new Set;for(let c of i)if(!s.has(c))for(let l of Bp(c))s.add(l);let a=0;return q(s).pipe(Jt(c=>i.has(c)?TI(c,r,e,t):(c.data=Wi(c,c.parent,e).resolve,C(void 0))),J(()=>a++),Xt(1),Z(c=>a===s.size?C(n):ce))})}function Bp(e){let t=e.children.map(n=>Bp(n)).flat();return[e,...t]}function TI(e,t,n,r){let o=e.routeConfig,i=e._resolve;return o?.title!==void 0&&!kp(o)&&(i[Ur]=o.title),Pn(()=>(e.data=Wi(e,e.parent,n).resolve,MI(i,e,t,r).pipe(A(s=>(e._resolvedData=s,e.data=E(E({},e.data),s),null)))))}function MI(e,t,n,r){let o=Vc(e);if(o.length===0)return C({});let i={};return q(o).pipe(Z(s=>_I(e[s],t,n,r).pipe(Ge(),J(a=>{if(a instanceof Tn)throw Zi(new Ht,a);i[s]=a}))),Xt(1),A(()=>i),ot(s=>Vp(s)?ce:Yt(s)))}function _I(e,t,n,r){let o=_n(t)??r,i=xn(e,o),s=i.resolve?i.resolve(t,n):oe(o,()=>i(t,n));return nt(s)}function Fc(e){return Ie(t=>{let n=e(t);return n?q(n).pipe(A(()=>t)):C(t)})}var tl=(()=>{class e{buildTitle(n){let r,o=n.root;for(;o!==void 0;)r=this.getResolvedTitleForRoute(o)??r,o=o.children.find(i=>i.outlet===T);return r}getResolvedTitleForRoute(n){return n.data[Ur]}static \u0275fac=function(r){return new(r||e)};static \u0275prov=b({token:e,factory:()=>v(zp),providedIn:"root"})}return e})(),zp=(()=>{class e extends tl{title;constructor(n){super(),this.title=n}updateTitle(n){let r=this.buildTitle(n);r!==void 0&&this.title.setTitle(r)}static \u0275fac=function(r){return new(r||e)(M(cp))};static \u0275prov=b({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),Gr=new w("",{providedIn:"root",factory:()=>({})}),qr=new w(""),Gp=(()=>{class e{componentLoaders=new WeakMap;childrenLoaders=new WeakMap;onLoadStartListener;onLoadEndListener;compiler=v(gc);loadComponent(n,r){if(this.componentLoaders.get(r))return this.componentLoaders.get(r);if(r._loadedComponent)return C(r._loadedComponent);this.onLoadStartListener&&this.onLoadStartListener(r);let o=nt(oe(n,()=>r.loadComponent())).pipe(A(Wp),J(s=>{this.onLoadEndListener&&this.onLoadEndListener(r),r._loadedComponent=s}),Ln(()=>{this.componentLoaders.delete(r)})),i=new Qt(o,()=>new ee).pipe(Zt());return this.componentLoaders.set(r,i),i}loadChildren(n,r){if(this.childrenLoaders.get(r))return this.childrenLoaders.get(r);if(r._loadedRoutes)return C({routes:r._loadedRoutes,injector:r._loadedInjector});this.onLoadStartListener&&this.onLoadStartListener(r);let i=qp(r,this.compiler,n,this.onLoadEndListener).pipe(Ln(()=>{this.childrenLoaders.delete(r)})),s=new Qt(i,()=>new ee).pipe(Zt());return this.childrenLoaders.set(r,s),s}static \u0275fac=function(r){return new(r||e)};static \u0275prov=b({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function qp(e,t,n,r){return nt(oe(n,()=>e.loadChildren())).pipe(A(Wp),Z(o=>o instanceof Ei||Array.isArray(o)?C(o):q(t.compileModuleAsync(o))),A(o=>{r&&r(e);let i,s,a=!1;return Array.isArray(o)?(s=o,a=!0):(i=o.create(n).injector,s=i.get(qr,[],{optional:!0,self:!0}).flat()),{routes:s.map(el),injector:i}}))}function NI(e){return e&&typeof e=="object"&&"default"in e}function Wp(e){return NI(e)?e.default:e}var Xi=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275prov=b({token:e,factory:()=>v(xI),providedIn:"root"})}return e})(),xI=(()=>{class e{shouldProcessUrl(n){return!0}extract(n){return n}merge(n,r){return n}static \u0275fac=function(r){return new(r||e)};static \u0275prov=b({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),Zp=new w("");var Qp=new w(""),Yp=(()=>{class e{currentNavigation=null;currentTransition=null;lastSuccessfulNavigation=null;events=new ee;transitionAbortWithErrorSubject=new ee;configLoader=v(Gp);environmentInjector=v(ne);destroyRef=v(xt);urlSerializer=v($r);rootContexts=v(Nn);location=v(yn);inputBindingEnabled=v(Ki,{optional:!0})!==null;titleStrategy=v(tl);options=v(Gr,{optional:!0})||{};paramsInheritanceStrategy=this.options.paramsInheritanceStrategy||"emptyOnly";urlHandlingStrategy=v(Xi);createViewTransition=v(Zp,{optional:!0});navigationErrorHandler=v(Qp,{optional:!0});navigationId=0;get hasRequestedNavigation(){return this.navigationId!==0}transitions;afterPreactivation=()=>C(void 0);rootComponentType=null;destroyed=!1;constructor(){let n=o=>this.events.next(new Vi(o)),r=o=>this.events.next(new Hi(o));this.configLoader.onLoadEndListener=r,this.configLoader.onLoadStartListener=n,this.destroyRef.onDestroy(()=>{this.destroyed=!0})}complete(){this.transitions?.complete()}handleNavigationRequest(n){let r=++this.navigationId;this.transitions?.next(z(E({},n),{extractedUrl:this.urlHandlingStrategy.extract(n.rawUrl),targetSnapshot:null,targetRouterState:null,guards:{canActivateChecks:[],canDeactivateChecks:[]},guardsResult:null,abortController:new AbortController,id:r}))}setupNavigations(n){return this.transitions=new te(null),this.transitions.pipe(Ee(r=>r!==null),Ie(r=>{let o=!1;return C(r).pipe(Ie(i=>{if(this.navigationId>r.id)return this.cancelNavigationTransition(r,"",ae.SupersededByNewNavigation),ce;this.currentTransition=r,this.currentNavigation={id:i.id,initialUrl:i.rawUrl,extractedUrl:i.extractedUrl,targetBrowserUrl:typeof i.extras.browserUrl=="string"?this.urlSerializer.parse(i.extras.browserUrl):i.extras.browserUrl,trigger:i.source,extras:i.extras,previousNavigation:this.lastSuccessfulNavigation?z(E({},this.lastSuccessfulNavigation),{previousNavigation:null}):null,abort:()=>i.abortController.abort()};let s=!n.navigated||this.isUpdatingInternalState()||this.isUpdatedBrowserUrl(),a=i.extras.onSameUrlNavigation??n.onSameUrlNavigation;if(!s&&a!=="reload"){let c="";return this.events.next(new tt(i.id,this.urlSerializer.serialize(i.rawUrl),c,Or.IgnoredSameUrlNavigation)),i.resolve(!1),ce}if(this.urlHandlingStrategy.shouldProcessUrl(i.rawUrl))return C(i).pipe(Ie(c=>(this.events.next(new Ut(c.id,this.urlSerializer.serialize(c.extractedUrl),c.source,c.restoredState)),c.id!==this.navigationId?ce:Promise.resolve(c))),CI(this.environmentInjector,this.configLoader,this.rootComponentType,n.config,this.urlSerializer,this.paramsInheritanceStrategy),J(c=>{r.targetSnapshot=c.targetSnapshot,r.urlAfterRedirects=c.urlAfterRedirects,this.currentNavigation=z(E({},this.currentNavigation),{finalUrl:c.urlAfterRedirects});let l=new kr(c.id,this.urlSerializer.serialize(c.extractedUrl),this.urlSerializer.serialize(c.urlAfterRedirects),c.targetSnapshot);this.events.next(l)}));if(s&&this.urlHandlingStrategy.shouldProcessUrl(i.currentRawUrl)){let{id:c,extractedUrl:l,source:f,restoredState:p,extras:y}=i,h=new Ut(c,this.urlSerializer.serialize(l),f,p);this.events.next(h);let S=Ap(this.rootComponentType).snapshot;return this.currentTransition=r=z(E({},i),{targetSnapshot:S,urlAfterRedirects:l,extras:z(E({},y),{skipLocationChange:!1,replaceUrl:!1})}),this.currentNavigation.finalUrl=l,C(r)}else{let c="";return this.events.next(new tt(i.id,this.urlSerializer.serialize(i.extractedUrl),c,Or.IgnoredByUrlHandlingStrategy)),i.resolve(!1),ce}}),J(i=>{let s=new Pi(i.id,this.urlSerializer.serialize(i.extractedUrl),this.urlSerializer.serialize(i.urlAfterRedirects),i.targetSnapshot);this.events.next(s)}),A(i=>(this.currentTransition=r=z(E({},i),{guards:BE(i.targetSnapshot,i.currentSnapshot,this.rootContexts)}),r)),eI(this.environmentInjector,i=>this.events.next(i)),J(i=>{if(r.guardsResult=i.guardsResult,i.guardsResult&&typeof i.guardsResult!="boolean")throw Zi(this.urlSerializer,i.guardsResult);let s=new Li(i.id,this.urlSerializer.serialize(i.extractedUrl),this.urlSerializer.serialize(i.urlAfterRedirects),i.targetSnapshot,!!i.guardsResult);this.events.next(s)}),Ee(i=>i.guardsResult?!0:(this.cancelNavigationTransition(i,"",ae.GuardRejected),!1)),Fc(i=>{if(i.guards.canActivateChecks.length!==0)return C(i).pipe(J(s=>{let a=new Fi(s.id,this.urlSerializer.serialize(s.extractedUrl),this.urlSerializer.serialize(s.urlAfterRedirects),s.targetSnapshot);this.events.next(a)}),Ie(s=>{let a=!1;return C(s).pipe(DI(this.paramsInheritanceStrategy,this.environmentInjector),J({next:()=>a=!0,complete:()=>{a||this.cancelNavigationTransition(s,"",ae.NoDataFromResolver)}}))}),J(s=>{let a=new ji(s.id,this.urlSerializer.serialize(s.extractedUrl),this.urlSerializer.serialize(s.urlAfterRedirects),s.targetSnapshot);this.events.next(a)}))}),Fc(i=>{let s=a=>{let c=[];if(a.routeConfig?.loadComponent&&!a.routeConfig._loadedComponent){let l=_n(a)??this.environmentInjector;c.push(this.configLoader.loadComponent(l,a.routeConfig).pipe(J(f=>{a.component=f}),A(()=>{})))}for(let l of a.children)c.push(...s(l));return c};return So(s(i.targetSnapshot.root)).pipe(it(null),ze(1))}),Fc(()=>this.afterPreactivation()),Ie(()=>{let{currentSnapshot:i,targetSnapshot:s}=r,a=this.createViewTransition?.(this.environmentInjector,i.root,s.root);return a?q(a).pipe(A(()=>r)):C(r)}),A(i=>{let s=jE(n.routeReuseStrategy,i.targetSnapshot,i.currentRouterState);return this.currentTransition=r=z(E({},i),{targetRouterState:s}),this.currentNavigation.targetRouterState=s,r}),J(()=>{this.events.next(new Pr)}),$E(this.rootContexts,n.routeReuseStrategy,i=>this.events.next(i),this.inputBindingEnabled),ze(1),Co(new k(i=>{let s=r.abortController.signal,a=()=>i.next();return s.addEventListener("abort",a),()=>s.removeEventListener("abort",a)}).pipe(Ee(()=>!o&&!r.targetRouterState),J(()=>{this.cancelNavigationTransition(r,r.abortController.signal.reason+"",ae.Aborted)}))),J({next:i=>{o=!0,this.lastSuccessfulNavigation=this.currentNavigation,this.events.next(new et(i.id,this.urlSerializer.serialize(i.extractedUrl),this.urlSerializer.serialize(i.urlAfterRedirects))),this.titleStrategy?.updateTitle(i.targetRouterState.snapshot),i.resolve(!0)},complete:()=>{o=!0}}),Co(this.transitionAbortWithErrorSubject.pipe(J(i=>{throw i}))),Ln(()=>{o||this.cancelNavigationTransition(r,"",ae.SupersededByNewNavigation),this.currentTransition?.id===r.id&&(this.currentNavigation=null,this.currentTransition=null)}),ot(i=>{if(this.destroyed)return r.resolve(!1),ce;if(o=!0,jp(i))this.events.next(new He(r.id,this.urlSerializer.serialize(r.extractedUrl),i.message,i.cancellationCode)),UE(i)?this.events.next(new Dn(i.url,i.navigationBehaviorOptions)):r.resolve(!1);else{let s=new Cn(r.id,this.urlSerializer.serialize(r.extractedUrl),i,r.targetSnapshot??void 0);try{let a=oe(this.environmentInjector,()=>this.navigationErrorHandler?.(s));if(a instanceof Tn){let{message:c,cancellationCode:l}=Zi(this.urlSerializer,a);this.events.next(new He(r.id,this.urlSerializer.serialize(r.extractedUrl),c,l)),this.events.next(new Dn(a.redirectTo,a.navigationBehaviorOptions))}else throw this.events.next(s),i}catch(a){this.options.resolveNavigationPromiseOnError?r.resolve(!1):r.reject(a)}}return ce}))}))}cancelNavigationTransition(n,r,o){let i=new He(n.id,this.urlSerializer.serialize(n.extractedUrl),r,o);this.events.next(i),n.resolve(!1)}isUpdatingInternalState(){return this.currentTransition?.extractedUrl.toString()!==this.currentTransition?.currentUrlTree.toString()}isUpdatedBrowserUrl(){let n=this.urlHandlingStrategy.extract(this.urlSerializer.parse(this.location.path(!0))),r=this.currentNavigation?.targetBrowserUrl??this.currentNavigation?.extractedUrl;return n.toString()!==r?.toString()&&!this.currentNavigation?.extras.skipLocationChange}static \u0275fac=function(r){return new(r||e)};static \u0275prov=b({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function RI(e){return e!==xr}var Kp=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275prov=b({token:e,factory:()=>v(AI),providedIn:"root"})}return e})(),Yi=class{shouldDetach(t){return!1}store(t,n){}shouldAttach(t){return!1}retrieve(t){return null}shouldReuseRoute(t,n){return t.routeConfig===n.routeConfig}},AI=(()=>{class e extends Yi{static \u0275fac=(()=>{let n;return function(o){return(n||(n=ai(e)))(o||e)}})();static \u0275prov=b({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),Jp=(()=>{class e{urlSerializer=v($r);options=v(Gr,{optional:!0})||{};canceledNavigationResolution=this.options.canceledNavigationResolution||"replace";location=v(yn);urlHandlingStrategy=v(Xi);urlUpdateStrategy=this.options.urlUpdateStrategy||"deferred";currentUrlTree=new Ue;getCurrentUrlTree(){return this.currentUrlTree}rawUrlTree=this.currentUrlTree;getRawUrlTree(){return this.rawUrlTree}createBrowserPath({finalUrl:n,initialUrl:r,targetBrowserUrl:o}){let i=n!==void 0?this.urlHandlingStrategy.merge(n,r):r,s=o??i;return s instanceof Ue?this.urlSerializer.serialize(s):s}commitTransition({targetRouterState:n,finalUrl:r,initialUrl:o}){r&&n?(this.currentUrlTree=r,this.rawUrlTree=this.urlHandlingStrategy.merge(r,o),this.routerState=n):this.rawUrlTree=o}routerState=Ap(null);getRouterState(){return this.routerState}stateMemento=this.createStateMemento();updateStateMemento(){this.stateMemento=this.createStateMemento()}createStateMemento(){return{rawUrlTree:this.rawUrlTree,currentUrlTree:this.currentUrlTree,routerState:this.routerState}}resetInternalState({finalUrl:n}){this.routerState=this.stateMemento.routerState,this.currentUrlTree=this.stateMemento.currentUrlTree,this.rawUrlTree=this.urlHandlingStrategy.merge(this.currentUrlTree,n??this.rawUrlTree)}static \u0275fac=function(r){return new(r||e)};static \u0275prov=b({token:e,factory:()=>v(OI),providedIn:"root"})}return e})(),OI=(()=>{class e extends Jp{currentPageId=0;lastSuccessfulId=-1;restoredState(){return this.location.getState()}get browserPageId(){return this.canceledNavigationResolution!=="computed"?this.currentPageId:this.restoredState()?.\u0275routerPageId??this.currentPageId}registerNonRouterCurrentEntryChangeListener(n){return this.location.subscribe(r=>{r.type==="popstate"&&setTimeout(()=>{n(r.url,r.state,"popstate")})})}handleRouterEvent(n,r){n instanceof Ut?this.updateStateMemento():n instanceof tt?this.commitTransition(r):n instanceof kr?this.urlUpdateStrategy==="eager"&&(r.extras.skipLocationChange||this.setBrowserUrl(this.createBrowserPath(r),r)):n instanceof Pr?(this.commitTransition(r),this.urlUpdateStrategy==="deferred"&&!r.extras.skipLocationChange&&this.setBrowserUrl(this.createBrowserPath(r),r)):n instanceof He&&n.code!==ae.SupersededByNewNavigation&&n.code!==ae.Redirect?this.restoreHistory(r):n instanceof Cn?this.restoreHistory(r,!0):n instanceof et&&(this.lastSuccessfulId=n.id,this.currentPageId=this.browserPageId)}setBrowserUrl(n,{extras:r,id:o}){let{replaceUrl:i,state:s}=r;if(this.location.isCurrentPathEqualTo(n)||i){let a=this.browserPageId,c=E(E({},s),this.generateNgRouterState(o,a));this.location.replaceState(n,"",c)}else{let a=E(E({},s),this.generateNgRouterState(o,this.browserPageId+1));this.location.go(n,"",a)}}restoreHistory(n,r=!1){if(this.canceledNavigationResolution==="computed"){let o=this.browserPageId,i=this.currentPageId-o;i!==0?this.location.historyGo(i):this.getCurrentUrlTree()===n.finalUrl&&i===0&&(this.resetInternalState(n),this.resetUrlToCurrentUrlTree())}else this.canceledNavigationResolution==="replace"&&(r&&this.resetInternalState(n),this.resetUrlToCurrentUrlTree())}resetUrlToCurrentUrlTree(){this.location.replaceState(this.urlSerializer.serialize(this.getRawUrlTree()),"",this.generateNgRouterState(this.lastSuccessfulId,this.currentPageId))}generateNgRouterState(n,r){return this.canceledNavigationResolution==="computed"?{navigationId:n,\u0275routerPageId:r}:{navigationId:n}}static \u0275fac=(()=>{let n;return function(o){return(n||(n=ai(e)))(o||e)}})();static \u0275prov=b({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function nl(e,t){e.events.pipe(Ee(n=>n instanceof et||n instanceof He||n instanceof Cn||n instanceof tt),A(n=>n instanceof et||n instanceof tt?0:(n instanceof He?n.code===ae.Redirect||n.code===ae.SupersededByNewNavigation:!1)?2:1),Ee(n=>n!==2),ze(1)).subscribe(()=>{t()})}var kI={paths:"exact",fragment:"ignored",matrixParams:"ignored",queryParams:"exact"},PI={paths:"subset",fragment:"ignored",matrixParams:"ignored",queryParams:"subset"},es=(()=>{class e{get currentUrlTree(){return this.stateManager.getCurrentUrlTree()}get rawUrlTree(){return this.stateManager.getRawUrlTree()}disposed=!1;nonRouterCurrentEntryChangeSubscription;console=v(fc);stateManager=v(Jp);options=v(Gr,{optional:!0})||{};pendingTasks=v(Ye);urlUpdateStrategy=this.options.urlUpdateStrategy||"deferred";navigationTransitions=v(Yp);urlSerializer=v($r);location=v(yn);urlHandlingStrategy=v(Xi);injector=v(ne);_events=new ee;get events(){return this._events}get routerState(){return this.stateManager.getRouterState()}navigated=!1;routeReuseStrategy=v(Kp);onSameUrlNavigation=this.options.onSameUrlNavigation||"ignore";config=v(qr,{optional:!0})?.flat()??[];componentInputBindingEnabled=!!v(Ki,{optional:!0});constructor(){this.resetConfig(this.config),this.navigationTransitions.setupNavigations(this).subscribe({error:n=>{this.console.warn(n)}}),this.subscribeToNavigationEvents()}eventsSubscription=new G;subscribeToNavigationEvents(){let n=this.navigationTransitions.events.subscribe(r=>{try{let o=this.navigationTransitions.currentTransition,i=this.navigationTransitions.currentNavigation;if(o!==null&&i!==null){if(this.stateManager.handleRouterEvent(r,i),r instanceof He&&r.code!==ae.Redirect&&r.code!==ae.SupersededByNewNavigation)this.navigated=!0;else if(r instanceof et)this.navigated=!0;else if(r instanceof Dn){let s=r.navigationBehaviorOptions,a=this.urlHandlingStrategy.merge(r.url,o.currentRawUrl),c=E({browserUrl:o.extras.browserUrl,info:o.extras.info,skipLocationChange:o.extras.skipLocationChange,replaceUrl:o.extras.replaceUrl||this.urlUpdateStrategy==="eager"||RI(o.source)},s);this.scheduleNavigation(a,xr,null,c,{resolve:o.resolve,reject:o.reject,promise:o.promise})}}kE(r)&&this._events.next(r)}catch(o){this.navigationTransitions.transitionAbortWithErrorSubject.next(o)}});this.eventsSubscription.add(n)}resetRootComponentType(n){this.routerState.root.component=n,this.navigationTransitions.rootComponentType=n}initialNavigation(){this.setUpLocationChangeListener(),this.navigationTransitions.hasRequestedNavigation||this.navigateToSyncWithBrowser(this.location.path(!0),xr,this.stateManager.restoredState())}setUpLocationChangeListener(){this.nonRouterCurrentEntryChangeSubscription??=this.stateManager.registerNonRouterCurrentEntryChangeListener((n,r,o)=>{this.navigateToSyncWithBrowser(n,o,r)})}navigateToSyncWithBrowser(n,r,o){let i={replaceUrl:!0},s=o?.navigationId?o:null;if(o){let c=E({},o);delete c.navigationId,delete c.\u0275routerPageId,Object.keys(c).length!==0&&(i.state=c)}let a=this.parseUrl(n);this.scheduleNavigation(a,r,s,i).catch(c=>{this.disposed||this.injector.get(xe)(c)})}get url(){return this.serializeUrl(this.currentUrlTree)}getCurrentNavigation(){return this.navigationTransitions.currentNavigation}get lastSuccessfulNavigation(){return this.navigationTransitions.lastSuccessfulNavigation}resetConfig(n){this.config=n.map(el),this.navigated=!1}ngOnDestroy(){this.dispose()}dispose(){this._events.unsubscribe(),this.navigationTransitions.complete(),this.nonRouterCurrentEntryChangeSubscription&&(this.nonRouterCurrentEntryChangeSubscription.unsubscribe(),this.nonRouterCurrentEntryChangeSubscription=void 0),this.disposed=!0,this.eventsSubscription.unsubscribe()}createUrlTree(n,r={}){let{relativeTo:o,queryParams:i,fragment:s,queryParamsHandling:a,preserveFragment:c}=r,l=c?this.currentUrlTree.fragment:s,f=null;switch(a??this.options.defaultQueryParamsHandling){case"merge":f=E(E({},this.currentUrlTree.queryParams),i);break;case"preserve":f=this.currentUrlTree.queryParams;break;default:f=i||null}f!==null&&(f=this.removeEmptyProps(f));let p;try{let y=o?o.snapshot:this.routerState.snapshot.root;p=_p(y)}catch{(typeof n[0]!="string"||n[0][0]!=="/")&&(n=[]),p=this.currentUrlTree.root}return Np(p,n,f,l??null)}navigateByUrl(n,r={skipLocationChange:!1}){let o=bn(n)?n:this.parseUrl(n),i=this.urlHandlingStrategy.merge(o,this.rawUrlTree);return this.scheduleNavigation(i,xr,null,r)}navigate(n,r={skipLocationChange:!1}){return LI(n),this.navigateByUrl(this.createUrlTree(n,r),r)}serializeUrl(n){return this.urlSerializer.serialize(n)}parseUrl(n){try{return this.urlSerializer.parse(n)}catch{return this.urlSerializer.parse("/")}}isActive(n,r){let o;if(r===!0?o=E({},kI):r===!1?o=E({},PI):o=r,bn(n))return lp(this.currentUrlTree,n,o);let i=this.parseUrl(n);return lp(this.currentUrlTree,i,o)}removeEmptyProps(n){return Object.entries(n).reduce((r,[o,i])=>(i!=null&&(r[o]=i),r),{})}scheduleNavigation(n,r,o,i,s){if(this.disposed)return Promise.resolve(!1);let a,c,l;s?(a=s.resolve,c=s.reject,l=s.promise):l=new Promise((p,y)=>{a=p,c=y});let f=this.pendingTasks.add();return nl(this,()=>{queueMicrotask(()=>this.pendingTasks.remove(f))}),this.navigationTransitions.handleNavigationRequest({source:r,restoredState:o,currentUrlTree:this.currentUrlTree,currentRawUrl:this.currentUrlTree,rawUrl:n,extras:i,resolve:a,reject:c,promise:l,currentSnapshot:this.routerState.snapshot,currentRouterState:this.routerState}),l.catch(p=>Promise.reject(p))}static \u0275fac=function(r){return new(r||e)};static \u0275prov=b({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function LI(e){for(let t=0;t<e.length;t++)if(e[t]==null)throw new I(4008,!1)}var jI=new w("");function rl(e,...t){return Tt([{provide:qr,multi:!0,useValue:e},[],{provide:gt,useFactory:VI,deps:[es]},{provide:Ii,multi:!0,useFactory:HI},t.map(n=>n.\u0275providers)])}function VI(e){return e.routerState.root}function HI(){let e=v(we);return t=>{let n=e.get(Lt);if(t!==n.components[0])return;let r=e.get(es),o=e.get(UI);e.get($I)===1&&r.initialNavigation(),e.get(BI,null,{optional:!0})?.setUpPreloading(),e.get(jI,null,{optional:!0})?.init(),r.resetRootComponentType(n.componentTypes[0]),o.closed||(o.next(),o.complete(),o.unsubscribe())}}var UI=new w("",{factory:()=>new ee}),$I=new w("",{providedIn:"root",factory:()=>1});var BI=new w("");var ts=class e{static \u0275fac=function(n){return new(n||e)};static \u0275cmp=Pt({type:e,selectors:[["app-dashboard"]],decls:655,vars:0,consts:[["id","loading"],["id","loading-center"],[1,"wrapper"],[1,"iq-sidebar","sidebar-default"],[1,"iq-sidebar-logo","d-flex","align-items-center","justify-content-between"],["href","index.html",1,"header-logo"],["ngSrc","assets/images/logo.png","alt","logo","height","500","width","500",1,"img-fluid","rounded-normal","light-logo"],[1,"logo-title","light-logo","ml-3"],[1,"iq-menu-bt-sidebar","ml-0"],[1,"las","la-bars","wrapper-menu"],["data-scroll","1",1,"data-scrollbar"],[1,"iq-sidebar-menu"],["id","iq-sidebar-toggle",1,"iq-menu"],[1,"active"],["href","index.html",1,"svg-icon"],["id","p-dash1","width","20","height","20","xmlns","http://www.w3.org/2000/svg","viewBox","0 0 24 24","fill","none","stroke","currentColor","stroke-width","2","stroke-linecap","round","stroke-linejoin","round",1,"svg-icon"],["d","M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"],["points","3.27 6.96 12 12.01 20.73 6.96"],["x1","12","y1","22.08","x2","12","y2","12"],[1,"ml-4"],[1,""],["href","#product","data-toggle","collapse","aria-expanded","false",1,"collapsed"],["id","p-dash2","width","20","height","20","xmlns","http://www.w3.org/2000/svg","viewBox","0 0 24 24","fill","none","stroke","currentColor","stroke-width","2","stroke-linecap","round","stroke-linejoin","round",1,"svg-icon"],["cx","9","cy","21","r","1"],["cx","20","cy","21","r","1"],["d","M1 1h4l2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6"],["width","20","height","20","xmlns","http://www.w3.org/2000/svg","viewBox","0 0 24 24","fill","none","stroke","currentColor","stroke-width","2","stroke-linecap","round","stroke-linejoin","round",1,"svg-icon","iq-arrow-right","arrow-active"],["points","10 15 15 20 20 15"],["d","M4 4h7a4 4 0 0 1 4 4v12"],["href","#category","data-toggle","collapse","aria-expanded","false",1,"collapsed"],["id","p-dash3","width","20","height","20","xmlns","http://www.w3.org/2000/svg","viewBox","0 0 24 24","fill","none","stroke","currentColor","stroke-width","2","stroke-linecap","round","stroke-linejoin","round",1,"svg-icon"],["x","9","y","9","width","13","height","13","rx","2","ry","2"],["d","M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"],["href","#sale","data-toggle","collapse","aria-expanded","false",1,"collapsed"],["id","p-dash4","width","20","height","20","xmlns","http://www.w3.org/2000/svg","viewBox","0 0 24 24","fill","none","stroke","currentColor","stroke-width","2","stroke-linecap","round","stroke-linejoin","round",1,"svg-icon"],["d","M21.21 15.89A10 10 0 1 1 8 2.83"],["d","M22 12A10 10 0 0 0 12 2v10z"],["href","#purchase","data-toggle","collapse","aria-expanded","false",1,"collapsed"],["id","p-dash5","width","20","height","20","xmlns","http://www.w3.org/2000/svg","viewBox","0 0 24 24","fill","none","stroke","currentColor","stroke-width","2","stroke-linecap","round","stroke-linejoin","round",1,"svg-icon"],["x","1","y","4","width","22","height","16","rx","2","ry","2"],["x1","1","y1","10","x2","23","y2","10"],["href","#return","data-toggle","collapse","aria-expanded","false",1,"collapsed"],["id","p-dash6","width","20","height","20","xmlns","http://www.w3.org/2000/svg","viewBox","0 0 24 24","fill","none","stroke","currentColor","stroke-width","2","stroke-linecap","round","stroke-linejoin","round",1,"svg-icon"],["points","4 14 10 14 10 20"],["points","20 10 14 10 14 4"],["x1","14","y1","10","x2","21","y2","3"],["x1","3","y1","21","x2","10","y2","14"],["href","#people","data-toggle","collapse","aria-expanded","false",1,"collapsed"],["id","p-dash8","width","20","height","20","xmlns","http://www.w3.org/2000/svg","viewBox","0 0 24 24","fill","none","stroke","currentColor","stroke-width","2","stroke-linecap","round","stroke-linejoin","round",1,"svg-icon"],["d","M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"],["cx","9","cy","7","r","4"],["d","M23 21v-2a4 4 0 0 0-3-3.87"],["d","M16 3.13a4 4 0 0 1 0 7.75"],["href","#otherpage","data-toggle","collapse","aria-expanded","false",1,"collapsed"],["id","p-dash9","width","20","height","20","xmlns","http://www.w3.org/2000/svg","viewBox","0 0 24 24","fill","none","stroke","currentColor","stroke-width","2","stroke-linecap","round","stroke-linejoin","round",1,"svg-icon"],["x","3","y","3","width","18","height","18","rx","2","ry","2"],["x","7","y","7","width","3","height","9"],["x","14","y","7","width","3","height","5"],["id","otherpage","data-parent","#iq-sidebar-toggle",1,"iq-submenu","collapse"],["href","#user","data-toggle","collapse","aria-expanded","false",1,"collapsed"],["id","p-dash10","width","20","height","20","xmlns","http://www.w3.org/2000/svg","viewBox","0 0 24 24","fill","none","stroke","currentColor","stroke-width","2","stroke-linecap","round","stroke-linejoin","round",1,"svg-icon"],["d","M16 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"],["cx","8.5","cy","7","r","4"],["points","17 11 19 13 23 9"],["href","#ui","data-toggle","collapse","aria-expanded","false",1,"collapsed"],["id","p-dash11","width","20","height","20","xmlns","http://www.w3.org/2000/svg","viewBox","0 0 24 24","fill","none","stroke","currentColor","stroke-width","2","stroke-linecap","round","stroke-linejoin","round",1,"svg-icon"],["href","#auth","data-toggle","collapse","aria-expanded","false",1,"collapsed"],["id","p-dash12","width","20","height","20","xmlns","http://www.w3.org/2000/svg","viewBox","0 0 24 24","fill","none","stroke","currentColor","stroke-width","2","stroke-linecap","round","stroke-linejoin","round",1,"svg-icon"],["d","M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"],["points","14 2 14 8 20 8"],["x1","16","y1","13","x2","8","y2","13"],["x1","16","y1","17","x2","8","y2","17"],["points","10 9 9 9 8 9"],["href","#form","data-toggle","collapse","aria-expanded","false",1,"collapsed","svg-icon"],["id","p-dash13","width","20","height","20","xmlns","http://www.w3.org/2000/svg","viewBox","0 0 24 24","fill","none","stroke","currentColor","stroke-width","2","stroke-linecap","round","stroke-linejoin","round",1,"svg-icon"],["d","M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2"],["x","8","y","2","width","8","height","4","rx","1","ry","1"],["href","#table","data-toggle","collapse","aria-expanded","false",1,"collapsed"],["id","p-dash14","width","20","height","20","xmlns","http://www.w3.org/2000/svg","viewBox","0 0 24 24","fill","none","stroke","currentColor","stroke-width","2","stroke-linecap","round","stroke-linejoin","round",1,"svg-icon"],["x","3","y","3","width","7","height","7"],["x","14","y","3","width","7","height","7"],["x","14","y","14","width","7","height","7"],["x","3","y","14","width","7","height","7"],["href","#pricing","data-toggle","collapse","aria-expanded","false",1,"collapsed"],["id","p-dash16","width","20","height","20","xmlns","http://www.w3.org/2000/svg","viewBox","0 0 24 24","fill","none","stroke","currentColor","stroke-width","2","stroke-linecap","round","stroke-linejoin","round",1,"svg-icon"],["cx","12","cy","5","rx","9","ry","3"],["d","M21 12c0 1.66-4 3-9 3s-9-1.34-9-3"],["d","M3 5v14c0 1.66 4 3 9 3s9-1.34 9-3V5"],["id","sidebar-bottom",1,"position-relative","sidebar-bottom"],[1,"card","border-none"],[1,"card-body","p-0"],[1,"sidebarbottom-content"],[1,"image"],["ngSrc","assets/images/layouts/side-bkg.png","alt","side-bkg","height","183","width","175",1,"img-fluid"],[1,"mt-4","px-4","body-title"],[1,"p-3"],[1,"iq-top-navbar"],[1,"iq-navbar-custom"],[1,"navbar","navbar-expand-lg","navbar-light","p-0"],[1,"iq-navbar-logo","d-flex","align-items-center","justify-content-between"],[1,"ri-menu-line","wrapper-menu"],["ngSrc","assets/images/logo.png","alt","logo","height","500","width","500",1,"img-fluid","rounded-normal"],[1,"logo-title","ml-3"],[1,"iq-search-bar","device-search"],["action","#",1,"searchbox"],["href","#",1,"search-link"],[1,"ri-search-line"],["type","text","placeholder","Search here...",1,"text","search-input"],[1,"d-flex","align-items-center"],["type","button","data-toggle","collapse","data-target","#navbarSupportedContent","aria-controls","navbarSupportedContent","aria-label","Toggle navigation",1,"navbar-toggler"],[1,"ri-menu-3-line"],["id","navbarSupportedContent",1,"collapse","navbar-collapse"],[1,"navbar-nav","ml-auto","navbar-list","align-items-center"],[1,"nav-item","nav-icon","dropdown"],["href","#","id","dropdownMenuButton02","data-toggle","dropdown","aria-haspopup","true","aria-expanded","false",1,"search-toggle","dropdown-toggle","btn","border","add-btn"],["ngSrc","assets/images/small/flag-01.png","alt","img-flag","height","16","width","16",1,"img-fluid","image-flag","mr-2"],["aria-labelledby","dropdownMenuButton2",1,"iq-sub-dropdown","dropdown-menu"],[1,"card","shadow-none","m-0"],[1,"card-body","p-3"],["href","#",1,"iq-sub-card"],["ngSrc","assets/images/small/flag-02.png","alt","img-flag","height","16","width","16",1,"img-fluid","mr-2"],["ngSrc","assets/images/small/flag-03.png","alt","img-flag","height","16","width","16",1,"img-fluid","mr-2"],["ngSrc","assets/images/small/flag-04.png","alt","img-flag","height","16","width","16",1,"img-fluid","mr-2"],["ngSrc","assets/images/small/flag-05.png","alt","img-flag","height","16","width","16",1,"img-fluid","mr-2"],["ngSrc","assets/images/small/flag-06.png","alt","img-flag","height","16","width","16",1,"img-fluid","mr-2"],["href","#","data-toggle","modal","data-target","#new-order",1,"btn","border","add-btn","shadow-none","mx-2","d-none","d-md-block"],[1,"las","la-plus","mr-2"],[1,"nav-item","nav-icon","search-content"],["href","#","id","dropdownSearch","data-toggle","dropdown","aria-haspopup","true","aria-expanded","false",1,"search-toggle","rounded"],["aria-labelledby","dropdownSearch",1,"iq-search-bar","iq-sub-dropdown","dropdown-menu"],["action","#",1,"searchbox","p-2"],[1,"form-group","mb-0","position-relative"],["type","text","placeholder","type here to search...",1,"text","search-input","font-size-12"],[1,"las","la-search"],["href","#","id","dropdownMenuButton2","data-toggle","dropdown","aria-haspopup","true","aria-expanded","false",1,"search-toggle","dropdown-toggle"],["xmlns","http://www.w3.org/2000/svg","width","20","height","20","viewBox","0 0 24 24","fill","none","stroke","currentColor","stroke-width","2","stroke-linecap","round","stroke-linejoin","round",1,"feather","feather-mail"],["d","M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"],["points","22,6 12,13 2,6"],[1,"bg-primary"],[1,"cust-title","p-3"],[1,"d-flex","align-items-center","justify-content-between"],[1,"mb-0"],["href","#",1,"badge","badge-primary","badge-card"],[1,"px-3","pt-0","pb-0","sub-card"],[1,"media","align-items-center","cust-card","py-3","border-bottom"],["ngSrc","assets/images/user/01.jpg","alt","01","height","350","width","350",1,"avatar-50","rounded-small"],[1,"media-body","ml-3"],[1,"text-dark"],["ngSrc","assets/images/user/02.jpg","alt","02","height","350","width","350",1,"avatar-50","rounded-small"],[1,"media","align-items-center","cust-card","py-3"],["ngSrc","assets/images/user/03.jpg","alt","03","height","350","width","350",1,"avatar-50","rounded-small"],["href","#","role","button",1,"right-ic","btn","btn-primary","btn-block","position-relative","p-2"],["href","#","id","dropdownMenuButton","data-toggle","dropdown","aria-haspopup","true","aria-expanded","false",1,"search-toggle","dropdown-toggle"],["xmlns","http://www.w3.org/2000/svg","width","20","height","20","viewBox","0 0 24 24","fill","none","stroke","currentColor","stroke-width","2","stroke-linecap","round","stroke-linejoin","round",1,"feather","feather-bell"],["d","M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9"],["d","M13.73 21a2 2 0 0 1-3.46 0"],["aria-labelledby","dropdownMenuButton",1,"iq-sub-dropdown","dropdown-menu"],[1,"nav-item","nav-icon","dropdown","caption-content"],["href","#","id","dropdownMenuButton4","data-toggle","dropdown","aria-haspopup","true","aria-expanded","false",1,"search-toggle","dropdown-toggle"],["ngSrc","assets/images/user/1.png","alt","user","height","350","width","350",1,"img-fluid","rounded"],[1,"card-body","p-0","text-center"],[1,"media-body","profile-detail","text-center"],["ngSrc","assets/images/page-img/profile-bg.jpg","alt","profile-bg","height","236","width","300",1,"rounded-top","img-fluid","mb-4"],["ngSrc","assets/images/user/1.png","alt","profile-img","height","350","width","350",1,"rounded","profile-img","img-fluid","avatar-70"],[1,"mb-1"],["id","new-order","tabindex","-1","role","dialog","aria-hidden","true",1,"modal","fade"],["role","document",1,"modal-dialog","modal-dialog-centered"],[1,"modal-content"],[1,"modal-body"],[1,"popup","text-left"],[1,"mb-3"],[1,"content","create-workform","bg-body"],[1,"pb-3"],[1,"mb-2"],["type","text","placeholder","Enter Name or Email",1,"form-control"],[1,"col-lg-12","mt-4"],[1,"d-flex","flex-wrap","align-items-ceter","justify-content-center"],["data-dismiss","modal",1,"btn","btn-primary","mr-4"],["data-dismiss","modal",1,"btn","btn-outline-primary"],[1,"content-page"],[1,"container-fluid"],[1,"row"],[1,"col-lg-4"],[1,"card","card-transparent","card-block","card-stretch","card-height","border-none"],[1,"card-body","p-0","mt-lg-2","mt-0"],[1,"mb-0","mr-4"],[1,"col-lg-8"],[1,"col-lg-4","col-md-4"],[1,"card","card-block","card-stretch","card-height"],[1,"card-body"],[1,"d-flex","align-items-center","mb-4","card-total-sale"],[1,"icon","iq-icon-box-2","bg-info-light"],["ngSrc","assets/images/product/1.png","alt","image","height","1748","width","676",1,"img-fluid"],[1,"iq-progress-bar","mt-2"],["data-percent","85",1,"bg-info","iq-progress","progress-1"],[1,"icon","iq-icon-box-2","bg-danger-light"],["ngSrc","assets/images/product/2.png","alt","image","height","685","width","826",1,"img-fluid"],["data-percent","70",1,"bg-danger","iq-progress","progress-1"],[1,"icon","iq-icon-box-2","bg-success-light"],["ngSrc","assets/images/product/3.png","alt","image","height","941","width","1504",1,"img-fluid"],["data-percent","75",1,"bg-success","iq-progress","progress-1"],[1,"col-lg-6"],[1,"card-header","d-flex","justify-content-between"],[1,"header-title"],[1,"card-title"],[1,"card-header-toolbar","d-flex","align-items-center"],[1,"dropdown"],["id","dropdownMenuButton001","data-toggle","dropdown",1,"dropdown-toggle","dropdown-bg","btn"],[1,"ri-arrow-down-s-line","ml-1"],["aria-labelledby","dropdownMenuButton001",1,"dropdown-menu","dropdown-menu-right","shadow-none"],["href","#",1,"dropdown-item"],["id","layout1-chart1"],[1,"card-header","d-flex","align-items-center","justify-content-between"],["id","dropdownMenuButton002","data-toggle","dropdown",1,"dropdown-toggle","dropdown-bg","btn"],["aria-labelledby","dropdownMenuButton002",1,"dropdown-menu","dropdown-menu-right","shadow-none"],["id","layout1-chart-2",2,"min-height","360px"],["id","dropdownMenuButton006","data-toggle","dropdown",1,"dropdown-toggle","dropdown-bg","btn"],["aria-labelledby","dropdownMenuButton006",1,"dropdown-menu","dropdown-menu-right","shadow-none"],[1,"list-unstyled","row","top-product","mb-0"],[1,"col-lg-3"],[1,"card","card-block","card-stretch","card-height","mb-0"],[1,"bg-warning-light","rounded"],["ngSrc","assets/images/product/01.png","alt","image","height","160","width","160",1,"style-img","img-fluid","m-auto","p-3"],[1,"style-text","text-left","mt-3"],[1,"bg-danger-light","rounded"],["ngSrc","assets/images/product/02.png","alt","image","height","160","width","160",1,"style-img","img-fluid","m-auto","p-3"],[1,"bg-info-light","rounded"],["ngSrc","assets/images/product/03.png","alt","image","height","160","width","160",1,"style-img","img-fluid","m-auto","p-3"],[1,"bg-success-light","rounded"],[1,"card","card-transparent","card-block","card-stretch","mb-4"],[1,"card-header","d-flex","align-items-center","justify-content-between","p-0"],[1,"card-title","mb-0"],["href","#",1,"btn","btn-primary","view-btn","font-size-14"],[1,"card","card-block","card-stretch","card-height-helf"],[1,"card-body","card-item-right"],[1,"d-flex","align-items-top"],["ngSrc","assets/images/product/04.png","alt","image","height","100","width","100",1,"style-img","img-fluid","m-auto"],[1,"style-text","text-left"],["ngSrc","assets/images/product/05.png","alt","image","height","100","width","100",1,"style-img","img-fluid","m-auto"],[1,"d-flex","align-items-top","justify-content-between"],["id","dropdownMenuButton003","data-toggle","dropdown",1,"dropdown-toggle","dropdown-bg","btn"],["aria-labelledby","dropdownMenuButton003",1,"dropdown-menu","dropdown-menu-right","shadow-none"],["id","layout1-chart-3",1,"layout-chart-1"],["id","dropdownMenuButton004","data-toggle","dropdown",1,"dropdown-toggle","dropdown-bg","btn"],["aria-labelledby","dropdownMenuButton004",1,"dropdown-menu","dropdown-menu-right","shadow-none"],["id","layout1-chart-4",1,"layout-chart-2"],["id","dropdownMenuButton005","data-toggle","dropdown",1,"dropdown-toggle","dropdown-bg","btn"],["aria-labelledby","dropdownMenuButton005",1,"dropdown-menu","dropdown-menu-right","shadow-none"],[1,"d-flex","flex-wrap","align-items-center","mt-2"],[1,"d-flex","align-items-center","progress-order-left"],["data-percent","46",1,"progress","progress-round","m-0","orange","conversation-bar"],[1,"progress-left"],[1,"progress-bar"],[1,"progress-right"],[1,"progress-value","text-secondary"],[1,"progress-value","ml-3","pr-5","border-right"],[1,"d-flex","align-items-center","ml-5","progress-order-right"],["data-percent","46",1,"progress","progress-round","m-0","primary","conversation-bar"],[1,"progress-value","text-primary"],[1,"progress-value","ml-3"],[1,"card-body","pt-0"],["id","layout1-chart-5"],[1,"iq-footer"],[1,"card"]],template:function(n,r){n&1&&(u(0,"div",0),m(1,"div",1),d(),u(2,"div",2)(3,"div",3)(4,"div",4)(5,"a",5),m(6,"img",6),u(7,"h5",7),g(8,"POSDash"),d()(),u(9,"div",8),m(10,"i",9),d()(),u(11,"div",10)(12,"nav",11)(13,"ul",12)(14,"li",13)(15,"a",14),L(),u(16,"svg",15),m(17,"path",16)(18,"polyline",17)(19,"line",18),d(),F(),u(20,"span",19),g(21,"Dashboards"),d()()(),u(22,"li",20)(23,"a",21),L(),u(24,"svg",22),m(25,"circle",23)(26,"circle",24)(27,"path",25),d(),F(),u(28,"span",19),g(29,"Products"),d(),L(),u(30,"svg",26),m(31,"polyline",27)(32,"path",28),d()()(),F(),u(33,"li",20)(34,"a",29),L(),u(35,"svg",30),m(36,"rect",31)(37,"path",32),d(),F(),u(38,"span",19),g(39,"Categories"),d(),L(),u(40,"svg",26),m(41,"polyline",27)(42,"path",28),d()()(),F(),u(43,"li",20)(44,"a",33),L(),u(45,"svg",34),m(46,"path",35)(47,"path",36),d(),F(),u(48,"span",19),g(49,"Sale"),d(),L(),u(50,"svg",26),m(51,"polyline",27)(52,"path",28),d()()(),F(),u(53,"li",20)(54,"a",37),L(),u(55,"svg",38),m(56,"rect",39)(57,"line",40),d(),F(),u(58,"span",19),g(59,"Purchases"),d(),L(),u(60,"svg",26),m(61,"polyline",27)(62,"path",28),d()()(),F(),u(63,"li",20)(64,"a",41),L(),u(65,"svg",42),m(66,"polyline",43)(67,"polyline",44)(68,"line",45)(69,"line",46),d(),F(),u(70,"span",19),g(71,"Returns"),d(),L(),u(72,"svg",26),m(73,"polyline",27)(74,"path",28),d()()(),F(),u(75,"li",20)(76,"a",47),L(),u(77,"svg",48),m(78,"path",49)(79,"circle",50)(80,"path",51)(81,"path",52),d(),F(),u(82,"span",19),g(83,"People"),d(),L(),u(84,"svg",26),m(85,"polyline",27)(86,"path",28),d()()(),F(),u(87,"li",20)(88,"a",53),L(),u(89,"svg",54),m(90,"rect",55)(91,"rect",56)(92,"rect",57),d(),F(),u(93,"span",19),g(94,"other page"),d(),L(),u(95,"svg",26),m(96,"polyline",27)(97,"path",28),d()(),F(),u(98,"ul",58)(99,"li",20)(100,"a",59),L(),u(101,"svg",60),m(102,"path",61)(103,"circle",62)(104,"polyline",63),d(),F(),u(105,"span",19),g(106,"User Details"),d(),L(),u(107,"svg",26),m(108,"polyline",27)(109,"path",28),d()()(),F(),u(110,"li",20)(111,"a",64),L(),u(112,"svg",65),m(113,"path",16),d(),F(),u(114,"span",19),g(115,"UI Elements"),d(),L(),u(116,"svg",26),m(117,"polyline",27)(118,"path",28),d()()(),F(),u(119,"li",20)(120,"a",66),L(),u(121,"svg",67),m(122,"path",68)(123,"polyline",69)(124,"line",70)(125,"line",71)(126,"polyline",72),d(),F(),u(127,"span",19),g(128,"Authentication"),d(),L(),u(129,"svg",26),m(130,"polyline",27)(131,"path",28),d()()(),F(),u(132,"li",20)(133,"a",73),L(),u(134,"svg",74),m(135,"path",75)(136,"rect",76),d(),F(),u(137,"span",19),g(138,"Forms"),d(),L(),u(139,"svg",26),m(140,"polyline",27)(141,"path",28),d()()(),F(),u(142,"li",20)(143,"a",77),L(),u(144,"svg",78),m(145,"rect",79)(146,"rect",80)(147,"rect",81)(148,"rect",82),d(),F(),u(149,"span",19),g(150,"Table"),d(),L(),u(151,"svg",26),m(152,"polyline",27)(153,"path",28),d()()(),F(),u(154,"li",20)(155,"a",83),L(),u(156,"svg",84),m(157,"ellipse",85)(158,"path",86)(159,"path",87),d(),F(),u(160,"span",19),g(161,"Pricing"),d(),L(),u(162,"svg",26),m(163,"polyline",27)(164,"path",28),d()()()()()()(),F(),u(165,"div",88)(166,"div",89)(167,"div",90)(168,"div",91)(169,"div",92),m(170,"img",93),d(),m(171,"h6",94),d()()()(),m(172,"div",95),d()(),u(173,"div",96)(174,"div",97)(175,"nav",98)(176,"div",99),m(177,"i",100),u(178,"a",5),m(179,"img",101),u(180,"h5",102),g(181,"POSDash"),d()()(),u(182,"div",103)(183,"form",104)(184,"a",105),m(185,"i",106),d(),m(186,"input",107),d()(),u(187,"div",108)(188,"button",109),m(189,"i",110),d(),u(190,"div",111)(191,"ul",112)(192,"li",113)(193,"a",114),m(194,"img",115),g(195,"En "),d(),u(196,"div",116)(197,"div",117)(198,"div",118)(199,"a",119),m(200,"img",120),g(201,"French"),d(),u(202,"a",119),m(203,"img",121),g(204,"Spanish"),d(),u(205,"a",119),m(206,"img",122),g(207,"Italian"),d(),u(208,"a",119),m(209,"img",123),g(210,"German"),d(),u(211,"a",119),m(212,"img",124),g(213,"Japanese"),d()()()()(),u(214,"li")(215,"a",125),m(216,"i",126),g(217,"New Order"),d()(),u(218,"li",127)(219,"a",128),m(220,"i",106),d(),u(221,"div",129)(222,"form",130)(223,"div",131),m(224,"input",132),u(225,"a",105),m(226,"i",133),d()()()()(),u(227,"li",113)(228,"a",134),L(),u(229,"svg",135),m(230,"path",136)(231,"polyline",137),d(),F(),m(232,"span",138),d(),u(233,"div",116)(234,"div",117)(235,"div",90)(236,"div",139)(237,"div",140)(238,"h5",141),g(239,"All Messages"),d(),u(240,"a",142),g(241,"3"),d()()(),u(242,"div",143)(243,"a",119)(244,"div",144)(245,"div",20),m(246,"img",145),d(),u(247,"div",146)(248,"div",140)(249,"h6",141),g(250,"Emma Watson"),d(),u(251,"small",147)(252,"b"),g(253,"12 : 47 pm"),d()()(),u(254,"small",141),g(255,"Lorem ipsum dolor sit amet"),d()()()(),u(256,"a",119)(257,"div",144)(258,"div",20),m(259,"img",148),d(),u(260,"div",146)(261,"div",140)(262,"h6",141),g(263,"Ashlynn Franci"),d(),u(264,"small",147)(265,"b"),g(266,"11 : 30 pm"),d()()(),u(267,"small",141),g(268,"Lorem ipsum dolor sit amet"),d()()()(),u(269,"a",119)(270,"div",149)(271,"div",20),m(272,"img",150),d(),u(273,"div",146)(274,"div",140)(275,"h6",141),g(276,"Kianna Carder"),d(),u(277,"small",147)(278,"b"),g(279,"11 : 21 pm"),d()()(),u(280,"small",141),g(281,"Lorem ipsum dolor sit amet"),d()()()()(),u(282,"a",151),g(283," View All "),d()()()()(),u(284,"li",113)(285,"a",152),L(),u(286,"svg",153),m(287,"path",154)(288,"path",155),d(),F(),m(289,"span",138),d(),u(290,"div",156)(291,"div",117)(292,"div",90)(293,"div",139)(294,"div",140)(295,"h5",141),g(296,"Notifications"),d(),u(297,"a",142),g(298,"3"),d()()(),u(299,"div",143)(300,"a",119)(301,"div",144)(302,"div",20),m(303,"img",145),d(),u(304,"div",146)(305,"div",140)(306,"h6",141),g(307,"Emma Watson"),d(),u(308,"small",147)(309,"b"),g(310,"12 : 47 pm"),d()()(),u(311,"small",141),g(312,"Lorem ipsum dolor sit amet"),d()()()(),u(313,"a",119)(314,"div",144)(315,"div",20),m(316,"img",148),d(),u(317,"div",146)(318,"div",140)(319,"h6",141),g(320,"Ashlynn Franci"),d(),u(321,"small",147)(322,"b"),g(323,"11 : 30 pm"),d()()(),u(324,"small",141),g(325,"Lorem ipsum dolor sit amet"),d()()()(),u(326,"a",119)(327,"div",149)(328,"div",20),m(329,"img",150),d(),u(330,"div",146)(331,"div",140)(332,"h6",141),g(333,"Kianna Carder"),d(),u(334,"small",147)(335,"b"),g(336,"11 : 21 pm"),d()()(),u(337,"small",141),g(338,"Lorem ipsum dolor sit amet"),d()()()()(),u(339,"a",151),g(340," View All "),d()()()()(),u(341,"li",157)(342,"a",158),m(343,"img",159),d(),u(344,"div",156)(345,"div",117)(346,"div",160)(347,"div",161),m(348,"img",162)(349,"img",163),d(),u(350,"div",95)(351,"h5",164),g(352,"therichpost.com"),d(),u(353,"p",141),g(354,"Since 10 march, 2017"),d()()()()()()()()()()()(),u(355,"div",165)(356,"div",166)(357,"div",167)(358,"div",168)(359,"div",169)(360,"h4",170),g(361,"New Order"),d(),u(362,"div",171)(363,"div",172)(364,"label",173),g(365,"Email"),d(),m(366,"input",174),d(),u(367,"div",175)(368,"div",176)(369,"div",177),g(370,"Cancel"),d(),u(371,"div",178),g(372,"Create"),d()()()()()()()()(),u(373,"div",179)(374,"div",180)(375,"div",181)(376,"div",182)(377,"div",183)(378,"div",184)(379,"h3",170),g(380,"Hi Graham, Good Morning"),d(),u(381,"p",185),g(382,"Your dashboard gives you views of key performance or business process."),d()()()(),u(383,"div",186)(384,"div",181)(385,"div",187)(386,"div",188)(387,"div",189)(388,"div",190)(389,"div",191),m(390,"img",192),d(),u(391,"div")(392,"p",173),g(393,"Total Sales"),d(),u(394,"h4"),g(395,"31.50"),d()()(),u(396,"div",193),m(397,"span",194),d()()()(),u(398,"div",187)(399,"div",188)(400,"div",189)(401,"div",190)(402,"div",195),m(403,"img",196),d(),u(404,"div")(405,"p",173),g(406,"Total Cost"),d(),u(407,"h4"),g(408,"$ 4598"),d()()(),u(409,"div",193),m(410,"span",197),d()()()(),u(411,"div",187)(412,"div",188)(413,"div",189)(414,"div",190)(415,"div",198),m(416,"img",199),d(),u(417,"div")(418,"p",173),g(419,"Product Sold"),d(),u(420,"h4"),g(421,"4589 M"),d()()(),u(422,"div",193),m(423,"span",200),d()()()()()(),u(424,"div",201)(425,"div",188)(426,"div",202)(427,"div",203)(428,"h4",204),g(429,"Overview"),d()(),u(430,"div",205)(431,"div",206)(432,"span",207),g(433," This Month"),m(434,"i",208),d(),u(435,"div",209)(436,"a",210),g(437,"Year"),d(),u(438,"a",210),g(439,"Month"),d(),u(440,"a",210),g(441,"Week"),d()()()()(),u(442,"div",189),m(443,"div",211),d()()(),u(444,"div",201)(445,"div",188)(446,"div",212)(447,"div",203)(448,"h4",204),g(449,"Revenue Vs Cost"),d()(),u(450,"div",205)(451,"div",206)(452,"span",213),g(453," This Month"),m(454,"i",208),d(),u(455,"div",214)(456,"a",210),g(457,"Yearly"),d(),u(458,"a",210),g(459,"Monthly"),d(),u(460,"a",210),g(461,"Weekly"),d()()()()(),u(462,"div",189),m(463,"div",215),d()()(),u(464,"div",186)(465,"div",188)(466,"div",212)(467,"div",203)(468,"h4",204),g(469,"Top Products"),d()(),u(470,"div",205)(471,"div",206)(472,"span",216),g(473," This Month"),m(474,"i",208),d(),u(475,"div",217)(476,"a",210),g(477,"Year"),d(),u(478,"a",210),g(479,"Month"),d(),u(480,"a",210),g(481,"Week"),d()()()()(),u(482,"div",189)(483,"ul",218)(484,"li",219)(485,"div",220)(486,"div",189)(487,"div",221),m(488,"img",222),d(),u(489,"div",223)(490,"h5",164),g(491,"Organic Cream"),d(),u(492,"p",141),g(493,"789 Item"),d()()()()(),u(494,"li",219)(495,"div",220)(496,"div",189)(497,"div",224),m(498,"img",225),d(),u(499,"div",223)(500,"h5",164),g(501,"Rain Umbrella"),d(),u(502,"p",141),g(503,"657 Item"),d()()()()(),u(504,"li",219)(505,"div",220)(506,"div",189)(507,"div",226),m(508,"img",227),d(),u(509,"div",223)(510,"h5",164),g(511,"Serum Bottle"),d(),u(512,"p",141),g(513,"489 Item"),d()()()()(),u(514,"li",219)(515,"div",220)(516,"div",189)(517,"div",228),m(518,"img",225),d(),u(519,"div",223)(520,"h5",164),g(521,"Organic Cream"),d(),u(522,"p",141),g(523,"468 Item"),d()()()()()()()()(),u(524,"div",182)(525,"div",229)(526,"div",230)(527,"div",203)(528,"h4",231),g(529,"Best Item All Time"),d()(),u(530,"div",205)(531,"div")(532,"a",232),g(533,"View All"),d()()()()(),u(534,"div",233)(535,"div",234)(536,"div",235)(537,"div",221),m(538,"img",236),d(),u(539,"div",237)(540,"h5",173),g(541,"Coffee Beans Packet"),d(),u(542,"p",173),g(543,"Total Sell : 45897"),d(),u(544,"p",141),g(545,"Total Earned : $45,89 M"),d()()()()(),u(546,"div",233)(547,"div",234)(548,"div",235)(549,"div",224),m(550,"img",238),d(),u(551,"div",237)(552,"h5",173),g(553,"Bottle Cup Set"),d(),u(554,"p",173),g(555,"Total Sell : 44359"),d(),u(556,"p",141),g(557,"Total Earned : $45,50 M"),d()()()()()(),u(558,"div",182)(559,"div",233)(560,"div",189)(561,"div",239)(562,"div",20)(563,"p",141),g(564,"Income"),d(),u(565,"h5"),g(566,"$ 98,7800 K"),d()(),u(567,"div",205)(568,"div",206)(569,"span",240),g(570," This Month"),m(571,"i",208),d(),u(572,"div",241)(573,"a",210),g(574,"Year"),d(),u(575,"a",210),g(576,"Month"),d(),u(577,"a",210),g(578,"Week"),d()()()()(),m(579,"div",242),d()(),u(580,"div",233)(581,"div",189)(582,"div",239)(583,"div",20)(584,"p",141),g(585,"Expenses"),d(),u(586,"h5"),g(587,"$ 45,8956 K"),d()(),u(588,"div",205)(589,"div",206)(590,"span",243),g(591," This Month"),m(592,"i",208),d(),u(593,"div",244)(594,"a",210),g(595,"Year"),d(),u(596,"a",210),g(597,"Month"),d(),u(598,"a",210),g(599,"Week"),d()()()()(),m(600,"div",245),d()()(),u(601,"div",186)(602,"div",188)(603,"div",202)(604,"div",203)(605,"h4",204),g(606,"Order Summary"),d()(),u(607,"div",205)(608,"div",206)(609,"span",246),g(610," This Month"),m(611,"i",208),d(),u(612,"div",247)(613,"a",210),g(614,"Year"),d(),u(615,"a",210),g(616,"Month"),d(),u(617,"a",210),g(618,"Week"),d()()()()(),u(619,"div",189)(620,"div",248)(621,"div",249)(622,"div",250)(623,"span",251),m(624,"span",252),d(),u(625,"span",253),m(626,"span",252),d(),u(627,"div",254),g(628,"46%"),d()(),u(629,"div",255)(630,"h5"),g(631,"$12,6598"),d(),u(632,"p",141),g(633,"Average Orders"),d()()(),u(634,"div",256)(635,"div",257)(636,"span",251),m(637,"span",252),d(),u(638,"span",253),m(639,"span",252),d(),u(640,"div",258),g(641,"46%"),d()(),u(642,"div",259)(643,"h5"),g(644,"$59,8478"),d(),u(645,"p",141),g(646,"Top Orders"),d()()()()(),u(647,"div",260),m(648,"div",261),d()()()()()()(),u(649,"footer",262)(650,"div",180)(651,"div",263)(652,"div",189)(653,"div",181),m(654,"div",201),d()()()()())},dependencies:[Xf],encapsulation:2})};var Xp=[{path:"",title:"Dashboard Page",component:ts}];var eh={providers:[ba(),vc({eventCoalescing:!0}),rl(Xp)]};var ns=class e{title=rr("front");static \u0275fac=function(n){return new(n||e)};static \u0275cmp=Pt({type:e,selectors:[["app-root"]],decls:1,vars:0,template:function(n,r){n&1&&m(0,"router-outlet")},dependencies:[Br],encapsulation:2})};Oc(ns,eh).catch(e=>console.error(e));
