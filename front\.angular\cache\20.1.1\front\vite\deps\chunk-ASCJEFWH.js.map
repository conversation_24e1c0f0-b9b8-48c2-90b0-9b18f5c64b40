{"version": 3, "sources": ["../../../../../../node_modules/@angular/platform-browser/fesm2022/dom_renderer.mjs", "../../../../../../node_modules/@angular/platform-browser/fesm2022/browser.mjs", "../../../../../../node_modules/@angular/common/fesm2022/module.mjs", "../../../../../../node_modules/@angular/common/fesm2022/http.mjs", "../../../../../../node_modules/@angular/platform-browser/fesm2022/platform-browser.mjs"], "sourcesContent": ["/**\n * @license Angular v20.1.1\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport { DOCUMENT, ɵgetDOM as _getDOM } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, ɵRuntimeError as _RuntimeError, Injectable, Inject, APP_ID, CSP_NONCE, PLATFORM_ID, Optional, ViewEncapsulation, ɵTracingService as _TracingService, RendererStyleFlags2 } from '@angular/core';\n\n/**\n * The injection token for plugins of the `EventManager` service.\n *\n * @publicApi\n */\nconst EVENT_MANAGER_PLUGINS = new InjectionToken(ngDevMode ? 'EventManagerPlugins' : '');\n/**\n * An injectable service that provides event management for Angular\n * through a browser plug-in.\n *\n * @publicApi\n */\nclass EventManager {\n  _zone;\n  _plugins;\n  _eventNameToPlugin = new Map();\n  /**\n   * Initializes an instance of the event-manager service.\n   */\n  constructor(plugins, _zone) {\n    this._zone = _zone;\n    plugins.forEach(plugin => {\n      plugin.manager = this;\n    });\n    this._plugins = plugins.slice().reverse();\n  }\n  /**\n   * Registers a handler for a specific element and event.\n   *\n   * @param element The HTML element to receive event notifications.\n   * @param eventName The name of the event to listen for.\n   * @param handler A function to call when the notification occurs. Receives the\n   * event object as an argument.\n   * @param options Options that configure how the event listener is bound.\n   * @returns  A callback function that can be used to remove the handler.\n   */\n  addEventListener(element, eventName, handler, options) {\n    const plugin = this._findPluginFor(eventName);\n    return plugin.addEventListener(element, eventName, handler, options);\n  }\n  /**\n   * Retrieves the compilation zone in which event listeners are registered.\n   */\n  getZone() {\n    return this._zone;\n  }\n  /** @internal */\n  _findPluginFor(eventName) {\n    let plugin = this._eventNameToPlugin.get(eventName);\n    if (plugin) {\n      return plugin;\n    }\n    const plugins = this._plugins;\n    plugin = plugins.find(plugin => plugin.supports(eventName));\n    if (!plugin) {\n      throw new _RuntimeError(5101 /* RuntimeErrorCode.NO_PLUGIN_FOR_EVENT */, (typeof ngDevMode === 'undefined' || ngDevMode) && `No event manager plugin found for event ${eventName}`);\n    }\n    this._eventNameToPlugin.set(eventName, plugin);\n    return plugin;\n  }\n  static ɵfac = function EventManager_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || EventManager)(i0.ɵɵinject(EVENT_MANAGER_PLUGINS), i0.ɵɵinject(i0.NgZone));\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: EventManager,\n    factory: EventManager.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(EventManager, [{\n    type: Injectable\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [EVENT_MANAGER_PLUGINS]\n    }]\n  }, {\n    type: i0.NgZone\n  }], null);\n})();\n/**\n * The plugin definition for the `EventManager` class\n *\n * It can be used as a base class to create custom manager plugins, i.e. you can create your own\n * class that extends the `EventManagerPlugin` one.\n *\n * @publicApi\n */\nclass EventManagerPlugin {\n  _doc;\n  // TODO: remove (has some usage in G3)\n  constructor(_doc) {\n    this._doc = _doc;\n  }\n  // Using non-null assertion because it's set by EventManager's constructor\n  manager;\n}\n\n/** The style elements attribute name used to set value of `APP_ID` token. */\nconst APP_ID_ATTRIBUTE_NAME = 'ng-app-id';\n/**\n * Removes all provided elements from the document.\n * @param elements An array of HTML Elements.\n */\nfunction removeElements(elements) {\n  for (const element of elements) {\n    element.remove();\n  }\n}\n/**\n * Creates a `style` element with the provided inline style content.\n * @param style A string of the inline style content.\n * @param doc A DOM Document to use to create the element.\n * @returns An HTMLStyleElement instance.\n */\nfunction createStyleElement(style, doc) {\n  const styleElement = doc.createElement('style');\n  styleElement.textContent = style;\n  return styleElement;\n}\n/**\n * Searches a DOM document's head element for style elements with a matching application\n * identifier attribute (`ng-app-id`) to the provide identifier and adds usage records for each.\n * @param doc An HTML DOM document instance.\n * @param appId A string containing an Angular application identifer.\n * @param inline A Map object for tracking inline (defined via `styles` in component decorator) style usage.\n * @param external A Map object for tracking external (defined via `styleUrls` in component decorator) style usage.\n */\nfunction addServerStyles(doc, appId, inline, external) {\n  const elements = doc.head?.querySelectorAll(`style[${APP_ID_ATTRIBUTE_NAME}=\"${appId}\"],link[${APP_ID_ATTRIBUTE_NAME}=\"${appId}\"]`);\n  if (elements) {\n    for (const styleElement of elements) {\n      styleElement.removeAttribute(APP_ID_ATTRIBUTE_NAME);\n      if (styleElement instanceof HTMLLinkElement) {\n        // Only use filename from href\n        // The href is build time generated with a unique value to prevent duplicates.\n        external.set(styleElement.href.slice(styleElement.href.lastIndexOf('/') + 1), {\n          usage: 0,\n          elements: [styleElement]\n        });\n      } else if (styleElement.textContent) {\n        inline.set(styleElement.textContent, {\n          usage: 0,\n          elements: [styleElement]\n        });\n      }\n    }\n  }\n}\n/**\n * Creates a `link` element for the provided external style URL.\n * @param url A string of the URL for the stylesheet.\n * @param doc A DOM Document to use to create the element.\n * @returns An HTMLLinkElement instance.\n */\nfunction createLinkElement(url, doc) {\n  const linkElement = doc.createElement('link');\n  linkElement.setAttribute('rel', 'stylesheet');\n  linkElement.setAttribute('href', url);\n  return linkElement;\n}\nclass SharedStylesHost {\n  doc;\n  appId;\n  nonce;\n  /**\n   * Provides usage information for active inline style content and associated HTML <style> elements.\n   * Embedded styles typically originate from the `styles` metadata of a rendered component.\n   */\n  inline = new Map();\n  /**\n   * Provides usage information for active external style URLs and the associated HTML <link> elements.\n   * External styles typically originate from the `ɵɵExternalStylesFeature` of a rendered component.\n   */\n  external = new Map();\n  /**\n   * Set of host DOM nodes that will have styles attached.\n   */\n  hosts = new Set();\n  constructor(doc, appId, nonce,\n  // Cannot remove it due to backward compatibility\n  // (it seems some TGP targets might be calling this constructor directly).\n  platformId = {}) {\n    this.doc = doc;\n    this.appId = appId;\n    this.nonce = nonce;\n    addServerStyles(doc, appId, this.inline, this.external);\n    this.hosts.add(doc.head);\n  }\n  /**\n   * Adds embedded styles to the DOM via HTML `style` elements.\n   * @param styles An array of style content strings.\n   */\n  addStyles(styles, urls) {\n    for (const value of styles) {\n      this.addUsage(value, this.inline, createStyleElement);\n    }\n    urls?.forEach(value => this.addUsage(value, this.external, createLinkElement));\n  }\n  /**\n   * Removes embedded styles from the DOM that were added as HTML `style` elements.\n   * @param styles An array of style content strings.\n   */\n  removeStyles(styles, urls) {\n    for (const value of styles) {\n      this.removeUsage(value, this.inline);\n    }\n    urls?.forEach(value => this.removeUsage(value, this.external));\n  }\n  addUsage(value, usages, creator) {\n    // Attempt to get any current usage of the value\n    const record = usages.get(value);\n    // If existing, just increment the usage count\n    if (record) {\n      if ((typeof ngDevMode === 'undefined' || ngDevMode) && record.usage === 0) {\n        // A usage count of zero indicates a preexisting server generated style.\n        // This attribute is solely used for debugging purposes of SSR style reuse.\n        record.elements.forEach(element => element.setAttribute('ng-style-reused', ''));\n      }\n      record.usage++;\n    } else {\n      // Otherwise, create an entry to track the elements and add element for each host\n      usages.set(value, {\n        usage: 1,\n        elements: [...this.hosts].map(host => this.addElement(host, creator(value, this.doc)))\n      });\n    }\n  }\n  removeUsage(value, usages) {\n    // Attempt to get any current usage of the value\n    const record = usages.get(value);\n    // If there is a record, reduce the usage count and if no longer used,\n    // remove from DOM and delete usage record.\n    if (record) {\n      record.usage--;\n      if (record.usage <= 0) {\n        removeElements(record.elements);\n        usages.delete(value);\n      }\n    }\n  }\n  ngOnDestroy() {\n    for (const [, {\n      elements\n    }] of [...this.inline, ...this.external]) {\n      removeElements(elements);\n    }\n    this.hosts.clear();\n  }\n  /**\n   * Adds a host node to the set of style hosts and adds all existing style usage to\n   * the newly added host node.\n   *\n   * This is currently only used for Shadow DOM encapsulation mode.\n   */\n  addHost(hostNode) {\n    this.hosts.add(hostNode);\n    // Add existing styles to new host\n    for (const [style, {\n      elements\n    }] of this.inline) {\n      elements.push(this.addElement(hostNode, createStyleElement(style, this.doc)));\n    }\n    for (const [url, {\n      elements\n    }] of this.external) {\n      elements.push(this.addElement(hostNode, createLinkElement(url, this.doc)));\n    }\n  }\n  removeHost(hostNode) {\n    this.hosts.delete(hostNode);\n  }\n  addElement(host, element) {\n    // Add a nonce if present\n    if (this.nonce) {\n      element.setAttribute('nonce', this.nonce);\n    }\n    // Add application identifier when on the server to support client-side reuse\n    if (typeof ngServerMode !== 'undefined' && ngServerMode) {\n      element.setAttribute(APP_ID_ATTRIBUTE_NAME, this.appId);\n    }\n    // Insert the element into the DOM with the host node as parent\n    return host.appendChild(element);\n  }\n  static ɵfac = function SharedStylesHost_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || SharedStylesHost)(i0.ɵɵinject(DOCUMENT), i0.ɵɵinject(APP_ID), i0.ɵɵinject(CSP_NONCE, 8), i0.ɵɵinject(PLATFORM_ID));\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: SharedStylesHost,\n    factory: SharedStylesHost.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(SharedStylesHost, [{\n    type: Injectable\n  }], () => [{\n    type: Document,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [APP_ID]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [CSP_NONCE]\n    }, {\n      type: Optional\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [PLATFORM_ID]\n    }]\n  }], null);\n})();\nconst NAMESPACE_URIS = {\n  'svg': 'http://www.w3.org/2000/svg',\n  'xhtml': 'http://www.w3.org/1999/xhtml',\n  'xlink': 'http://www.w3.org/1999/xlink',\n  'xml': 'http://www.w3.org/XML/1998/namespace',\n  'xmlns': 'http://www.w3.org/2000/xmlns/',\n  'math': 'http://www.w3.org/1998/Math/MathML'\n};\nconst COMPONENT_REGEX = /%COMP%/g;\nconst SOURCEMAP_URL_REGEXP = /\\/\\*#\\s*sourceMappingURL=(.+?)\\s*\\*\\//;\nconst PROTOCOL_REGEXP = /^https?:/;\nconst COMPONENT_VARIABLE = '%COMP%';\nconst HOST_ATTR = `_nghost-${COMPONENT_VARIABLE}`;\nconst CONTENT_ATTR = `_ngcontent-${COMPONENT_VARIABLE}`;\n/**\n * The default value for the `REMOVE_STYLES_ON_COMPONENT_DESTROY` DI token.\n */\nconst REMOVE_STYLES_ON_COMPONENT_DESTROY_DEFAULT = true;\n/**\n * A DI token that indicates whether styles\n * of destroyed components should be removed from DOM.\n *\n * By default, the value is set to `true`.\n * @publicApi\n */\nconst REMOVE_STYLES_ON_COMPONENT_DESTROY = new InjectionToken(ngDevMode ? 'RemoveStylesOnCompDestroy' : '', {\n  providedIn: 'root',\n  factory: () => REMOVE_STYLES_ON_COMPONENT_DESTROY_DEFAULT\n});\nfunction shimContentAttribute(componentShortId) {\n  return CONTENT_ATTR.replace(COMPONENT_REGEX, componentShortId);\n}\nfunction shimHostAttribute(componentShortId) {\n  return HOST_ATTR.replace(COMPONENT_REGEX, componentShortId);\n}\nfunction shimStylesContent(compId, styles) {\n  return styles.map(s => s.replace(COMPONENT_REGEX, compId));\n}\n/**\n * Prepends a baseHref to the `sourceMappingURL` within the provided CSS content.\n * If the `sourceMappingURL` contains an inline (encoded) map, the function skips processing.\n *\n * @note For inline stylesheets, the `sourceMappingURL` is relative to the page's origin\n * and not the provided baseHref. This function is needed as when accessing the page with a URL\n * containing two or more segments.\n * For example, if the baseHref is set to `/`, and you visit a URL like `http://localhost/foo/bar`,\n * the map would be requested from `http://localhost/foo/bar/comp.css.map` instead of what you'd expect,\n * which is `http://localhost/comp.css.map`. This behavior is corrected by modifying the `sourceMappingURL`\n * to ensure external source maps are loaded relative to the baseHref.\n *\n\n * @param baseHref - The base URL to prepend to the `sourceMappingURL`.\n * @param styles - An array of CSS content strings, each potentially containing a `sourceMappingURL`.\n * @returns The updated array of CSS content strings with modified `sourceMappingURL` values,\n * or the original content if no modification is needed.\n */\nfunction addBaseHrefToCssSourceMap(baseHref, styles) {\n  if (!baseHref) {\n    return styles;\n  }\n  const absoluteBaseHrefUrl = new URL(baseHref, 'http://localhost');\n  return styles.map(cssContent => {\n    if (!cssContent.includes('sourceMappingURL=')) {\n      return cssContent;\n    }\n    return cssContent.replace(SOURCEMAP_URL_REGEXP, (_, sourceMapUrl) => {\n      if (sourceMapUrl[0] === '/' || sourceMapUrl.startsWith('data:') || PROTOCOL_REGEXP.test(sourceMapUrl)) {\n        return `/*# sourceMappingURL=${sourceMapUrl} */`;\n      }\n      const {\n        pathname: resolvedSourceMapUrl\n      } = new URL(sourceMapUrl, absoluteBaseHrefUrl);\n      return `/*# sourceMappingURL=${resolvedSourceMapUrl} */`;\n    });\n  });\n}\nclass DomRendererFactory2 {\n  eventManager;\n  sharedStylesHost;\n  appId;\n  removeStylesOnCompDestroy;\n  doc;\n  platformId;\n  ngZone;\n  nonce;\n  tracingService;\n  rendererByCompId = new Map();\n  defaultRenderer;\n  platformIsServer;\n  constructor(eventManager, sharedStylesHost, appId, removeStylesOnCompDestroy, doc, platformId, ngZone, nonce = null, tracingService = null) {\n    this.eventManager = eventManager;\n    this.sharedStylesHost = sharedStylesHost;\n    this.appId = appId;\n    this.removeStylesOnCompDestroy = removeStylesOnCompDestroy;\n    this.doc = doc;\n    this.platformId = platformId;\n    this.ngZone = ngZone;\n    this.nonce = nonce;\n    this.tracingService = tracingService;\n    this.platformIsServer = typeof ngServerMode !== 'undefined' && ngServerMode;\n    this.defaultRenderer = new DefaultDomRenderer2(eventManager, doc, ngZone, this.platformIsServer, this.tracingService);\n  }\n  createRenderer(element, type) {\n    if (!element || !type) {\n      return this.defaultRenderer;\n    }\n    if (typeof ngServerMode !== 'undefined' && ngServerMode && type.encapsulation === ViewEncapsulation.ShadowDom) {\n      // Domino does not support shadow DOM.\n      type = {\n        ...type,\n        encapsulation: ViewEncapsulation.Emulated\n      };\n    }\n    const renderer = this.getOrCreateRenderer(element, type);\n    // Renderers have different logic due to different encapsulation behaviours.\n    // Ex: for emulated, an attribute is added to the element.\n    if (renderer instanceof EmulatedEncapsulationDomRenderer2) {\n      renderer.applyToHost(element);\n    } else if (renderer instanceof NoneEncapsulationDomRenderer) {\n      renderer.applyStyles();\n    }\n    return renderer;\n  }\n  getOrCreateRenderer(element, type) {\n    const rendererByCompId = this.rendererByCompId;\n    let renderer = rendererByCompId.get(type.id);\n    if (!renderer) {\n      const doc = this.doc;\n      const ngZone = this.ngZone;\n      const eventManager = this.eventManager;\n      const sharedStylesHost = this.sharedStylesHost;\n      const removeStylesOnCompDestroy = this.removeStylesOnCompDestroy;\n      const platformIsServer = this.platformIsServer;\n      const tracingService = this.tracingService;\n      switch (type.encapsulation) {\n        case ViewEncapsulation.Emulated:\n          renderer = new EmulatedEncapsulationDomRenderer2(eventManager, sharedStylesHost, type, this.appId, removeStylesOnCompDestroy, doc, ngZone, platformIsServer, tracingService);\n          break;\n        case ViewEncapsulation.ShadowDom:\n          return new ShadowDomRenderer(eventManager, sharedStylesHost, element, type, doc, ngZone, this.nonce, platformIsServer, tracingService);\n        default:\n          renderer = new NoneEncapsulationDomRenderer(eventManager, sharedStylesHost, type, removeStylesOnCompDestroy, doc, ngZone, platformIsServer, tracingService);\n          break;\n      }\n      rendererByCompId.set(type.id, renderer);\n    }\n    return renderer;\n  }\n  ngOnDestroy() {\n    this.rendererByCompId.clear();\n  }\n  /**\n   * Used during HMR to clear any cached data about a component.\n   * @param componentId ID of the component that is being replaced.\n   */\n  componentReplaced(componentId) {\n    this.rendererByCompId.delete(componentId);\n  }\n  static ɵfac = function DomRendererFactory2_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || DomRendererFactory2)(i0.ɵɵinject(EventManager), i0.ɵɵinject(SharedStylesHost), i0.ɵɵinject(APP_ID), i0.ɵɵinject(REMOVE_STYLES_ON_COMPONENT_DESTROY), i0.ɵɵinject(DOCUMENT), i0.ɵɵinject(PLATFORM_ID), i0.ɵɵinject(i0.NgZone), i0.ɵɵinject(CSP_NONCE), i0.ɵɵinject(_TracingService, 8));\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: DomRendererFactory2,\n    factory: DomRendererFactory2.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DomRendererFactory2, [{\n    type: Injectable\n  }], () => [{\n    type: EventManager\n  }, {\n    type: SharedStylesHost\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [APP_ID]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [REMOVE_STYLES_ON_COMPONENT_DESTROY]\n    }]\n  }, {\n    type: Document,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }, {\n    type: Object,\n    decorators: [{\n      type: Inject,\n      args: [PLATFORM_ID]\n    }]\n  }, {\n    type: i0.NgZone\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [CSP_NONCE]\n    }]\n  }, {\n    type: i0.ɵTracingService,\n    decorators: [{\n      type: Inject,\n      args: [_TracingService]\n    }, {\n      type: Optional\n    }]\n  }], null);\n})();\nclass DefaultDomRenderer2 {\n  eventManager;\n  doc;\n  ngZone;\n  platformIsServer;\n  tracingService;\n  data = Object.create(null);\n  /**\n   * By default this renderer throws when encountering synthetic properties\n   * This can be disabled for example by the AsyncAnimationRendererFactory\n   */\n  throwOnSyntheticProps = true;\n  constructor(eventManager, doc, ngZone, platformIsServer, tracingService) {\n    this.eventManager = eventManager;\n    this.doc = doc;\n    this.ngZone = ngZone;\n    this.platformIsServer = platformIsServer;\n    this.tracingService = tracingService;\n  }\n  destroy() {}\n  destroyNode = null;\n  createElement(name, namespace) {\n    if (namespace) {\n      // TODO: `|| namespace` was added in\n      // https://github.com/angular/angular/commit/2b9cc8503d48173492c29f5a271b61126104fbdb to\n      // support how Ivy passed around the namespace URI rather than short name at the time. It did\n      // not, however extend the support to other parts of the system (setAttribute, setAttribute,\n      // and the ServerRenderer). We should decide what exactly the semantics for dealing with\n      // namespaces should be and make it consistent.\n      // Related issues:\n      // https://github.com/angular/angular/issues/44028\n      // https://github.com/angular/angular/issues/44883\n      return this.doc.createElementNS(NAMESPACE_URIS[namespace] || namespace, name);\n    }\n    return this.doc.createElement(name);\n  }\n  createComment(value) {\n    return this.doc.createComment(value);\n  }\n  createText(value) {\n    return this.doc.createTextNode(value);\n  }\n  appendChild(parent, newChild) {\n    const targetParent = isTemplateNode(parent) ? parent.content : parent;\n    targetParent.appendChild(newChild);\n  }\n  insertBefore(parent, newChild, refChild) {\n    if (parent) {\n      const targetParent = isTemplateNode(parent) ? parent.content : parent;\n      targetParent.insertBefore(newChild, refChild);\n    }\n  }\n  removeChild(_parent, oldChild) {\n    oldChild.remove();\n  }\n  selectRootElement(selectorOrNode, preserveContent) {\n    let el = typeof selectorOrNode === 'string' ? this.doc.querySelector(selectorOrNode) : selectorOrNode;\n    if (!el) {\n      throw new _RuntimeError(-5104 /* RuntimeErrorCode.ROOT_NODE_NOT_FOUND */, (typeof ngDevMode === 'undefined' || ngDevMode) && `The selector \"${selectorOrNode}\" did not match any elements`);\n    }\n    if (!preserveContent) {\n      el.textContent = '';\n    }\n    return el;\n  }\n  parentNode(node) {\n    return node.parentNode;\n  }\n  nextSibling(node) {\n    return node.nextSibling;\n  }\n  setAttribute(el, name, value, namespace) {\n    if (namespace) {\n      name = namespace + ':' + name;\n      const namespaceUri = NAMESPACE_URIS[namespace];\n      if (namespaceUri) {\n        el.setAttributeNS(namespaceUri, name, value);\n      } else {\n        el.setAttribute(name, value);\n      }\n    } else {\n      el.setAttribute(name, value);\n    }\n  }\n  removeAttribute(el, name, namespace) {\n    if (namespace) {\n      const namespaceUri = NAMESPACE_URIS[namespace];\n      if (namespaceUri) {\n        el.removeAttributeNS(namespaceUri, name);\n      } else {\n        el.removeAttribute(`${namespace}:${name}`);\n      }\n    } else {\n      el.removeAttribute(name);\n    }\n  }\n  addClass(el, name) {\n    el.classList.add(name);\n  }\n  removeClass(el, name) {\n    el.classList.remove(name);\n  }\n  setStyle(el, style, value, flags) {\n    if (flags & (RendererStyleFlags2.DashCase | RendererStyleFlags2.Important)) {\n      el.style.setProperty(style, value, flags & RendererStyleFlags2.Important ? 'important' : '');\n    } else {\n      el.style[style] = value;\n    }\n  }\n  removeStyle(el, style, flags) {\n    if (flags & RendererStyleFlags2.DashCase) {\n      // removeProperty has no effect when used on camelCased properties.\n      el.style.removeProperty(style);\n    } else {\n      el.style[style] = '';\n    }\n  }\n  setProperty(el, name, value) {\n    if (el == null) {\n      return;\n    }\n    (typeof ngDevMode === 'undefined' || ngDevMode) && this.throwOnSyntheticProps && checkNoSyntheticProp(name, 'property');\n    el[name] = value;\n  }\n  setValue(node, value) {\n    node.nodeValue = value;\n  }\n  listen(target, event, callback, options) {\n    (typeof ngDevMode === 'undefined' || ngDevMode) && this.throwOnSyntheticProps && checkNoSyntheticProp(event, 'listener');\n    if (typeof target === 'string') {\n      target = _getDOM().getGlobalEventTarget(this.doc, target);\n      if (!target) {\n        throw new _RuntimeError(5102 /* RuntimeErrorCode.UNSUPPORTED_EVENT_TARGET */, (typeof ngDevMode === 'undefined' || ngDevMode) && `Unsupported event target ${target} for event ${event}`);\n      }\n    }\n    let wrappedCallback = this.decoratePreventDefault(callback);\n    if (this.tracingService?.wrapEventListener) {\n      wrappedCallback = this.tracingService.wrapEventListener(target, event, wrappedCallback);\n    }\n    return this.eventManager.addEventListener(target, event, wrappedCallback, options);\n  }\n  decoratePreventDefault(eventHandler) {\n    // `DebugNode.triggerEventHandler` needs to know if the listener was created with\n    // decoratePreventDefault or is a listener added outside the Angular context so it can handle\n    // the two differently. In the first case, the special '__ngUnwrap__' token is passed to the\n    // unwrap the listener (see below).\n    return event => {\n      // Ivy uses '__ngUnwrap__' as a special token that allows us to unwrap the function\n      // so that it can be invoked programmatically by `DebugNode.triggerEventHandler`. The\n      // debug_node can inspect the listener toString contents for the existence of this special\n      // token. Because the token is a string literal, it is ensured to not be modified by compiled\n      // code.\n      if (event === '__ngUnwrap__') {\n        return eventHandler;\n      }\n      // Run the event handler inside the ngZone because event handlers are not patched\n      // by Zone on the server. This is required only for tests.\n      const allowDefaultBehavior = typeof ngServerMode !== 'undefined' && ngServerMode ? this.ngZone.runGuarded(() => eventHandler(event)) : eventHandler(event);\n      if (allowDefaultBehavior === false) {\n        event.preventDefault();\n      }\n      return undefined;\n    };\n  }\n}\nconst AT_CHARCODE = (() => '@'.charCodeAt(0))();\nfunction checkNoSyntheticProp(name, nameKind) {\n  if (name.charCodeAt(0) === AT_CHARCODE) {\n    throw new _RuntimeError(5105 /* RuntimeErrorCode.UNEXPECTED_SYNTHETIC_PROPERTY */, `Unexpected synthetic ${nameKind} ${name} found. Please make sure that:\n  - Make sure \\`provideAnimationsAsync()\\`, \\`provideAnimations()\\` or \\`provideNoopAnimations()\\` call was added to a list of providers used to bootstrap an application.\n  - There is a corresponding animation configuration named \\`${name}\\` defined in the \\`animations\\` field of the \\`@Component\\` decorator (see https://angular.dev/api/core/Component#animations).`);\n  }\n}\nfunction isTemplateNode(node) {\n  return node.tagName === 'TEMPLATE' && node.content !== undefined;\n}\nclass ShadowDomRenderer extends DefaultDomRenderer2 {\n  sharedStylesHost;\n  hostEl;\n  shadowRoot;\n  constructor(eventManager, sharedStylesHost, hostEl, component, doc, ngZone, nonce, platformIsServer, tracingService) {\n    super(eventManager, doc, ngZone, platformIsServer, tracingService);\n    this.sharedStylesHost = sharedStylesHost;\n    this.hostEl = hostEl;\n    this.shadowRoot = hostEl.attachShadow({\n      mode: 'open'\n    });\n    this.sharedStylesHost.addHost(this.shadowRoot);\n    let styles = component.styles;\n    if (ngDevMode) {\n      // We only do this in development, as for production users should not add CSS sourcemaps to components.\n      const baseHref = _getDOM().getBaseHref(doc) ?? '';\n      styles = addBaseHrefToCssSourceMap(baseHref, styles);\n    }\n    styles = shimStylesContent(component.id, styles);\n    for (const style of styles) {\n      const styleEl = document.createElement('style');\n      if (nonce) {\n        styleEl.setAttribute('nonce', nonce);\n      }\n      styleEl.textContent = style;\n      this.shadowRoot.appendChild(styleEl);\n    }\n    // Apply any external component styles to the shadow root for the component's element.\n    // The ShadowDOM renderer uses an alternative execution path for component styles that\n    // does not use the SharedStylesHost that other encapsulation modes leverage. Much like\n    // the manual addition of embedded styles directly above, any external stylesheets\n    // must be manually added here to ensure ShadowDOM components are correctly styled.\n    // TODO: Consider reworking the DOM Renderers to consolidate style handling.\n    const styleUrls = component.getExternalStyles?.();\n    if (styleUrls) {\n      for (const styleUrl of styleUrls) {\n        const linkEl = createLinkElement(styleUrl, doc);\n        if (nonce) {\n          linkEl.setAttribute('nonce', nonce);\n        }\n        this.shadowRoot.appendChild(linkEl);\n      }\n    }\n  }\n  nodeOrShadowRoot(node) {\n    return node === this.hostEl ? this.shadowRoot : node;\n  }\n  appendChild(parent, newChild) {\n    return super.appendChild(this.nodeOrShadowRoot(parent), newChild);\n  }\n  insertBefore(parent, newChild, refChild) {\n    return super.insertBefore(this.nodeOrShadowRoot(parent), newChild, refChild);\n  }\n  removeChild(_parent, oldChild) {\n    return super.removeChild(null, oldChild);\n  }\n  parentNode(node) {\n    return this.nodeOrShadowRoot(super.parentNode(this.nodeOrShadowRoot(node)));\n  }\n  destroy() {\n    this.sharedStylesHost.removeHost(this.shadowRoot);\n  }\n}\nclass NoneEncapsulationDomRenderer extends DefaultDomRenderer2 {\n  sharedStylesHost;\n  removeStylesOnCompDestroy;\n  styles;\n  styleUrls;\n  constructor(eventManager, sharedStylesHost, component, removeStylesOnCompDestroy, doc, ngZone, platformIsServer, tracingService, compId) {\n    super(eventManager, doc, ngZone, platformIsServer, tracingService);\n    this.sharedStylesHost = sharedStylesHost;\n    this.removeStylesOnCompDestroy = removeStylesOnCompDestroy;\n    let styles = component.styles;\n    if (ngDevMode) {\n      // We only do this in development, as for production users should not add CSS sourcemaps to components.\n      const baseHref = _getDOM().getBaseHref(doc) ?? '';\n      styles = addBaseHrefToCssSourceMap(baseHref, styles);\n    }\n    this.styles = compId ? shimStylesContent(compId, styles) : styles;\n    this.styleUrls = component.getExternalStyles?.(compId);\n  }\n  applyStyles() {\n    this.sharedStylesHost.addStyles(this.styles, this.styleUrls);\n  }\n  destroy() {\n    if (!this.removeStylesOnCompDestroy) {\n      return;\n    }\n    this.sharedStylesHost.removeStyles(this.styles, this.styleUrls);\n  }\n}\nclass EmulatedEncapsulationDomRenderer2 extends NoneEncapsulationDomRenderer {\n  contentAttr;\n  hostAttr;\n  constructor(eventManager, sharedStylesHost, component, appId, removeStylesOnCompDestroy, doc, ngZone, platformIsServer, tracingService) {\n    const compId = appId + '-' + component.id;\n    super(eventManager, sharedStylesHost, component, removeStylesOnCompDestroy, doc, ngZone, platformIsServer, tracingService, compId);\n    this.contentAttr = shimContentAttribute(compId);\n    this.hostAttr = shimHostAttribute(compId);\n  }\n  applyToHost(element) {\n    this.applyStyles();\n    this.setAttribute(element, this.hostAttr, '');\n  }\n  createElement(parent, name) {\n    const el = super.createElement(parent, name);\n    super.setAttribute(el, this.contentAttr, '');\n    return el;\n  }\n}\nexport { DomRendererFactory2, EVENT_MANAGER_PLUGINS, EventManager, EventManagerPlugin, REMOVE_STYLES_ON_COMPONENT_DESTROY, SharedStylesHost };\n", "/**\n * @license Angular v20.1.1\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport { ɵDomAdapter as _DomAdapter, ɵsetRootDomAdapter as _setRootDomAdapter, ɵparseCookieValue as _parseCookieValue, ɵgetDOM as _getDOM, DOCUMENT, ɵPLATFORM_BROWSER_ID as _PLATFORM_BROWSER_ID, XhrFactory, CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { ɵglobal as _global, ɵRuntimeError as _RuntimeError, Injectable, Inject, ɵinternalCreateApplication as _internalCreateApplication, PLATFORM_ID, PLATFORM_INITIALIZER, createPlatformFactory, platformCore, InjectionToken, ɵTESTABILITY_GETTER as _TESTABILITY_GETTER, ɵTESTABILITY as _TESTABILITY, Testability, NgZone, TestabilityRegistry, ɵINJECTOR_SCOPE as _INJECTOR_SCOPE, ErrorH<PERSON>ler, RendererFactory2, inject, ApplicationModule, NgModule, ɵsetDocument as _setDocument } from '@angular/core';\nimport { EventManagerPlugin, EVENT_MANAGER_PLUGINS, DomRendererFactory2, SharedStylesHost, EventManager } from './dom_renderer.mjs';\n\n/**\n * A `DomAdapter` powered by full browser DOM APIs.\n *\n * @security Tread carefully! Interacting with the DOM directly is dangerous and\n * can introduce XSS risks.\n */\nclass BrowserDomAdapter extends _DomAdapter {\n  supportsDOMEvents = true;\n  static makeCurrent() {\n    _setRootDomAdapter(new BrowserDomAdapter());\n  }\n  onAndCancel(el, evt, listener, options) {\n    el.addEventListener(evt, listener, options);\n    return () => {\n      el.removeEventListener(evt, listener, options);\n    };\n  }\n  dispatchEvent(el, evt) {\n    el.dispatchEvent(evt);\n  }\n  remove(node) {\n    node.remove();\n  }\n  createElement(tagName, doc) {\n    doc = doc || this.getDefaultDocument();\n    return doc.createElement(tagName);\n  }\n  createHtmlDocument() {\n    return document.implementation.createHTMLDocument('fakeTitle');\n  }\n  getDefaultDocument() {\n    return document;\n  }\n  isElementNode(node) {\n    return node.nodeType === Node.ELEMENT_NODE;\n  }\n  isShadowRoot(node) {\n    return node instanceof DocumentFragment;\n  }\n  /** @deprecated No longer being used in Ivy code. To be removed in version 14. */\n  getGlobalEventTarget(doc, target) {\n    if (target === 'window') {\n      return window;\n    }\n    if (target === 'document') {\n      return doc;\n    }\n    if (target === 'body') {\n      return doc.body;\n    }\n    return null;\n  }\n  getBaseHref(doc) {\n    const href = getBaseElementHref();\n    return href == null ? null : relativePath(href);\n  }\n  resetBaseElement() {\n    baseElement = null;\n  }\n  getUserAgent() {\n    return window.navigator.userAgent;\n  }\n  getCookie(name) {\n    return _parseCookieValue(document.cookie, name);\n  }\n}\nlet baseElement = null;\nfunction getBaseElementHref() {\n  baseElement = baseElement || document.head.querySelector('base');\n  return baseElement ? baseElement.getAttribute('href') : null;\n}\nfunction relativePath(url) {\n  // The base URL doesn't really matter, we just need it so relative paths have something\n  // to resolve against. In the browser `HTMLBaseElement.href` is always absolute.\n  return new URL(url, document.baseURI).pathname;\n}\nclass BrowserGetTestability {\n  addToWindow(registry) {\n    _global['getAngularTestability'] = (elem, findInAncestors = true) => {\n      const testability = registry.findTestabilityInTree(elem, findInAncestors);\n      if (testability == null) {\n        throw new _RuntimeError(5103 /* RuntimeErrorCode.TESTABILITY_NOT_FOUND */, (typeof ngDevMode === 'undefined' || ngDevMode) && 'Could not find testability for element.');\n      }\n      return testability;\n    };\n    _global['getAllAngularTestabilities'] = () => registry.getAllTestabilities();\n    _global['getAllAngularRootElements'] = () => registry.getAllRootElements();\n    const whenAllStable = callback => {\n      const testabilities = _global['getAllAngularTestabilities']();\n      let count = testabilities.length;\n      const decrement = function () {\n        count--;\n        if (count == 0) {\n          callback();\n        }\n      };\n      testabilities.forEach(testability => {\n        testability.whenStable(decrement);\n      });\n    };\n    if (!_global['frameworkStabilizers']) {\n      _global['frameworkStabilizers'] = [];\n    }\n    _global['frameworkStabilizers'].push(whenAllStable);\n  }\n  findTestabilityInTree(registry, elem, findInAncestors) {\n    if (elem == null) {\n      return null;\n    }\n    const t = registry.getTestability(elem);\n    if (t != null) {\n      return t;\n    } else if (!findInAncestors) {\n      return null;\n    }\n    if (_getDOM().isShadowRoot(elem)) {\n      return this.findTestabilityInTree(registry, elem.host, true);\n    }\n    return this.findTestabilityInTree(registry, elem.parentElement, true);\n  }\n}\n\n/**\n * A factory for `HttpXhrBackend` that uses the `XMLHttpRequest` browser API.\n */\nclass BrowserXhr {\n  build() {\n    return new XMLHttpRequest();\n  }\n  static ɵfac = function BrowserXhr_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || BrowserXhr)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: BrowserXhr,\n    factory: BrowserXhr.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(BrowserXhr, [{\n    type: Injectable\n  }], null, null);\n})();\nclass DomEventsPlugin extends EventManagerPlugin {\n  constructor(doc) {\n    super(doc);\n  }\n  // This plugin should come last in the list of plugins, because it accepts all\n  // events.\n  supports(eventName) {\n    return true;\n  }\n  addEventListener(element, eventName, handler, options) {\n    element.addEventListener(eventName, handler, options);\n    return () => this.removeEventListener(element, eventName, handler, options);\n  }\n  removeEventListener(target, eventName, callback, options) {\n    return target.removeEventListener(eventName, callback, options);\n  }\n  static ɵfac = function DomEventsPlugin_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || DomEventsPlugin)(i0.ɵɵinject(DOCUMENT));\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: DomEventsPlugin,\n    factory: DomEventsPlugin.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DomEventsPlugin, [{\n    type: Injectable\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }], null);\n})();\n\n/**\n * Defines supported modifiers for key events.\n */\nconst MODIFIER_KEYS = ['alt', 'control', 'meta', 'shift'];\n// The following values are here for cross-browser compatibility and to match the W3C standard\n// cf https://www.w3.org/TR/DOM-Level-3-Events-key/\nconst _keyMap = {\n  '\\b': 'Backspace',\n  '\\t': 'Tab',\n  '\\x7F': 'Delete',\n  '\\x1B': 'Escape',\n  'Del': 'Delete',\n  'Esc': 'Escape',\n  'Left': 'ArrowLeft',\n  'Right': 'ArrowRight',\n  'Up': 'ArrowUp',\n  'Down': 'ArrowDown',\n  'Menu': 'ContextMenu',\n  'Scroll': 'ScrollLock',\n  'Win': 'OS'\n};\n/**\n * Retrieves modifiers from key-event objects.\n */\nconst MODIFIER_KEY_GETTERS = {\n  'alt': event => event.altKey,\n  'control': event => event.ctrlKey,\n  'meta': event => event.metaKey,\n  'shift': event => event.shiftKey\n};\n/**\n * A browser plug-in that provides support for handling of key events in Angular.\n */\nclass KeyEventsPlugin extends EventManagerPlugin {\n  /**\n   * Initializes an instance of the browser plug-in.\n   * @param doc The document in which key events will be detected.\n   */\n  constructor(doc) {\n    super(doc);\n  }\n  /**\n   * Reports whether a named key event is supported.\n   * @param eventName The event name to query.\n   * @return True if the named key event is supported.\n   */\n  supports(eventName) {\n    return KeyEventsPlugin.parseEventName(eventName) != null;\n  }\n  /**\n   * Registers a handler for a specific element and key event.\n   * @param element The HTML element to receive event notifications.\n   * @param eventName The name of the key event to listen for.\n   * @param handler A function to call when the notification occurs. Receives the\n   * event object as an argument.\n   * @returns The key event that was registered.\n   */\n  addEventListener(element, eventName, handler, options) {\n    const parsedEvent = KeyEventsPlugin.parseEventName(eventName);\n    const outsideHandler = KeyEventsPlugin.eventCallback(parsedEvent['fullKey'], handler, this.manager.getZone());\n    return this.manager.getZone().runOutsideAngular(() => {\n      return _getDOM().onAndCancel(element, parsedEvent['domEventName'], outsideHandler, options);\n    });\n  }\n  /**\n   * Parses the user provided full keyboard event definition and normalizes it for\n   * later internal use. It ensures the string is all lowercase, converts special\n   * characters to a standard spelling, and orders all the values consistently.\n   *\n   * @param eventName The name of the key event to listen for.\n   * @returns an object with the full, normalized string, and the dom event name\n   * or null in the case when the event doesn't match a keyboard event.\n   */\n  static parseEventName(eventName) {\n    const parts = eventName.toLowerCase().split('.');\n    const domEventName = parts.shift();\n    if (parts.length === 0 || !(domEventName === 'keydown' || domEventName === 'keyup')) {\n      return null;\n    }\n    const key = KeyEventsPlugin._normalizeKey(parts.pop());\n    let fullKey = '';\n    let codeIX = parts.indexOf('code');\n    if (codeIX > -1) {\n      parts.splice(codeIX, 1);\n      fullKey = 'code.';\n    }\n    MODIFIER_KEYS.forEach(modifierName => {\n      const index = parts.indexOf(modifierName);\n      if (index > -1) {\n        parts.splice(index, 1);\n        fullKey += modifierName + '.';\n      }\n    });\n    fullKey += key;\n    if (parts.length != 0 || key.length === 0) {\n      // returning null instead of throwing to let another plugin process the event\n      return null;\n    }\n    // NOTE: Please don't rewrite this as so, as it will break JSCompiler property renaming.\n    //       The code must remain in the `result['domEventName']` form.\n    // return {domEventName, fullKey};\n    const result = {};\n    result['domEventName'] = domEventName;\n    result['fullKey'] = fullKey;\n    return result;\n  }\n  /**\n   * Determines whether the actual keys pressed match the configured key code string.\n   * The `fullKeyCode` event is normalized in the `parseEventName` method when the\n   * event is attached to the DOM during the `addEventListener` call. This is unseen\n   * by the end user and is normalized for internal consistency and parsing.\n   *\n   * @param event The keyboard event.\n   * @param fullKeyCode The normalized user defined expected key event string\n   * @returns boolean.\n   */\n  static matchEventFullKeyCode(event, fullKeyCode) {\n    let keycode = _keyMap[event.key] || event.key;\n    let key = '';\n    if (fullKeyCode.indexOf('code.') > -1) {\n      keycode = event.code;\n      key = 'code.';\n    }\n    // the keycode could be unidentified so we have to check here\n    if (keycode == null || !keycode) return false;\n    keycode = keycode.toLowerCase();\n    if (keycode === ' ') {\n      keycode = 'space'; // for readability\n    } else if (keycode === '.') {\n      keycode = 'dot'; // because '.' is used as a separator in event names\n    }\n    MODIFIER_KEYS.forEach(modifierName => {\n      if (modifierName !== keycode) {\n        const modifierGetter = MODIFIER_KEY_GETTERS[modifierName];\n        if (modifierGetter(event)) {\n          key += modifierName + '.';\n        }\n      }\n    });\n    key += keycode;\n    return key === fullKeyCode;\n  }\n  /**\n   * Configures a handler callback for a key event.\n   * @param fullKey The event name that combines all simultaneous keystrokes.\n   * @param handler The function that responds to the key event.\n   * @param zone The zone in which the event occurred.\n   * @returns A callback function.\n   */\n  static eventCallback(fullKey, handler, zone) {\n    return event => {\n      if (KeyEventsPlugin.matchEventFullKeyCode(event, fullKey)) {\n        zone.runGuarded(() => handler(event));\n      }\n    };\n  }\n  /** @internal */\n  static _normalizeKey(keyName) {\n    return keyName === 'esc' ? 'escape' : keyName;\n  }\n  static ɵfac = function KeyEventsPlugin_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || KeyEventsPlugin)(i0.ɵɵinject(DOCUMENT));\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: KeyEventsPlugin,\n    factory: KeyEventsPlugin.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(KeyEventsPlugin, [{\n    type: Injectable\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }], null);\n})();\n\n/**\n * Bootstraps an instance of an Angular application and renders a standalone component as the\n * application's root component. More information about standalone components can be found in [this\n * guide](guide/components/importing).\n *\n * @usageNotes\n * The root component passed into this function *must* be a standalone one (should have the\n * `standalone: true` flag in the `@Component` decorator config).\n *\n * ```angular-ts\n * @Component({\n *   standalone: true,\n *   template: 'Hello world!'\n * })\n * class RootComponent {}\n *\n * const appRef: ApplicationRef = await bootstrapApplication(RootComponent);\n * ```\n *\n * You can add the list of providers that should be available in the application injector by\n * specifying the `providers` field in an object passed as the second argument:\n *\n * ```ts\n * await bootstrapApplication(RootComponent, {\n *   providers: [\n *     {provide: BACKEND_URL, useValue: 'https://yourdomain.com/api'}\n *   ]\n * });\n * ```\n *\n * The `importProvidersFrom` helper method can be used to collect all providers from any\n * existing NgModule (and transitively from all NgModules that it imports):\n *\n * ```ts\n * await bootstrapApplication(RootComponent, {\n *   providers: [\n *     importProvidersFrom(SomeNgModule)\n *   ]\n * });\n * ```\n *\n * Note: the `bootstrapApplication` method doesn't include [Testability](api/core/Testability) by\n * default. You can add [Testability](api/core/Testability) by getting the list of necessary\n * providers using `provideProtractorTestingSupport()` function and adding them into the `providers`\n * array, for example:\n *\n * ```ts\n * import {provideProtractorTestingSupport} from '@angular/platform-browser';\n *\n * await bootstrapApplication(RootComponent, {providers: [provideProtractorTestingSupport()]});\n * ```\n *\n * @param rootComponent A reference to a standalone component that should be rendered.\n * @param options Extra configuration for the bootstrap operation, see `ApplicationConfig` for\n *     additional info.\n * @returns A promise that returns an `ApplicationRef` instance once resolved.\n *\n * @publicApi\n */\nfunction bootstrapApplication(rootComponent, options) {\n  return _internalCreateApplication({\n    rootComponent,\n    ...createProvidersConfig(options)\n  });\n}\n/**\n * Create an instance of an Angular application without bootstrapping any components. This is useful\n * for the situation where one wants to decouple application environment creation (a platform and\n * associated injectors) from rendering components on a screen. Components can be subsequently\n * bootstrapped on the returned `ApplicationRef`.\n *\n * @param options Extra configuration for the application environment, see `ApplicationConfig` for\n *     additional info.\n * @returns A promise that returns an `ApplicationRef` instance once resolved.\n *\n * @publicApi\n */\nfunction createApplication(options) {\n  return _internalCreateApplication(createProvidersConfig(options));\n}\nfunction createProvidersConfig(options) {\n  return {\n    appProviders: [...BROWSER_MODULE_PROVIDERS, ...(options?.providers ?? [])],\n    platformProviders: INTERNAL_BROWSER_PLATFORM_PROVIDERS\n  };\n}\n/**\n * Returns a set of providers required to setup [Testability](api/core/Testability) for an\n * application bootstrapped using the `bootstrapApplication` function. The set of providers is\n * needed to support testing an application with Protractor (which relies on the Testability APIs\n * to be present).\n *\n * @returns An array of providers required to setup Testability for an application and make it\n *     available for testing using Protractor.\n *\n * @publicApi\n */\nfunction provideProtractorTestingSupport() {\n  // Return a copy to prevent changes to the original array in case any in-place\n  // alterations are performed to the `provideProtractorTestingSupport` call results in app\n  // code.\n  return [...TESTABILITY_PROVIDERS];\n}\nfunction initDomAdapter() {\n  BrowserDomAdapter.makeCurrent();\n}\nfunction errorHandler() {\n  return new ErrorHandler();\n}\nfunction _document() {\n  // Tell ivy about the global document\n  _setDocument(document);\n  return document;\n}\nconst INTERNAL_BROWSER_PLATFORM_PROVIDERS = [{\n  provide: PLATFORM_ID,\n  useValue: _PLATFORM_BROWSER_ID\n}, {\n  provide: PLATFORM_INITIALIZER,\n  useValue: initDomAdapter,\n  multi: true\n}, {\n  provide: DOCUMENT,\n  useFactory: _document\n}];\n/**\n * A factory function that returns a `PlatformRef` instance associated with browser service\n * providers.\n *\n * @publicApi\n */\nconst platformBrowser = createPlatformFactory(platformCore, 'browser', INTERNAL_BROWSER_PLATFORM_PROVIDERS);\n/**\n * Internal marker to signal whether providers from the `BrowserModule` are already present in DI.\n * This is needed to avoid loading `BrowserModule` providers twice. We can't rely on the\n * `BrowserModule` presence itself, since the standalone-based bootstrap just imports\n * `BrowserModule` providers without referencing the module itself.\n */\nconst BROWSER_MODULE_PROVIDERS_MARKER = new InjectionToken(typeof ngDevMode === 'undefined' || ngDevMode ? 'BrowserModule Providers Marker' : '');\nconst TESTABILITY_PROVIDERS = [{\n  provide: _TESTABILITY_GETTER,\n  useClass: BrowserGetTestability\n}, {\n  provide: _TESTABILITY,\n  useClass: Testability,\n  deps: [NgZone, TestabilityRegistry, _TESTABILITY_GETTER]\n}, {\n  provide: Testability,\n  // Also provide as `Testability` for backwards-compatibility.\n  useClass: Testability,\n  deps: [NgZone, TestabilityRegistry, _TESTABILITY_GETTER]\n}];\nconst BROWSER_MODULE_PROVIDERS = [{\n  provide: _INJECTOR_SCOPE,\n  useValue: 'root'\n}, {\n  provide: ErrorHandler,\n  useFactory: errorHandler\n}, {\n  provide: EVENT_MANAGER_PLUGINS,\n  useClass: DomEventsPlugin,\n  multi: true,\n  deps: [DOCUMENT]\n}, {\n  provide: EVENT_MANAGER_PLUGINS,\n  useClass: KeyEventsPlugin,\n  multi: true,\n  deps: [DOCUMENT]\n}, DomRendererFactory2, SharedStylesHost, EventManager, {\n  provide: RendererFactory2,\n  useExisting: DomRendererFactory2\n}, {\n  provide: XhrFactory,\n  useClass: BrowserXhr\n}, typeof ngDevMode === 'undefined' || ngDevMode ? {\n  provide: BROWSER_MODULE_PROVIDERS_MARKER,\n  useValue: true\n} : []];\n/**\n * Exports required infrastructure for all Angular apps.\n * Included by default in all Angular apps created with the CLI\n * `new` command.\n * Re-exports `CommonModule` and `ApplicationModule`, making their\n * exports and providers available to all apps.\n *\n * @publicApi\n */\nclass BrowserModule {\n  constructor() {\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      const providersAlreadyPresent = inject(BROWSER_MODULE_PROVIDERS_MARKER, {\n        optional: true,\n        skipSelf: true\n      });\n      if (providersAlreadyPresent) {\n        throw new _RuntimeError(5100 /* RuntimeErrorCode.BROWSER_MODULE_ALREADY_LOADED */, `Providers from the \\`BrowserModule\\` have already been loaded. If you need access ` + `to common directives such as NgIf and NgFor, import the \\`CommonModule\\` instead.`);\n      }\n    }\n  }\n  static ɵfac = function BrowserModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || BrowserModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: BrowserModule,\n    exports: [CommonModule, ApplicationModule]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    providers: [...BROWSER_MODULE_PROVIDERS, ...TESTABILITY_PROVIDERS],\n    imports: [CommonModule, ApplicationModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(BrowserModule, [{\n    type: NgModule,\n    args: [{\n      providers: [...BROWSER_MODULE_PROVIDERS, ...TESTABILITY_PROVIDERS],\n      exports: [CommonModule, ApplicationModule]\n    }]\n  }], () => [], null);\n})();\nexport { BrowserDomAdapter, BrowserGetTestability, BrowserModule, DomEventsPlugin, KeyEventsPlugin, bootstrapApplication, createApplication, platformBrowser, provideProtractorTestingSupport };\n", "/**\n * @license Angular v20.1.1\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport * as i0 from '@angular/core';\nimport { ɵRuntimeError as _RuntimeError, Injectable, InjectionToken, inject, NgZone, DestroyRef, ɵformatRuntimeError as _formatRuntimeError, PendingTasks, ɵConsole as _Console, runInInjectionContext, DOCUMENT, Inject, makeEnvironmentProviders, NgModule } from '@angular/core';\nimport { concatMap, filter, map, finalize, switchMap } from 'rxjs/operators';\nimport { of, Observable, from } from 'rxjs';\nimport { XhrFactory, parseCookieValue } from './xhr.mjs';\n\n/**\n * Transforms an `HttpRequest` into a stream of `HttpEvent`s, one of which will likely be a\n * `HttpResponse`.\n *\n * `HttpHandler` is injectable. When injected, the handler instance dispatches requests to the\n * first interceptor in the chain, which dispatches to the second, etc, eventually reaching the\n * `HttpBackend`.\n *\n * In an `HttpInterceptor`, the `HttpHandler` parameter is the next interceptor in the chain.\n *\n * @publicApi\n */\nclass HttpHandler {}\n/**\n * A final `HttpHandler` which will dispatch the request via browser HTTP APIs to a backend.\n *\n * Interceptors sit between the `HttpClient` interface and the `HttpBackend`.\n *\n * When injected, `HttpBackend` dispatches requests directly to the backend, without going\n * through the interceptor chain.\n *\n * @publicApi\n */\nclass HttpBackend {}\n\n/**\n * Represents the header configuration options for an HTTP request.\n * Instances are immutable. Modifying methods return a cloned\n * instance with the change. The original object is never changed.\n *\n * @publicApi\n */\nclass HttpHeaders {\n  /**\n   * Internal map of lowercase header names to values.\n   */\n  headers;\n  /**\n   * Internal map of lowercased header names to the normalized\n   * form of the name (the form seen first).\n   */\n  normalizedNames = new Map();\n  /**\n   * Complete the lazy initialization of this object (needed before reading).\n   */\n  lazyInit;\n  /**\n   * Queued updates to be materialized the next initialization.\n   */\n  lazyUpdate = null;\n  /**  Constructs a new HTTP header object with the given values.*/\n  constructor(headers) {\n    if (!headers) {\n      this.headers = new Map();\n    } else if (typeof headers === 'string') {\n      this.lazyInit = () => {\n        this.headers = new Map();\n        headers.split('\\n').forEach(line => {\n          const index = line.indexOf(':');\n          if (index > 0) {\n            const name = line.slice(0, index);\n            const value = line.slice(index + 1).trim();\n            this.addHeaderEntry(name, value);\n          }\n        });\n      };\n    } else if (typeof Headers !== 'undefined' && headers instanceof Headers) {\n      this.headers = new Map();\n      headers.forEach((value, name) => {\n        this.addHeaderEntry(name, value);\n      });\n    } else {\n      this.lazyInit = () => {\n        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n          assertValidHeaders(headers);\n        }\n        this.headers = new Map();\n        Object.entries(headers).forEach(([name, values]) => {\n          this.setHeaderEntries(name, values);\n        });\n      };\n    }\n  }\n  /**\n   * Checks for existence of a given header.\n   *\n   * @param name The header name to check for existence.\n   *\n   * @returns True if the header exists, false otherwise.\n   */\n  has(name) {\n    this.init();\n    return this.headers.has(name.toLowerCase());\n  }\n  /**\n   * Retrieves the first value of a given header.\n   *\n   * @param name The header name.\n   *\n   * @returns The value string if the header exists, null otherwise\n   */\n  get(name) {\n    this.init();\n    const values = this.headers.get(name.toLowerCase());\n    return values && values.length > 0 ? values[0] : null;\n  }\n  /**\n   * Retrieves the names of the headers.\n   *\n   * @returns A list of header names.\n   */\n  keys() {\n    this.init();\n    return Array.from(this.normalizedNames.values());\n  }\n  /**\n   * Retrieves a list of values for a given header.\n   *\n   * @param name The header name from which to retrieve values.\n   *\n   * @returns A string of values if the header exists, null otherwise.\n   */\n  getAll(name) {\n    this.init();\n    return this.headers.get(name.toLowerCase()) || null;\n  }\n  /**\n   * Appends a new value to the existing set of values for a header\n   * and returns them in a clone of the original instance.\n   *\n   * @param name The header name for which to append the values.\n   * @param value The value to append.\n   *\n   * @returns A clone of the HTTP headers object with the value appended to the given header.\n   */\n  append(name, value) {\n    return this.clone({\n      name,\n      value,\n      op: 'a'\n    });\n  }\n  /**\n   * Sets or modifies a value for a given header in a clone of the original instance.\n   * If the header already exists, its value is replaced with the given value\n   * in the returned object.\n   *\n   * @param name The header name.\n   * @param value The value or values to set or override for the given header.\n   *\n   * @returns A clone of the HTTP headers object with the newly set header value.\n   */\n  set(name, value) {\n    return this.clone({\n      name,\n      value,\n      op: 's'\n    });\n  }\n  /**\n   * Deletes values for a given header in a clone of the original instance.\n   *\n   * @param name The header name.\n   * @param value The value or values to delete for the given header.\n   *\n   * @returns A clone of the HTTP headers object with the given value deleted.\n   */\n  delete(name, value) {\n    return this.clone({\n      name,\n      value,\n      op: 'd'\n    });\n  }\n  maybeSetNormalizedName(name, lcName) {\n    if (!this.normalizedNames.has(lcName)) {\n      this.normalizedNames.set(lcName, name);\n    }\n  }\n  init() {\n    if (!!this.lazyInit) {\n      if (this.lazyInit instanceof HttpHeaders) {\n        this.copyFrom(this.lazyInit);\n      } else {\n        this.lazyInit();\n      }\n      this.lazyInit = null;\n      if (!!this.lazyUpdate) {\n        this.lazyUpdate.forEach(update => this.applyUpdate(update));\n        this.lazyUpdate = null;\n      }\n    }\n  }\n  copyFrom(other) {\n    other.init();\n    Array.from(other.headers.keys()).forEach(key => {\n      this.headers.set(key, other.headers.get(key));\n      this.normalizedNames.set(key, other.normalizedNames.get(key));\n    });\n  }\n  clone(update) {\n    const clone = new HttpHeaders();\n    clone.lazyInit = !!this.lazyInit && this.lazyInit instanceof HttpHeaders ? this.lazyInit : this;\n    clone.lazyUpdate = (this.lazyUpdate || []).concat([update]);\n    return clone;\n  }\n  applyUpdate(update) {\n    const key = update.name.toLowerCase();\n    switch (update.op) {\n      case 'a':\n      case 's':\n        let value = update.value;\n        if (typeof value === 'string') {\n          value = [value];\n        }\n        if (value.length === 0) {\n          return;\n        }\n        this.maybeSetNormalizedName(update.name, key);\n        const base = (update.op === 'a' ? this.headers.get(key) : undefined) || [];\n        base.push(...value);\n        this.headers.set(key, base);\n        break;\n      case 'd':\n        const toDelete = update.value;\n        if (!toDelete) {\n          this.headers.delete(key);\n          this.normalizedNames.delete(key);\n        } else {\n          let existing = this.headers.get(key);\n          if (!existing) {\n            return;\n          }\n          existing = existing.filter(value => toDelete.indexOf(value) === -1);\n          if (existing.length === 0) {\n            this.headers.delete(key);\n            this.normalizedNames.delete(key);\n          } else {\n            this.headers.set(key, existing);\n          }\n        }\n        break;\n    }\n  }\n  addHeaderEntry(name, value) {\n    const key = name.toLowerCase();\n    this.maybeSetNormalizedName(name, key);\n    if (this.headers.has(key)) {\n      this.headers.get(key).push(value);\n    } else {\n      this.headers.set(key, [value]);\n    }\n  }\n  setHeaderEntries(name, values) {\n    const headerValues = (Array.isArray(values) ? values : [values]).map(value => value.toString());\n    const key = name.toLowerCase();\n    this.headers.set(key, headerValues);\n    this.maybeSetNormalizedName(name, key);\n  }\n  /**\n   * @internal\n   */\n  forEach(fn) {\n    this.init();\n    Array.from(this.normalizedNames.keys()).forEach(key => fn(this.normalizedNames.get(key), this.headers.get(key)));\n  }\n}\n/**\n * Verifies that the headers object has the right shape: the values\n * must be either strings, numbers or arrays. Throws an error if an invalid\n * header value is present.\n */\nfunction assertValidHeaders(headers) {\n  for (const [key, value] of Object.entries(headers)) {\n    if (!(typeof value === 'string' || typeof value === 'number') && !Array.isArray(value)) {\n      throw new Error(`Unexpected value of the \\`${key}\\` header provided. ` + `Expecting either a string, a number or an array, but got: \\`${value}\\`.`);\n    }\n  }\n}\n\n/**\n * Provides encoding and decoding of URL parameter and query-string values.\n *\n * Serializes and parses URL parameter keys and values to encode and decode them.\n * If you pass URL query parameters without encoding,\n * the query parameters can be misinterpreted at the receiving end.\n *\n *\n * @publicApi\n */\nclass HttpUrlEncodingCodec {\n  /**\n   * Encodes a key name for a URL parameter or query-string.\n   * @param key The key name.\n   * @returns The encoded key name.\n   */\n  encodeKey(key) {\n    return standardEncoding(key);\n  }\n  /**\n   * Encodes the value of a URL parameter or query-string.\n   * @param value The value.\n   * @returns The encoded value.\n   */\n  encodeValue(value) {\n    return standardEncoding(value);\n  }\n  /**\n   * Decodes an encoded URL parameter or query-string key.\n   * @param key The encoded key name.\n   * @returns The decoded key name.\n   */\n  decodeKey(key) {\n    return decodeURIComponent(key);\n  }\n  /**\n   * Decodes an encoded URL parameter or query-string value.\n   * @param value The encoded value.\n   * @returns The decoded value.\n   */\n  decodeValue(value) {\n    return decodeURIComponent(value);\n  }\n}\nfunction paramParser(rawParams, codec) {\n  const map = new Map();\n  if (rawParams.length > 0) {\n    // The `window.location.search` can be used while creating an instance of the `HttpParams` class\n    // (e.g. `new HttpParams({ fromString: window.location.search })`). The `window.location.search`\n    // may start with the `?` char, so we strip it if it's present.\n    const params = rawParams.replace(/^\\?/, '').split('&');\n    params.forEach(param => {\n      const eqIdx = param.indexOf('=');\n      const [key, val] = eqIdx == -1 ? [codec.decodeKey(param), ''] : [codec.decodeKey(param.slice(0, eqIdx)), codec.decodeValue(param.slice(eqIdx + 1))];\n      const list = map.get(key) || [];\n      list.push(val);\n      map.set(key, list);\n    });\n  }\n  return map;\n}\n/**\n * Encode input string with standard encodeURIComponent and then un-encode specific characters.\n */\nconst STANDARD_ENCODING_REGEX = /%(\\d[a-f0-9])/gi;\nconst STANDARD_ENCODING_REPLACEMENTS = {\n  '40': '@',\n  '3A': ':',\n  '24': '$',\n  '2C': ',',\n  '3B': ';',\n  '3D': '=',\n  '3F': '?',\n  '2F': '/'\n};\nfunction standardEncoding(v) {\n  return encodeURIComponent(v).replace(STANDARD_ENCODING_REGEX, (s, t) => STANDARD_ENCODING_REPLACEMENTS[t] ?? s);\n}\nfunction valueToString(value) {\n  return `${value}`;\n}\n/**\n * An HTTP request/response body that represents serialized parameters,\n * per the MIME type `application/x-www-form-urlencoded`.\n *\n * This class is immutable; all mutation operations return a new instance.\n *\n * @publicApi\n */\nclass HttpParams {\n  map;\n  encoder;\n  updates = null;\n  cloneFrom = null;\n  constructor(options = {}) {\n    this.encoder = options.encoder || new HttpUrlEncodingCodec();\n    if (options.fromString) {\n      if (options.fromObject) {\n        throw new _RuntimeError(2805 /* RuntimeErrorCode.CANNOT_SPECIFY_BOTH_FROM_STRING_AND_FROM_OBJECT */, ngDevMode && 'Cannot specify both fromString and fromObject.');\n      }\n      this.map = paramParser(options.fromString, this.encoder);\n    } else if (!!options.fromObject) {\n      this.map = new Map();\n      Object.keys(options.fromObject).forEach(key => {\n        const value = options.fromObject[key];\n        // convert the values to strings\n        const values = Array.isArray(value) ? value.map(valueToString) : [valueToString(value)];\n        this.map.set(key, values);\n      });\n    } else {\n      this.map = null;\n    }\n  }\n  /**\n   * Reports whether the body includes one or more values for a given parameter.\n   * @param param The parameter name.\n   * @returns True if the parameter has one or more values,\n   * false if it has no value or is not present.\n   */\n  has(param) {\n    this.init();\n    return this.map.has(param);\n  }\n  /**\n   * Retrieves the first value for a parameter.\n   * @param param The parameter name.\n   * @returns The first value of the given parameter,\n   * or `null` if the parameter is not present.\n   */\n  get(param) {\n    this.init();\n    const res = this.map.get(param);\n    return !!res ? res[0] : null;\n  }\n  /**\n   * Retrieves all values for a  parameter.\n   * @param param The parameter name.\n   * @returns All values in a string array,\n   * or `null` if the parameter not present.\n   */\n  getAll(param) {\n    this.init();\n    return this.map.get(param) || null;\n  }\n  /**\n   * Retrieves all the parameters for this body.\n   * @returns The parameter names in a string array.\n   */\n  keys() {\n    this.init();\n    return Array.from(this.map.keys());\n  }\n  /**\n   * Appends a new value to existing values for a parameter.\n   * @param param The parameter name.\n   * @param value The new value to add.\n   * @return A new body with the appended value.\n   */\n  append(param, value) {\n    return this.clone({\n      param,\n      value,\n      op: 'a'\n    });\n  }\n  /**\n   * Constructs a new body with appended values for the given parameter name.\n   * @param params parameters and values\n   * @return A new body with the new value.\n   */\n  appendAll(params) {\n    const updates = [];\n    Object.keys(params).forEach(param => {\n      const value = params[param];\n      if (Array.isArray(value)) {\n        value.forEach(_value => {\n          updates.push({\n            param,\n            value: _value,\n            op: 'a'\n          });\n        });\n      } else {\n        updates.push({\n          param,\n          value: value,\n          op: 'a'\n        });\n      }\n    });\n    return this.clone(updates);\n  }\n  /**\n   * Replaces the value for a parameter.\n   * @param param The parameter name.\n   * @param value The new value.\n   * @return A new body with the new value.\n   */\n  set(param, value) {\n    return this.clone({\n      param,\n      value,\n      op: 's'\n    });\n  }\n  /**\n   * Removes a given value or all values from a parameter.\n   * @param param The parameter name.\n   * @param value The value to remove, if provided.\n   * @return A new body with the given value removed, or with all values\n   * removed if no value is specified.\n   */\n  delete(param, value) {\n    return this.clone({\n      param,\n      value,\n      op: 'd'\n    });\n  }\n  /**\n   * Serializes the body to an encoded string, where key-value pairs (separated by `=`) are\n   * separated by `&`s.\n   */\n  toString() {\n    this.init();\n    return this.keys().map(key => {\n      const eKey = this.encoder.encodeKey(key);\n      // `a: ['1']` produces `'a=1'`\n      // `b: []` produces `''`\n      // `c: ['1', '2']` produces `'c=1&c=2'`\n      return this.map.get(key).map(value => eKey + '=' + this.encoder.encodeValue(value)).join('&');\n    })\n    // filter out empty values because `b: []` produces `''`\n    // which results in `a=1&&c=1&c=2` instead of `a=1&c=1&c=2` if we don't\n    .filter(param => param !== '').join('&');\n  }\n  clone(update) {\n    const clone = new HttpParams({\n      encoder: this.encoder\n    });\n    clone.cloneFrom = this.cloneFrom || this;\n    clone.updates = (this.updates || []).concat(update);\n    return clone;\n  }\n  init() {\n    if (this.map === null) {\n      this.map = new Map();\n    }\n    if (this.cloneFrom !== null) {\n      this.cloneFrom.init();\n      this.cloneFrom.keys().forEach(key => this.map.set(key, this.cloneFrom.map.get(key)));\n      this.updates.forEach(update => {\n        switch (update.op) {\n          case 'a':\n          case 's':\n            const base = (update.op === 'a' ? this.map.get(update.param) : undefined) || [];\n            base.push(valueToString(update.value));\n            this.map.set(update.param, base);\n            break;\n          case 'd':\n            if (update.value !== undefined) {\n              let base = this.map.get(update.param) || [];\n              const idx = base.indexOf(valueToString(update.value));\n              if (idx !== -1) {\n                base.splice(idx, 1);\n              }\n              if (base.length > 0) {\n                this.map.set(update.param, base);\n              } else {\n                this.map.delete(update.param);\n              }\n            } else {\n              this.map.delete(update.param);\n              break;\n            }\n        }\n      });\n      this.cloneFrom = this.updates = null;\n    }\n  }\n}\n\n/**\n * A token used to manipulate and access values stored in `HttpContext`.\n *\n * @publicApi\n */\nclass HttpContextToken {\n  defaultValue;\n  constructor(defaultValue) {\n    this.defaultValue = defaultValue;\n  }\n}\n/**\n * Http context stores arbitrary user defined values and ensures type safety without\n * actually knowing the types. It is backed by a `Map` and guarantees that keys do not clash.\n *\n * This context is mutable and is shared between cloned requests unless explicitly specified.\n *\n * @usageNotes\n *\n * ### Usage Example\n *\n * ```ts\n * // inside cache.interceptors.ts\n * export const IS_CACHE_ENABLED = new HttpContextToken<boolean>(() => false);\n *\n * export class CacheInterceptor implements HttpInterceptor {\n *\n *   intercept(req: HttpRequest<any>, delegate: HttpHandler): Observable<HttpEvent<any>> {\n *     if (req.context.get(IS_CACHE_ENABLED) === true) {\n *       return ...;\n *     }\n *     return delegate.handle(req);\n *   }\n * }\n *\n * // inside a service\n *\n * this.httpClient.get('/api/weather', {\n *   context: new HttpContext().set(IS_CACHE_ENABLED, true)\n * }).subscribe(...);\n * ```\n *\n * @publicApi\n */\nclass HttpContext {\n  map = new Map();\n  /**\n   * Store a value in the context. If a value is already present it will be overwritten.\n   *\n   * @param token The reference to an instance of `HttpContextToken`.\n   * @param value The value to store.\n   *\n   * @returns A reference to itself for easy chaining.\n   */\n  set(token, value) {\n    this.map.set(token, value);\n    return this;\n  }\n  /**\n   * Retrieve the value associated with the given token.\n   *\n   * @param token The reference to an instance of `HttpContextToken`.\n   *\n   * @returns The stored value or default if one is defined.\n   */\n  get(token) {\n    if (!this.map.has(token)) {\n      this.map.set(token, token.defaultValue());\n    }\n    return this.map.get(token);\n  }\n  /**\n   * Delete the value associated with the given token.\n   *\n   * @param token The reference to an instance of `HttpContextToken`.\n   *\n   * @returns A reference to itself for easy chaining.\n   */\n  delete(token) {\n    this.map.delete(token);\n    return this;\n  }\n  /**\n   * Checks for existence of a given token.\n   *\n   * @param token The reference to an instance of `HttpContextToken`.\n   *\n   * @returns True if the token exists, false otherwise.\n   */\n  has(token) {\n    return this.map.has(token);\n  }\n  /**\n   * @returns a list of tokens currently stored in the context.\n   */\n  keys() {\n    return this.map.keys();\n  }\n}\n\n/**\n * Determine whether the given HTTP method may include a body.\n */\nfunction mightHaveBody(method) {\n  switch (method) {\n    case 'DELETE':\n    case 'GET':\n    case 'HEAD':\n    case 'OPTIONS':\n    case 'JSONP':\n      return false;\n    default:\n      return true;\n  }\n}\n/**\n * Safely assert whether the given value is an ArrayBuffer.\n *\n * In some execution environments ArrayBuffer is not defined.\n */\nfunction isArrayBuffer(value) {\n  return typeof ArrayBuffer !== 'undefined' && value instanceof ArrayBuffer;\n}\n/**\n * Safely assert whether the given value is a Blob.\n *\n * In some execution environments Blob is not defined.\n */\nfunction isBlob(value) {\n  return typeof Blob !== 'undefined' && value instanceof Blob;\n}\n/**\n * Safely assert whether the given value is a FormData instance.\n *\n * In some execution environments FormData is not defined.\n */\nfunction isFormData(value) {\n  return typeof FormData !== 'undefined' && value instanceof FormData;\n}\n/**\n * Safely assert whether the given value is a URLSearchParams instance.\n *\n * In some execution environments URLSearchParams is not defined.\n */\nfunction isUrlSearchParams(value) {\n  return typeof URLSearchParams !== 'undefined' && value instanceof URLSearchParams;\n}\n/**\n * `Content-Type` is an HTTP header used to indicate the media type\n * (also known as MIME type) of the resource being sent to the client\n * or received from the server.\n */\nconst CONTENT_TYPE_HEADER = 'Content-Type';\n/**\n * The `Accept` header is an HTTP request header that indicates the media types\n * (or content types) the client is willing to receive from the server.\n */\nconst ACCEPT_HEADER = 'Accept';\n/**\n * `X-Request-URL` is a custom HTTP header used in older browser versions,\n * including Firefox (< 32), Chrome (< 37), Safari (< 8), and Internet Explorer,\n * to include the full URL of the request in cross-origin requests.\n */\nconst X_REQUEST_URL_HEADER = 'X-Request-URL';\n/**\n * `text/plain` is a content type used to indicate that the content being\n * sent is plain text with no special formatting or structured data\n * like HTML, XML, or JSON.\n */\nconst TEXT_CONTENT_TYPE = 'text/plain';\n/**\n * `application/json` is a content type used to indicate that the content\n * being sent is in the JSON format.\n */\nconst JSON_CONTENT_TYPE = 'application/json';\n/**\n * `application/json, text/plain, *\\/*` is a content negotiation string often seen in the\n * Accept header of HTTP requests. It indicates the types of content the client is willing\n * to accept from the server, with a preference for `application/json` and `text/plain`,\n * but also accepting any other type (*\\/*).\n */\nconst ACCEPT_HEADER_VALUE = `${JSON_CONTENT_TYPE}, ${TEXT_CONTENT_TYPE}, */*`;\n/**\n * An outgoing HTTP request with an optional typed body.\n *\n * `HttpRequest` represents an outgoing request, including URL, method,\n * headers, body, and other request configuration options. Instances should be\n * assumed to be immutable. To modify a `HttpRequest`, the `clone`\n * method should be used.\n *\n * @publicApi\n */\nclass HttpRequest {\n  url;\n  /**\n   * The request body, or `null` if one isn't set.\n   *\n   * Bodies are not enforced to be immutable, as they can include a reference to any\n   * user-defined data type. However, interceptors should take care to preserve\n   * idempotence by treating them as such.\n   */\n  body = null;\n  /**\n   * Outgoing headers for this request.\n   */\n  headers;\n  /**\n   * Shared and mutable context that can be used by interceptors\n   */\n  context;\n  /**\n   * Whether this request should be made in a way that exposes progress events.\n   *\n   * Progress events are expensive (change detection runs on each event) and so\n   * they should only be requested if the consumer intends to monitor them.\n   *\n   * Note: The `FetchBackend` doesn't support progress report on uploads.\n   */\n  reportProgress = false;\n  /**\n   * Whether this request should be sent with outgoing credentials (cookies).\n   */\n  withCredentials = false;\n  /**\n   *  The credentials mode of the request, which determines how cookies and HTTP authentication are handled.\n   *  This can affect whether cookies are sent with the request, and how authentication is handled.\n   */\n  credentials;\n  /**\n   * When using the fetch implementation and set to `true`, the browser will not abort the associated request if the page that initiated it is unloaded before the request is complete.\n   */\n  keepalive = false;\n  /**\n   * Controls how the request will interact with the browser's HTTP cache.\n   * This affects whether a response is retrieved from the cache, how it is stored, or if it bypasses the cache altogether.\n   */\n  cache;\n  /**\n   * Indicates the relative priority of the request. This may be used by the browser to decide the order in which requests are dispatched and resources fetched.\n   */\n  priority;\n  /**\n   * The mode of the request, which determines how the request will interact with the browser's security model.\n   * This can affect things like CORS (Cross-Origin Resource Sharing) and same-origin policies.\n   */\n  mode;\n  /**\n   * The redirect mode of the request, which determines how redirects are handled.\n   * This can affect whether the request follows redirects automatically, or if it fails when a redirect occurs.\n   */\n  redirect;\n  /**\n   * The expected response type of the server.\n   *\n   * This is used to parse the response appropriately before returning it to\n   * the requestee.\n   */\n  responseType = 'json';\n  /**\n   * The outgoing HTTP request method.\n   */\n  method;\n  /**\n   * Outgoing URL parameters.\n   *\n   * To pass a string representation of HTTP parameters in the URL-query-string format,\n   * the `HttpParamsOptions`' `fromString` may be used. For example:\n   *\n   * ```ts\n   * new HttpParams({fromString: 'angular=awesome'})\n   * ```\n   */\n  params;\n  /**\n   * The outgoing URL with all URL parameters set.\n   */\n  urlWithParams;\n  /**\n   * The HttpTransferCache option for the request\n   */\n  transferCache;\n  /**\n   * The timeout for the backend HTTP request in ms.\n   */\n  timeout;\n  constructor(method, url, third, fourth) {\n    this.url = url;\n    this.method = method.toUpperCase();\n    // Next, need to figure out which argument holds the HttpRequestInit\n    // options, if any.\n    let options;\n    // Check whether a body argument is expected. The only valid way to omit\n    // the body argument is to use a known no-body method like GET.\n    if (mightHaveBody(this.method) || !!fourth) {\n      // Body is the third argument, options are the fourth.\n      this.body = third !== undefined ? third : null;\n      options = fourth;\n    } else {\n      // No body required, options are the third argument. The body stays null.\n      options = third;\n    }\n    // If options have been passed, interpret them.\n    if (options) {\n      // Normalize reportProgress and withCredentials.\n      this.reportProgress = !!options.reportProgress;\n      this.withCredentials = !!options.withCredentials;\n      this.keepalive = !!options.keepalive;\n      // Override default response type of 'json' if one is provided.\n      if (!!options.responseType) {\n        this.responseType = options.responseType;\n      }\n      // Override headers if they're provided.\n      if (options.headers) {\n        this.headers = options.headers;\n      }\n      if (options.context) {\n        this.context = options.context;\n      }\n      if (options.params) {\n        this.params = options.params;\n      }\n      if (options.priority) {\n        this.priority = options.priority;\n      }\n      if (options.cache) {\n        this.cache = options.cache;\n      }\n      if (options.credentials) {\n        this.credentials = options.credentials;\n      }\n      if (typeof options.timeout === 'number') {\n        // XHR will ignore any value below 1. AbortSignals only accept unsigned integers.\n        if (options.timeout < 1 || !Number.isInteger(options.timeout)) {\n          // TODO: create a runtime error\n          throw new Error(ngDevMode ? '`timeout` must be a positive integer value' : '');\n        }\n        this.timeout = options.timeout;\n      }\n      if (options.mode) {\n        this.mode = options.mode;\n      }\n      if (options.redirect) {\n        this.redirect = options.redirect;\n      }\n      // We do want to assign transferCache even if it's falsy (false is valid value)\n      this.transferCache = options.transferCache;\n    }\n    // If no headers have been passed in, construct a new HttpHeaders instance.\n    this.headers ??= new HttpHeaders();\n    // If no context have been passed in, construct a new HttpContext instance.\n    this.context ??= new HttpContext();\n    // If no parameters have been passed in, construct a new HttpUrlEncodedParams instance.\n    if (!this.params) {\n      this.params = new HttpParams();\n      this.urlWithParams = url;\n    } else {\n      // Encode the parameters to a string in preparation for inclusion in the URL.\n      const params = this.params.toString();\n      if (params.length === 0) {\n        // No parameters, the visible URL is just the URL given at creation time.\n        this.urlWithParams = url;\n      } else {\n        // Does the URL already have query parameters? Look for '?'.\n        const qIdx = url.indexOf('?');\n        // There are 3 cases to handle:\n        // 1) No existing parameters -> append '?' followed by params.\n        // 2) '?' exists and is followed by existing query string ->\n        //    append '&' followed by params.\n        // 3) '?' exists at the end of the url -> append params directly.\n        // This basically amounts to determining the character, if any, with\n        // which to join the URL and parameters.\n        const sep = qIdx === -1 ? '?' : qIdx < url.length - 1 ? '&' : '';\n        this.urlWithParams = url + sep + params;\n      }\n    }\n  }\n  /**\n   * Transform the free-form body into a serialized format suitable for\n   * transmission to the server.\n   */\n  serializeBody() {\n    // If no body is present, no need to serialize it.\n    if (this.body === null) {\n      return null;\n    }\n    // Check whether the body is already in a serialized form. If so,\n    // it can just be returned directly.\n    if (typeof this.body === 'string' || isArrayBuffer(this.body) || isBlob(this.body) || isFormData(this.body) || isUrlSearchParams(this.body)) {\n      return this.body;\n    }\n    // Check whether the body is an instance of HttpUrlEncodedParams.\n    if (this.body instanceof HttpParams) {\n      return this.body.toString();\n    }\n    // Check whether the body is an object or array, and serialize with JSON if so.\n    if (typeof this.body === 'object' || typeof this.body === 'boolean' || Array.isArray(this.body)) {\n      return JSON.stringify(this.body);\n    }\n    // Fall back on toString() for everything else.\n    return this.body.toString();\n  }\n  /**\n   * Examine the body and attempt to infer an appropriate MIME type\n   * for it.\n   *\n   * If no such type can be inferred, this method will return `null`.\n   */\n  detectContentTypeHeader() {\n    // An empty body has no content type.\n    if (this.body === null) {\n      return null;\n    }\n    // FormData bodies rely on the browser's content type assignment.\n    if (isFormData(this.body)) {\n      return null;\n    }\n    // Blobs usually have their own content type. If it doesn't, then\n    // no type can be inferred.\n    if (isBlob(this.body)) {\n      return this.body.type || null;\n    }\n    // Array buffers have unknown contents and thus no type can be inferred.\n    if (isArrayBuffer(this.body)) {\n      return null;\n    }\n    // Technically, strings could be a form of JSON data, but it's safe enough\n    // to assume they're plain strings.\n    if (typeof this.body === 'string') {\n      return TEXT_CONTENT_TYPE;\n    }\n    // `HttpUrlEncodedParams` has its own content-type.\n    if (this.body instanceof HttpParams) {\n      return 'application/x-www-form-urlencoded;charset=UTF-8';\n    }\n    // Arrays, objects, boolean and numbers will be encoded as JSON.\n    if (typeof this.body === 'object' || typeof this.body === 'number' || typeof this.body === 'boolean') {\n      return JSON_CONTENT_TYPE;\n    }\n    // No type could be inferred.\n    return null;\n  }\n  clone(update = {}) {\n    // For method, url, and responseType, take the current value unless\n    // it is overridden in the update hash.\n    const method = update.method || this.method;\n    const url = update.url || this.url;\n    const responseType = update.responseType || this.responseType;\n    const keepalive = update.keepalive ?? this.keepalive;\n    const priority = update.priority || this.priority;\n    const cache = update.cache || this.cache;\n    const mode = update.mode || this.mode;\n    const redirect = update.redirect || this.redirect;\n    const credentials = update.credentials || this.credentials;\n    // Carefully handle the transferCache to differentiate between\n    // `false` and `undefined` in the update args.\n    const transferCache = update.transferCache ?? this.transferCache;\n    const timeout = update.timeout ?? this.timeout;\n    // The body is somewhat special - a `null` value in update.body means\n    // whatever current body is present is being overridden with an empty\n    // body, whereas an `undefined` value in update.body implies no\n    // override.\n    const body = update.body !== undefined ? update.body : this.body;\n    // Carefully handle the boolean options to differentiate between\n    // `false` and `undefined` in the update args.\n    const withCredentials = update.withCredentials ?? this.withCredentials;\n    const reportProgress = update.reportProgress ?? this.reportProgress;\n    // Headers and params may be appended to if `setHeaders` or\n    // `setParams` are used.\n    let headers = update.headers || this.headers;\n    let params = update.params || this.params;\n    // Pass on context if needed\n    const context = update.context ?? this.context;\n    // Check whether the caller has asked to add headers.\n    if (update.setHeaders !== undefined) {\n      // Set every requested header.\n      headers = Object.keys(update.setHeaders).reduce((headers, name) => headers.set(name, update.setHeaders[name]), headers);\n    }\n    // Check whether the caller has asked to set params.\n    if (update.setParams) {\n      // Set every requested param.\n      params = Object.keys(update.setParams).reduce((params, param) => params.set(param, update.setParams[param]), params);\n    }\n    // Finally, construct the new HttpRequest using the pieces from above.\n    return new HttpRequest(method, url, body, {\n      params,\n      headers,\n      context,\n      reportProgress,\n      responseType,\n      withCredentials,\n      transferCache,\n      keepalive,\n      cache,\n      priority,\n      timeout,\n      mode,\n      redirect,\n      credentials\n    });\n  }\n}\n\n/**\n * Type enumeration for the different kinds of `HttpEvent`.\n *\n * @publicApi\n */\nvar HttpEventType;\n(function (HttpEventType) {\n  /**\n   * The request was sent out over the wire.\n   */\n  HttpEventType[HttpEventType[\"Sent\"] = 0] = \"Sent\";\n  /**\n   * An upload progress event was received.\n   *\n   * Note: The `FetchBackend` doesn't support progress report on uploads.\n   */\n  HttpEventType[HttpEventType[\"UploadProgress\"] = 1] = \"UploadProgress\";\n  /**\n   * The response status code and headers were received.\n   */\n  HttpEventType[HttpEventType[\"ResponseHeader\"] = 2] = \"ResponseHeader\";\n  /**\n   * A download progress event was received.\n   */\n  HttpEventType[HttpEventType[\"DownloadProgress\"] = 3] = \"DownloadProgress\";\n  /**\n   * The full response including the body was received.\n   */\n  HttpEventType[HttpEventType[\"Response\"] = 4] = \"Response\";\n  /**\n   * A custom event from an interceptor or a backend.\n   */\n  HttpEventType[HttpEventType[\"User\"] = 5] = \"User\";\n})(HttpEventType || (HttpEventType = {}));\n/**\n * Base class for both `HttpResponse` and `HttpHeaderResponse`.\n *\n * @publicApi\n */\nclass HttpResponseBase {\n  /**\n   * All response headers.\n   */\n  headers;\n  /**\n   * Response status code.\n   */\n  status;\n  /**\n   * Textual description of response status code, defaults to OK.\n   *\n   * Do not depend on this.\n   */\n  statusText;\n  /**\n   * URL of the resource retrieved, or null if not available.\n   */\n  url;\n  /**\n   * Whether the status code falls in the 2xx range.\n   */\n  ok;\n  /**\n   * Type of the response, narrowed to either the full response or the header.\n   */\n  type;\n  /**\n   * Super-constructor for all responses.\n   *\n   * The single parameter accepted is an initialization hash. Any properties\n   * of the response passed there will override the default values.\n   */\n  constructor(init, defaultStatus = 200, defaultStatusText = 'OK') {\n    // If the hash has values passed, use them to initialize the response.\n    // Otherwise use the default values.\n    this.headers = init.headers || new HttpHeaders();\n    this.status = init.status !== undefined ? init.status : defaultStatus;\n    this.statusText = init.statusText || defaultStatusText;\n    this.url = init.url || null;\n    // Cache the ok value to avoid defining a getter.\n    this.ok = this.status >= 200 && this.status < 300;\n  }\n}\n/**\n * A partial HTTP response which only includes the status and header data,\n * but no response body.\n *\n * `HttpHeaderResponse` is a `HttpEvent` available on the response\n * event stream, only when progress events are requested.\n *\n * @publicApi\n */\nclass HttpHeaderResponse extends HttpResponseBase {\n  /**\n   * Create a new `HttpHeaderResponse` with the given parameters.\n   */\n  constructor(init = {}) {\n    super(init);\n  }\n  type = HttpEventType.ResponseHeader;\n  /**\n   * Copy this `HttpHeaderResponse`, overriding its contents with the\n   * given parameter hash.\n   */\n  clone(update = {}) {\n    // Perform a straightforward initialization of the new HttpHeaderResponse,\n    // overriding the current parameters with new ones if given.\n    return new HttpHeaderResponse({\n      headers: update.headers || this.headers,\n      status: update.status !== undefined ? update.status : this.status,\n      statusText: update.statusText || this.statusText,\n      url: update.url || this.url || undefined\n    });\n  }\n}\n/**\n * A full HTTP response, including a typed response body (which may be `null`\n * if one was not returned).\n *\n * `HttpResponse` is a `HttpEvent` available on the response event\n * stream.\n *\n * @publicApi\n */\nclass HttpResponse extends HttpResponseBase {\n  /**\n   * The response body, or `null` if one was not returned.\n   */\n  body;\n  /**\n   * Construct a new `HttpResponse`.\n   */\n  constructor(init = {}) {\n    super(init);\n    this.body = init.body !== undefined ? init.body : null;\n  }\n  type = HttpEventType.Response;\n  clone(update = {}) {\n    return new HttpResponse({\n      body: update.body !== undefined ? update.body : this.body,\n      headers: update.headers || this.headers,\n      status: update.status !== undefined ? update.status : this.status,\n      statusText: update.statusText || this.statusText,\n      url: update.url || this.url || undefined\n    });\n  }\n}\n/**\n * A response that represents an error or failure, either from a\n * non-successful HTTP status, an error while executing the request,\n * or some other failure which occurred during the parsing of the response.\n *\n * Any error returned on the `Observable` response stream will be\n * wrapped in an `HttpErrorResponse` to provide additional context about\n * the state of the HTTP layer when the error occurred. The error property\n * will contain either a wrapped Error object or the error response returned\n * from the server.\n *\n * @publicApi\n */\nclass HttpErrorResponse extends HttpResponseBase {\n  name = 'HttpErrorResponse';\n  message;\n  error;\n  /**\n   * Errors are never okay, even when the status code is in the 2xx success range.\n   */\n  ok = false;\n  constructor(init) {\n    // Initialize with a default status of 0 / Unknown Error.\n    super(init, 0, 'Unknown Error');\n    // If the response was successful, then this was a parse error. Otherwise, it was\n    // a protocol-level failure of some sort. Either the request failed in transit\n    // or the server returned an unsuccessful status code.\n    if (this.status >= 200 && this.status < 300) {\n      this.message = `Http failure during parsing for ${init.url || '(unknown url)'}`;\n    } else {\n      this.message = `Http failure response for ${init.url || '(unknown url)'}: ${init.status} ${init.statusText}`;\n    }\n    this.error = init.error || null;\n  }\n}\n/**\n * We use these constant to prevent pulling the whole HttpStatusCode enum\n * Those are the only ones referenced directly by the framework\n */\nconst HTTP_STATUS_CODE_OK = 200;\nconst HTTP_STATUS_CODE_NO_CONTENT = 204;\n/**\n * Http status codes.\n * As per https://www.iana.org/assignments/http-status-codes/http-status-codes.xhtml\n * @publicApi\n */\nvar HttpStatusCode;\n(function (HttpStatusCode) {\n  HttpStatusCode[HttpStatusCode[\"Continue\"] = 100] = \"Continue\";\n  HttpStatusCode[HttpStatusCode[\"SwitchingProtocols\"] = 101] = \"SwitchingProtocols\";\n  HttpStatusCode[HttpStatusCode[\"Processing\"] = 102] = \"Processing\";\n  HttpStatusCode[HttpStatusCode[\"EarlyHints\"] = 103] = \"EarlyHints\";\n  HttpStatusCode[HttpStatusCode[\"Ok\"] = 200] = \"Ok\";\n  HttpStatusCode[HttpStatusCode[\"Created\"] = 201] = \"Created\";\n  HttpStatusCode[HttpStatusCode[\"Accepted\"] = 202] = \"Accepted\";\n  HttpStatusCode[HttpStatusCode[\"NonAuthoritativeInformation\"] = 203] = \"NonAuthoritativeInformation\";\n  HttpStatusCode[HttpStatusCode[\"NoContent\"] = 204] = \"NoContent\";\n  HttpStatusCode[HttpStatusCode[\"ResetContent\"] = 205] = \"ResetContent\";\n  HttpStatusCode[HttpStatusCode[\"PartialContent\"] = 206] = \"PartialContent\";\n  HttpStatusCode[HttpStatusCode[\"MultiStatus\"] = 207] = \"MultiStatus\";\n  HttpStatusCode[HttpStatusCode[\"AlreadyReported\"] = 208] = \"AlreadyReported\";\n  HttpStatusCode[HttpStatusCode[\"ImUsed\"] = 226] = \"ImUsed\";\n  HttpStatusCode[HttpStatusCode[\"MultipleChoices\"] = 300] = \"MultipleChoices\";\n  HttpStatusCode[HttpStatusCode[\"MovedPermanently\"] = 301] = \"MovedPermanently\";\n  HttpStatusCode[HttpStatusCode[\"Found\"] = 302] = \"Found\";\n  HttpStatusCode[HttpStatusCode[\"SeeOther\"] = 303] = \"SeeOther\";\n  HttpStatusCode[HttpStatusCode[\"NotModified\"] = 304] = \"NotModified\";\n  HttpStatusCode[HttpStatusCode[\"UseProxy\"] = 305] = \"UseProxy\";\n  HttpStatusCode[HttpStatusCode[\"Unused\"] = 306] = \"Unused\";\n  HttpStatusCode[HttpStatusCode[\"TemporaryRedirect\"] = 307] = \"TemporaryRedirect\";\n  HttpStatusCode[HttpStatusCode[\"PermanentRedirect\"] = 308] = \"PermanentRedirect\";\n  HttpStatusCode[HttpStatusCode[\"BadRequest\"] = 400] = \"BadRequest\";\n  HttpStatusCode[HttpStatusCode[\"Unauthorized\"] = 401] = \"Unauthorized\";\n  HttpStatusCode[HttpStatusCode[\"PaymentRequired\"] = 402] = \"PaymentRequired\";\n  HttpStatusCode[HttpStatusCode[\"Forbidden\"] = 403] = \"Forbidden\";\n  HttpStatusCode[HttpStatusCode[\"NotFound\"] = 404] = \"NotFound\";\n  HttpStatusCode[HttpStatusCode[\"MethodNotAllowed\"] = 405] = \"MethodNotAllowed\";\n  HttpStatusCode[HttpStatusCode[\"NotAcceptable\"] = 406] = \"NotAcceptable\";\n  HttpStatusCode[HttpStatusCode[\"ProxyAuthenticationRequired\"] = 407] = \"ProxyAuthenticationRequired\";\n  HttpStatusCode[HttpStatusCode[\"RequestTimeout\"] = 408] = \"RequestTimeout\";\n  HttpStatusCode[HttpStatusCode[\"Conflict\"] = 409] = \"Conflict\";\n  HttpStatusCode[HttpStatusCode[\"Gone\"] = 410] = \"Gone\";\n  HttpStatusCode[HttpStatusCode[\"LengthRequired\"] = 411] = \"LengthRequired\";\n  HttpStatusCode[HttpStatusCode[\"PreconditionFailed\"] = 412] = \"PreconditionFailed\";\n  HttpStatusCode[HttpStatusCode[\"PayloadTooLarge\"] = 413] = \"PayloadTooLarge\";\n  HttpStatusCode[HttpStatusCode[\"UriTooLong\"] = 414] = \"UriTooLong\";\n  HttpStatusCode[HttpStatusCode[\"UnsupportedMediaType\"] = 415] = \"UnsupportedMediaType\";\n  HttpStatusCode[HttpStatusCode[\"RangeNotSatisfiable\"] = 416] = \"RangeNotSatisfiable\";\n  HttpStatusCode[HttpStatusCode[\"ExpectationFailed\"] = 417] = \"ExpectationFailed\";\n  HttpStatusCode[HttpStatusCode[\"ImATeapot\"] = 418] = \"ImATeapot\";\n  HttpStatusCode[HttpStatusCode[\"MisdirectedRequest\"] = 421] = \"MisdirectedRequest\";\n  HttpStatusCode[HttpStatusCode[\"UnprocessableEntity\"] = 422] = \"UnprocessableEntity\";\n  HttpStatusCode[HttpStatusCode[\"Locked\"] = 423] = \"Locked\";\n  HttpStatusCode[HttpStatusCode[\"FailedDependency\"] = 424] = \"FailedDependency\";\n  HttpStatusCode[HttpStatusCode[\"TooEarly\"] = 425] = \"TooEarly\";\n  HttpStatusCode[HttpStatusCode[\"UpgradeRequired\"] = 426] = \"UpgradeRequired\";\n  HttpStatusCode[HttpStatusCode[\"PreconditionRequired\"] = 428] = \"PreconditionRequired\";\n  HttpStatusCode[HttpStatusCode[\"TooManyRequests\"] = 429] = \"TooManyRequests\";\n  HttpStatusCode[HttpStatusCode[\"RequestHeaderFieldsTooLarge\"] = 431] = \"RequestHeaderFieldsTooLarge\";\n  HttpStatusCode[HttpStatusCode[\"UnavailableForLegalReasons\"] = 451] = \"UnavailableForLegalReasons\";\n  HttpStatusCode[HttpStatusCode[\"InternalServerError\"] = 500] = \"InternalServerError\";\n  HttpStatusCode[HttpStatusCode[\"NotImplemented\"] = 501] = \"NotImplemented\";\n  HttpStatusCode[HttpStatusCode[\"BadGateway\"] = 502] = \"BadGateway\";\n  HttpStatusCode[HttpStatusCode[\"ServiceUnavailable\"] = 503] = \"ServiceUnavailable\";\n  HttpStatusCode[HttpStatusCode[\"GatewayTimeout\"] = 504] = \"GatewayTimeout\";\n  HttpStatusCode[HttpStatusCode[\"HttpVersionNotSupported\"] = 505] = \"HttpVersionNotSupported\";\n  HttpStatusCode[HttpStatusCode[\"VariantAlsoNegotiates\"] = 506] = \"VariantAlsoNegotiates\";\n  HttpStatusCode[HttpStatusCode[\"InsufficientStorage\"] = 507] = \"InsufficientStorage\";\n  HttpStatusCode[HttpStatusCode[\"LoopDetected\"] = 508] = \"LoopDetected\";\n  HttpStatusCode[HttpStatusCode[\"NotExtended\"] = 510] = \"NotExtended\";\n  HttpStatusCode[HttpStatusCode[\"NetworkAuthenticationRequired\"] = 511] = \"NetworkAuthenticationRequired\";\n})(HttpStatusCode || (HttpStatusCode = {}));\n\n/**\n * Constructs an instance of `HttpRequestOptions<T>` from a source `HttpMethodOptions` and\n * the given `body`. This function clones the object and adds the body.\n *\n * Note that the `responseType` *options* value is a String that identifies the\n * single data type of the response.\n * A single overload version of the method handles each response type.\n * The value of `responseType` cannot be a union, as the combined signature could imply.\n *\n */\nfunction addBody(options, body) {\n  return {\n    body,\n    headers: options.headers,\n    context: options.context,\n    observe: options.observe,\n    params: options.params,\n    reportProgress: options.reportProgress,\n    responseType: options.responseType,\n    withCredentials: options.withCredentials,\n    transferCache: options.transferCache,\n    keepalive: options.keepalive,\n    priority: options.priority,\n    cache: options.cache,\n    mode: options.mode,\n    redirect: options.redirect\n  };\n}\n/**\n * Performs HTTP requests.\n * This service is available as an injectable class, with methods to perform HTTP requests.\n * Each request method has multiple signatures, and the return type varies based on\n * the signature that is called (mainly the values of `observe` and `responseType`).\n *\n * Note that the `responseType` *options* value is a String that identifies the\n * single data type of the response.\n * A single overload version of the method handles each response type.\n * The value of `responseType` cannot be a union, as the combined signature could imply.\n *\n * @usageNotes\n *\n * ### HTTP Request Example\n *\n * ```ts\n *  // GET heroes whose name contains search term\n * searchHeroes(term: string): observable<Hero[]>{\n *\n *  const params = new HttpParams({fromString: 'name=term'});\n *    return this.httpClient.request('GET', this.heroesUrl, {responseType:'json', params});\n * }\n * ```\n *\n * Alternatively, the parameter string can be used without invoking HttpParams\n * by directly joining to the URL.\n * ```ts\n * this.httpClient.request('GET', this.heroesUrl + '?' + 'name=term', {responseType:'json'});\n * ```\n *\n *\n * ### JSONP Example\n * ```ts\n * requestJsonp(url, callback = 'callback') {\n *  return this.httpClient.jsonp(this.heroesURL, callback);\n * }\n * ```\n *\n * ### PATCH Example\n * ```ts\n * // PATCH one of the heroes' name\n * patchHero (id: number, heroName: string): Observable<{}> {\n * const url = `${this.heroesUrl}/${id}`;   // PATCH api/heroes/42\n *  return this.httpClient.patch(url, {name: heroName}, httpOptions)\n *    .pipe(catchError(this.handleError('patchHero')));\n * }\n * ```\n *\n * @see [HTTP Guide](guide/http)\n * @see [HTTP Request](api/common/http/HttpRequest)\n *\n * @publicApi\n */\nclass HttpClient {\n  handler;\n  constructor(handler) {\n    this.handler = handler;\n  }\n  /**\n   * Constructs an observable for a generic HTTP request that, when subscribed,\n   * fires the request through the chain of registered interceptors and on to the\n   * server.\n   *\n   * You can pass an `HttpRequest` directly as the only parameter. In this case,\n   * the call returns an observable of the raw `HttpEvent` stream.\n   *\n   * Alternatively you can pass an HTTP method as the first parameter,\n   * a URL string as the second, and an options hash containing the request body as the third.\n   * See `addBody()`. In this case, the specified `responseType` and `observe` options determine the\n   * type of returned observable.\n   *   * The `responseType` value determines how a successful response body is parsed.\n   *   * If `responseType` is the default `json`, you can pass a type interface for the resulting\n   * object as a type parameter to the call.\n   *\n   * The `observe` value determines the return type, according to what you are interested in\n   * observing.\n   *   * An `observe` value of events returns an observable of the raw `HttpEvent` stream, including\n   * progress events by default.\n   *   * An `observe` value of response returns an observable of `HttpResponse<T>`,\n   * where the `T` parameter depends on the `responseType` and any optionally provided type\n   * parameter.\n   *   * An `observe` value of body returns an observable of `<T>` with the same `T` body type.\n   *\n   */\n  request(first, url, options = {}) {\n    let req;\n    // First, check whether the primary argument is an instance of `HttpRequest`.\n    if (first instanceof HttpRequest) {\n      // It is. The other arguments must be undefined (per the signatures) and can be\n      // ignored.\n      req = first;\n    } else {\n      // It's a string, so it represents a URL. Construct a request based on it,\n      // and incorporate the remaining arguments (assuming `GET` unless a method is\n      // provided.\n      // Figure out the headers.\n      let headers = undefined;\n      if (options.headers instanceof HttpHeaders) {\n        headers = options.headers;\n      } else {\n        headers = new HttpHeaders(options.headers);\n      }\n      // Sort out parameters.\n      let params = undefined;\n      if (!!options.params) {\n        if (options.params instanceof HttpParams) {\n          params = options.params;\n        } else {\n          params = new HttpParams({\n            fromObject: options.params\n          });\n        }\n      }\n      // Construct the request.\n      req = new HttpRequest(first, url, options.body !== undefined ? options.body : null, {\n        headers,\n        context: options.context,\n        params,\n        reportProgress: options.reportProgress,\n        // By default, JSON is assumed to be returned for all calls.\n        responseType: options.responseType || 'json',\n        withCredentials: options.withCredentials,\n        transferCache: options.transferCache,\n        keepalive: options.keepalive,\n        priority: options.priority,\n        cache: options.cache,\n        mode: options.mode,\n        redirect: options.redirect,\n        credentials: options.credentials\n      });\n    }\n    // Start with an Observable.of() the initial request, and run the handler (which\n    // includes all interceptors) inside a concatMap(). This way, the handler runs\n    // inside an Observable chain, which causes interceptors to be re-run on every\n    // subscription (this also makes retries re-run the handler, including interceptors).\n    const events$ = of(req).pipe(concatMap(req => this.handler.handle(req)));\n    // If coming via the API signature which accepts a previously constructed HttpRequest,\n    // the only option is to get the event stream. Otherwise, return the event stream if\n    // that is what was requested.\n    if (first instanceof HttpRequest || options.observe === 'events') {\n      return events$;\n    }\n    // The requested stream contains either the full response or the body. In either\n    // case, the first step is to filter the event stream to extract a stream of\n    // responses(s).\n    const res$ = events$.pipe(filter(event => event instanceof HttpResponse));\n    // Decide which stream to return.\n    switch (options.observe || 'body') {\n      case 'body':\n        // The requested stream is the body. Map the response stream to the response\n        // body. This could be done more simply, but a misbehaving interceptor might\n        // transform the response body into a different format and ignore the requested\n        // responseType. Guard against this by validating that the response is of the\n        // requested type.\n        switch (req.responseType) {\n          case 'arraybuffer':\n            return res$.pipe(map(res => {\n              // Validate that the body is an ArrayBuffer.\n              if (res.body !== null && !(res.body instanceof ArrayBuffer)) {\n                throw new _RuntimeError(2806 /* RuntimeErrorCode.RESPONSE_IS_NOT_AN_ARRAY_BUFFER */, ngDevMode && 'Response is not an ArrayBuffer.');\n              }\n              return res.body;\n            }));\n          case 'blob':\n            return res$.pipe(map(res => {\n              // Validate that the body is a Blob.\n              if (res.body !== null && !(res.body instanceof Blob)) {\n                throw new _RuntimeError(2807 /* RuntimeErrorCode.RESPONSE_IS_NOT_A_BLOB */, ngDevMode && 'Response is not a Blob.');\n              }\n              return res.body;\n            }));\n          case 'text':\n            return res$.pipe(map(res => {\n              // Validate that the body is a string.\n              if (res.body !== null && typeof res.body !== 'string') {\n                throw new _RuntimeError(2808 /* RuntimeErrorCode.RESPONSE_IS_NOT_A_STRING */, ngDevMode && 'Response is not a string.');\n              }\n              return res.body;\n            }));\n          case 'json':\n          default:\n            // No validation needed for JSON responses, as they can be of any type.\n            return res$.pipe(map(res => res.body));\n        }\n      case 'response':\n        // The response stream was requested directly, so return it.\n        return res$;\n      default:\n        // Guard against new future observe types being added.\n        throw new _RuntimeError(2809 /* RuntimeErrorCode.UNHANDLED_OBSERVE_TYPE */, ngDevMode && `Unreachable: unhandled observe type ${options.observe}}`);\n    }\n  }\n  /**\n   * Constructs an observable that, when subscribed, causes the configured\n   * `DELETE` request to execute on the server. See the individual overloads for\n   * details on the return type.\n   *\n   * @param url     The endpoint URL.\n   * @param options The HTTP options to send with the request.\n   *\n   */\n  delete(url, options = {}) {\n    return this.request('DELETE', url, options);\n  }\n  /**\n   * Constructs an observable that, when subscribed, causes the configured\n   * `GET` request to execute on the server. See the individual overloads for\n   * details on the return type.\n   */\n  get(url, options = {}) {\n    return this.request('GET', url, options);\n  }\n  /**\n   * Constructs an observable that, when subscribed, causes the configured\n   * `HEAD` request to execute on the server. The `HEAD` method returns\n   * meta information about the resource without transferring the\n   * resource itself. See the individual overloads for\n   * details on the return type.\n   */\n  head(url, options = {}) {\n    return this.request('HEAD', url, options);\n  }\n  /**\n   * Constructs an `Observable` that, when subscribed, causes a request with the special method\n   * `JSONP` to be dispatched via the interceptor pipeline.\n   * The [JSONP pattern](https://en.wikipedia.org/wiki/JSONP) works around limitations of certain\n   * API endpoints that don't support newer,\n   * and preferable [CORS](https://developer.mozilla.org/en-US/docs/Web/HTTP/CORS) protocol.\n   * JSONP treats the endpoint API as a JavaScript file and tricks the browser to process the\n   * requests even if the API endpoint is not located on the same domain (origin) as the client-side\n   * application making the request.\n   * The endpoint API must support JSONP callback for JSONP requests to work.\n   * The resource API returns the JSON response wrapped in a callback function.\n   * You can pass the callback function name as one of the query parameters.\n   * Note that JSONP requests can only be used with `GET` requests.\n   *\n   * @param url The resource URL.\n   * @param callbackParam The callback function name.\n   *\n   */\n  jsonp(url, callbackParam) {\n    return this.request('JSONP', url, {\n      params: new HttpParams().append(callbackParam, 'JSONP_CALLBACK'),\n      observe: 'body',\n      responseType: 'json'\n    });\n  }\n  /**\n   * Constructs an `Observable` that, when subscribed, causes the configured\n   * `OPTIONS` request to execute on the server. This method allows the client\n   * to determine the supported HTTP methods and other capabilities of an endpoint,\n   * without implying a resource action. See the individual overloads for\n   * details on the return type.\n   */\n  options(url, options = {}) {\n    return this.request('OPTIONS', url, options);\n  }\n  /**\n   * Constructs an observable that, when subscribed, causes the configured\n   * `PATCH` request to execute on the server. See the individual overloads for\n   * details on the return type.\n   */\n  patch(url, body, options = {}) {\n    return this.request('PATCH', url, addBody(options, body));\n  }\n  /**\n   * Constructs an observable that, when subscribed, causes the configured\n   * `POST` request to execute on the server. The server responds with the location of\n   * the replaced resource. See the individual overloads for\n   * details on the return type.\n   */\n  post(url, body, options = {}) {\n    return this.request('POST', url, addBody(options, body));\n  }\n  /**\n   * Constructs an observable that, when subscribed, causes the configured\n   * `PUT` request to execute on the server. The `PUT` method replaces an existing resource\n   * with a new set of values.\n   * See the individual overloads for details on the return type.\n   */\n  put(url, body, options = {}) {\n    return this.request('PUT', url, addBody(options, body));\n  }\n  static ɵfac = function HttpClient_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || HttpClient)(i0.ɵɵinject(HttpHandler));\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: HttpClient,\n    factory: HttpClient.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HttpClient, [{\n    type: Injectable\n  }], () => [{\n    type: HttpHandler\n  }], null);\n})();\nconst XSSI_PREFIX$1 = /^\\)\\]\\}',?\\n/;\n/**\n * Determine an appropriate URL for the response, by checking either\n * response url or the X-Request-URL header.\n */\nfunction getResponseUrl$1(response) {\n  if (response.url) {\n    return response.url;\n  }\n  // stored as lowercase in the map\n  const xRequestUrl = X_REQUEST_URL_HEADER.toLocaleLowerCase();\n  return response.headers.get(xRequestUrl);\n}\n/**\n * An internal injection token to reference `FetchBackend` implementation\n * in a tree-shakable way.\n */\nconst FETCH_BACKEND = new InjectionToken(typeof ngDevMode === 'undefined' || ngDevMode ? 'FETCH_BACKEND' : '');\n/**\n * Uses `fetch` to send requests to a backend server.\n *\n * This `FetchBackend` requires the support of the\n * [Fetch API](https://developer.mozilla.org/en-US/docs/Web/API/Fetch_API) which is available on all\n * supported browsers and on Node.js v18 or later.\n *\n * @see {@link HttpHandler}\n *\n * @publicApi\n */\nclass FetchBackend {\n  // We use an arrow function to always reference the current global implementation of `fetch`.\n  // This is helpful for cases when the global `fetch` implementation is modified by external code,\n  // see https://github.com/angular/angular/issues/57527.\n  fetchImpl = inject(FetchFactory, {\n    optional: true\n  })?.fetch ?? ((...args) => globalThis.fetch(...args));\n  ngZone = inject(NgZone);\n  destroyRef = inject(DestroyRef);\n  destroyed = false;\n  constructor() {\n    this.destroyRef.onDestroy(() => {\n      this.destroyed = true;\n    });\n  }\n  handle(request) {\n    return new Observable(observer => {\n      const aborter = new AbortController();\n      this.doRequest(request, aborter.signal, observer).then(noop, error => observer.error(new HttpErrorResponse({\n        error\n      })));\n      let timeoutId;\n      if (request.timeout) {\n        // TODO: Replace with AbortSignal.any([aborter.signal, AbortSignal.timeout(request.timeout)])\n        // when AbortSignal.any support is Baseline widely available (NET nov. 2026)\n        timeoutId = this.ngZone.runOutsideAngular(() => setTimeout(() => {\n          if (!aborter.signal.aborted) {\n            aborter.abort(new DOMException('signal timed out', 'TimeoutError'));\n          }\n        }, request.timeout));\n      }\n      return () => {\n        if (timeoutId !== undefined) {\n          clearTimeout(timeoutId);\n        }\n        aborter.abort();\n      };\n    });\n  }\n  async doRequest(request, signal, observer) {\n    const init = this.createRequestInit(request);\n    let response;\n    try {\n      // Run fetch outside of Angular zone.\n      // This is due to Node.js fetch implementation (Undici) which uses a number of setTimeouts to check if\n      // the response should eventually timeout which causes extra CD cycles every 500ms\n      const fetchPromise = this.ngZone.runOutsideAngular(() => this.fetchImpl(request.urlWithParams, {\n        signal,\n        ...init\n      }));\n      // Make sure Zone.js doesn't trigger false-positive unhandled promise\n      // error in case the Promise is rejected synchronously. See function\n      // description for additional information.\n      silenceSuperfluousUnhandledPromiseRejection(fetchPromise);\n      // Send the `Sent` event before awaiting the response.\n      observer.next({\n        type: HttpEventType.Sent\n      });\n      response = await fetchPromise;\n    } catch (error) {\n      observer.error(new HttpErrorResponse({\n        error,\n        status: error.status ?? 0,\n        statusText: error.statusText,\n        url: request.urlWithParams,\n        headers: error.headers\n      }));\n      return;\n    }\n    const headers = new HttpHeaders(response.headers);\n    const statusText = response.statusText;\n    const url = getResponseUrl$1(response) ?? request.urlWithParams;\n    let status = response.status;\n    let body = null;\n    if (request.reportProgress) {\n      observer.next(new HttpHeaderResponse({\n        headers,\n        status,\n        statusText,\n        url\n      }));\n    }\n    if (response.body) {\n      // Read Progress\n      const contentLength = response.headers.get('content-length');\n      const chunks = [];\n      const reader = response.body.getReader();\n      let receivedLength = 0;\n      let decoder;\n      let partialText;\n      // We have to check whether the Zone is defined in the global scope because this may be called\n      // when the zone is nooped.\n      const reqZone = typeof Zone !== 'undefined' && Zone.current;\n      let canceled = false;\n      // Perform response processing outside of Angular zone to\n      // ensure no excessive change detection runs are executed\n      // Here calling the async ReadableStreamDefaultReader.read() is responsible for triggering CD\n      await this.ngZone.runOutsideAngular(async () => {\n        while (true) {\n          // Prevent reading chunks if the app is destroyed. Otherwise, we risk doing\n          // unnecessary work or triggering side effects after teardown.\n          // This may happen if the app was explicitly destroyed before\n          // the response returned entirely.\n          if (this.destroyed) {\n            // Streams left in a pending state (due to `break` without cancel) may\n            // continue consuming or holding onto data behind the scenes.\n            // Calling `reader.cancel()` allows the browser or the underlying\n            // system to release any network or memory resources associated with the stream.\n            await reader.cancel();\n            canceled = true;\n            break;\n          }\n          const {\n            done,\n            value\n          } = await reader.read();\n          if (done) {\n            break;\n          }\n          chunks.push(value);\n          receivedLength += value.length;\n          if (request.reportProgress) {\n            partialText = request.responseType === 'text' ? (partialText ?? '') + (decoder ??= new TextDecoder()).decode(value, {\n              stream: true\n            }) : undefined;\n            const reportProgress = () => observer.next({\n              type: HttpEventType.DownloadProgress,\n              total: contentLength ? +contentLength : undefined,\n              loaded: receivedLength,\n              partialText\n            });\n            reqZone ? reqZone.run(reportProgress) : reportProgress();\n          }\n        }\n      });\n      // We need to manage the canceled state — because the Streams API does not\n      // expose a direct `.state` property on the reader.\n      // We need to `return` because `parseBody` may not be able to parse chunks\n      // that were only partially read (due to cancellation caused by app destruction).\n      if (canceled) {\n        observer.complete();\n        return;\n      }\n      // Combine all chunks.\n      const chunksAll = this.concatChunks(chunks, receivedLength);\n      try {\n        const contentType = response.headers.get(CONTENT_TYPE_HEADER) ?? '';\n        body = this.parseBody(request, chunksAll, contentType);\n      } catch (error) {\n        // Body loading or parsing failed\n        observer.error(new HttpErrorResponse({\n          error,\n          headers: new HttpHeaders(response.headers),\n          status: response.status,\n          statusText: response.statusText,\n          url: getResponseUrl$1(response) ?? request.urlWithParams\n        }));\n        return;\n      }\n    }\n    // Same behavior as the XhrBackend\n    if (status === 0) {\n      status = body ? HTTP_STATUS_CODE_OK : 0;\n    }\n    // ok determines whether the response will be transmitted on the event or\n    // error channel. Unsuccessful status codes (not 2xx) will always be errors,\n    // but a successful status code can still result in an error if the user\n    // asked for JSON data and the body cannot be parsed as such.\n    const ok = status >= 200 && status < 300;\n    if (ok) {\n      observer.next(new HttpResponse({\n        body,\n        headers,\n        status,\n        statusText,\n        url\n      }));\n      // The full body has been received and delivered, no further events\n      // are possible. This request is complete.\n      observer.complete();\n    } else {\n      observer.error(new HttpErrorResponse({\n        error: body,\n        headers,\n        status,\n        statusText,\n        url\n      }));\n    }\n  }\n  parseBody(request, binContent, contentType) {\n    switch (request.responseType) {\n      case 'json':\n        // stripping the XSSI when present\n        const text = new TextDecoder().decode(binContent).replace(XSSI_PREFIX$1, '');\n        return text === '' ? null : JSON.parse(text);\n      case 'text':\n        return new TextDecoder().decode(binContent);\n      case 'blob':\n        return new Blob([binContent], {\n          type: contentType\n        });\n      case 'arraybuffer':\n        return binContent.buffer;\n    }\n  }\n  createRequestInit(req) {\n    // We could share some of this logic with the XhrBackend\n    const headers = {};\n    let credentials;\n    // If the request has a credentials property, use it.\n    // Otherwise, if the request has withCredentials set to true, use 'include'.\n    credentials = req.credentials;\n    // If withCredentials is true should be set to 'include', for compatibility\n    if (req.withCredentials) {\n      // A warning is logged in development mode if the request has both\n      (typeof ngDevMode === 'undefined' || ngDevMode) && warningOptionsMessage(req);\n      credentials = 'include';\n    }\n    // Setting all the requested headers.\n    req.headers.forEach((name, values) => headers[name] = values.join(','));\n    // Add an Accept header if one isn't present already.\n    if (!req.headers.has(ACCEPT_HEADER)) {\n      headers[ACCEPT_HEADER] = ACCEPT_HEADER_VALUE;\n    }\n    // Auto-detect the Content-Type header if one isn't present already.\n    if (!req.headers.has(CONTENT_TYPE_HEADER)) {\n      const detectedType = req.detectContentTypeHeader();\n      // Sometimes Content-Type detection fails.\n      if (detectedType !== null) {\n        headers[CONTENT_TYPE_HEADER] = detectedType;\n      }\n    }\n    return {\n      body: req.serializeBody(),\n      method: req.method,\n      headers,\n      credentials,\n      keepalive: req.keepalive,\n      cache: req.cache,\n      priority: req.priority,\n      mode: req.mode,\n      redirect: req.redirect\n    };\n  }\n  concatChunks(chunks, totalLength) {\n    const chunksAll = new Uint8Array(totalLength);\n    let position = 0;\n    for (const chunk of chunks) {\n      chunksAll.set(chunk, position);\n      position += chunk.length;\n    }\n    return chunksAll;\n  }\n  static ɵfac = function FetchBackend_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || FetchBackend)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: FetchBackend,\n    factory: FetchBackend.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FetchBackend, [{\n    type: Injectable\n  }], () => [], null);\n})();\n/**\n * Abstract class to provide a mocked implementation of `fetch()`\n */\nclass FetchFactory {}\nfunction noop() {}\nfunction warningOptionsMessage(req) {\n  if (req.credentials && req.withCredentials) {\n    console.warn(_formatRuntimeError(2819 /* RuntimeErrorCode.WITH_CREDENTIALS_OVERRIDES_EXPLICIT_CREDENTIALS */, `Angular detected that a \\`HttpClient\\` request has both \\`withCredentials: true\\` and \\`credentials: '${req.credentials}'\\` options. The \\`withCredentials\\` option is overriding the explicit \\`credentials\\` setting to 'include'. Consider removing \\`withCredentials\\` and using \\`credentials: '${req.credentials}'\\` directly for clarity.`));\n  }\n}\n/**\n * Zone.js treats a rejected promise that has not yet been awaited\n * as an unhandled error. This function adds a noop `.then` to make\n * sure that Zone.js doesn't throw an error if the Promise is rejected\n * synchronously.\n */\nfunction silenceSuperfluousUnhandledPromiseRejection(promise) {\n  promise.then(noop, noop);\n}\nfunction interceptorChainEndFn(req, finalHandlerFn) {\n  return finalHandlerFn(req);\n}\n/**\n * Constructs a `ChainedInterceptorFn` which adapts a legacy `HttpInterceptor` to the\n * `ChainedInterceptorFn` interface.\n */\nfunction adaptLegacyInterceptorToChain(chainTailFn, interceptor) {\n  return (initialRequest, finalHandlerFn) => interceptor.intercept(initialRequest, {\n    handle: downstreamRequest => chainTailFn(downstreamRequest, finalHandlerFn)\n  });\n}\n/**\n * Constructs a `ChainedInterceptorFn` which wraps and invokes a functional interceptor in the given\n * injector.\n */\nfunction chainedInterceptorFn(chainTailFn, interceptorFn, injector) {\n  return (initialRequest, finalHandlerFn) => runInInjectionContext(injector, () => interceptorFn(initialRequest, downstreamRequest => chainTailFn(downstreamRequest, finalHandlerFn)));\n}\n/**\n * A multi-provider token that represents the array of registered\n * `HttpInterceptor` objects.\n *\n * @publicApi\n */\nconst HTTP_INTERCEPTORS = new InjectionToken(ngDevMode ? 'HTTP_INTERCEPTORS' : '');\n/**\n * A multi-provided token of `HttpInterceptorFn`s.\n */\nconst HTTP_INTERCEPTOR_FNS = new InjectionToken(ngDevMode ? 'HTTP_INTERCEPTOR_FNS' : '');\n/**\n * A multi-provided token of `HttpInterceptorFn`s that are only set in root.\n */\nconst HTTP_ROOT_INTERCEPTOR_FNS = new InjectionToken(ngDevMode ? 'HTTP_ROOT_INTERCEPTOR_FNS' : '');\n// TODO(atscott): We need a larger discussion about stability and what should contribute to stability.\n// Should the whole interceptor chain contribute to stability or just the backend request #55075?\n// Should HttpClient contribute to stability automatically at all?\nconst REQUESTS_CONTRIBUTE_TO_STABILITY = new InjectionToken(ngDevMode ? 'REQUESTS_CONTRIBUTE_TO_STABILITY' : '', {\n  providedIn: 'root',\n  factory: () => true\n});\n/**\n * Creates an `HttpInterceptorFn` which lazily initializes an interceptor chain from the legacy\n * class-based interceptors and runs the request through it.\n */\nfunction legacyInterceptorFnFactory() {\n  let chain = null;\n  return (req, handler) => {\n    if (chain === null) {\n      const interceptors = inject(HTTP_INTERCEPTORS, {\n        optional: true\n      }) ?? [];\n      // Note: interceptors are wrapped right-to-left so that final execution order is\n      // left-to-right. That is, if `interceptors` is the array `[a, b, c]`, we want to\n      // produce a chain that is conceptually `c(b(a(end)))`, which we build from the inside\n      // out.\n      chain = interceptors.reduceRight(adaptLegacyInterceptorToChain, interceptorChainEndFn);\n    }\n    const pendingTasks = inject(PendingTasks);\n    const contributeToStability = inject(REQUESTS_CONTRIBUTE_TO_STABILITY);\n    if (contributeToStability) {\n      const removeTask = pendingTasks.add();\n      return chain(req, handler).pipe(finalize(removeTask));\n    } else {\n      return chain(req, handler);\n    }\n  };\n}\nlet fetchBackendWarningDisplayed = false;\nclass HttpInterceptorHandler extends HttpHandler {\n  backend;\n  injector;\n  chain = null;\n  pendingTasks = inject(PendingTasks);\n  contributeToStability = inject(REQUESTS_CONTRIBUTE_TO_STABILITY);\n  constructor(backend, injector) {\n    super();\n    this.backend = backend;\n    this.injector = injector;\n    // We strongly recommend using fetch backend for HTTP calls when SSR is used\n    // for an application. The logic below checks if that's the case and produces\n    // a warning otherwise.\n    if ((typeof ngDevMode === 'undefined' || ngDevMode) && !fetchBackendWarningDisplayed) {\n      // This flag is necessary because provideHttpClientTesting() overrides the backend\n      // even if `withFetch()` is used within the test. When the testing HTTP backend is provided,\n      // no HTTP calls are actually performed during the test, so producing a warning would be\n      // misleading.\n      const isTestingBackend = this.backend.isTestingBackend;\n      if (typeof ngServerMode !== 'undefined' && ngServerMode && !(this.backend instanceof FetchBackend) && !isTestingBackend) {\n        fetchBackendWarningDisplayed = true;\n        injector.get(_Console).warn(_formatRuntimeError(2801 /* RuntimeErrorCode.NOT_USING_FETCH_BACKEND_IN_SSR */, 'Angular detected that `HttpClient` is not configured ' + \"to use `fetch` APIs. It's strongly recommended to \" + 'enable `fetch` for applications that use Server-Side Rendering ' + 'for better performance and compatibility. ' + 'To enable `fetch`, add the `withFetch()` to the `provideHttpClient()` ' + 'call at the root of the application.'));\n      }\n    }\n  }\n  handle(initialRequest) {\n    if (this.chain === null) {\n      const dedupedInterceptorFns = Array.from(new Set([...this.injector.get(HTTP_INTERCEPTOR_FNS), ...this.injector.get(HTTP_ROOT_INTERCEPTOR_FNS, [])]));\n      // Note: interceptors are wrapped right-to-left so that final execution order is\n      // left-to-right. That is, if `dedupedInterceptorFns` is the array `[a, b, c]`, we want to\n      // produce a chain that is conceptually `c(b(a(end)))`, which we build from the inside\n      // out.\n      this.chain = dedupedInterceptorFns.reduceRight((nextSequencedFn, interceptorFn) => chainedInterceptorFn(nextSequencedFn, interceptorFn, this.injector), interceptorChainEndFn);\n    }\n    if (this.contributeToStability) {\n      const removeTask = this.pendingTasks.add();\n      return this.chain(initialRequest, downstreamRequest => this.backend.handle(downstreamRequest)).pipe(finalize(removeTask));\n    } else {\n      return this.chain(initialRequest, downstreamRequest => this.backend.handle(downstreamRequest));\n    }\n  }\n  static ɵfac = function HttpInterceptorHandler_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || HttpInterceptorHandler)(i0.ɵɵinject(HttpBackend), i0.ɵɵinject(i0.EnvironmentInjector));\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: HttpInterceptorHandler,\n    factory: HttpInterceptorHandler.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HttpInterceptorHandler, [{\n    type: Injectable\n  }], () => [{\n    type: HttpBackend\n  }, {\n    type: i0.EnvironmentInjector\n  }], null);\n})();\n\n// Every request made through JSONP needs a callback name that's unique across the\n// whole page. Each request is assigned an id and the callback name is constructed\n// from that. The next id to be assigned is tracked in a global variable here that\n// is shared among all applications on the page.\nlet nextRequestId = 0;\n/**\n * When a pending <script> is unsubscribed we'll move it to this document, so it won't be\n * executed.\n */\nlet foreignDocument;\n// Error text given when a JSONP script is injected, but doesn't invoke the callback\n// passed in its URL.\nconst JSONP_ERR_NO_CALLBACK = 'JSONP injected script did not invoke callback.';\n// Error text given when a request is passed to the JsonpClientBackend that doesn't\n// have a request method JSONP.\nconst JSONP_ERR_WRONG_METHOD = 'JSONP requests must use JSONP request method.';\nconst JSONP_ERR_WRONG_RESPONSE_TYPE = 'JSONP requests must use Json response type.';\n// Error text given when a request is passed to the JsonpClientBackend that has\n// headers set\nconst JSONP_ERR_HEADERS_NOT_SUPPORTED = 'JSONP requests do not support headers.';\n/**\n * DI token/abstract type representing a map of JSONP callbacks.\n *\n * In the browser, this should always be the `window` object.\n *\n *\n */\nclass JsonpCallbackContext {}\n/**\n * Factory function that determines where to store JSONP callbacks.\n *\n * Ordinarily JSONP callbacks are stored on the `window` object, but this may not exist\n * in test environments. In that case, callbacks are stored on an anonymous object instead.\n *\n *\n */\nfunction jsonpCallbackContext() {\n  if (typeof window === 'object') {\n    return window;\n  }\n  return {};\n}\n/**\n * Processes an `HttpRequest` with the JSONP method,\n * by performing JSONP style requests.\n * @see {@link HttpHandler}\n * @see {@link HttpXhrBackend}\n *\n * @publicApi\n */\nclass JsonpClientBackend {\n  callbackMap;\n  document;\n  /**\n   * A resolved promise that can be used to schedule microtasks in the event handlers.\n   */\n  resolvedPromise = Promise.resolve();\n  constructor(callbackMap, document) {\n    this.callbackMap = callbackMap;\n    this.document = document;\n  }\n  /**\n   * Get the name of the next callback method, by incrementing the global `nextRequestId`.\n   */\n  nextCallback() {\n    return `ng_jsonp_callback_${nextRequestId++}`;\n  }\n  /**\n   * Processes a JSONP request and returns an event stream of the results.\n   * @param req The request object.\n   * @returns An observable of the response events.\n   *\n   */\n  handle(req) {\n    // Firstly, check both the method and response type. If either doesn't match\n    // then the request was improperly routed here and cannot be handled.\n    if (req.method !== 'JSONP') {\n      throw new _RuntimeError(2810 /* RuntimeErrorCode.JSONP_WRONG_METHOD */, ngDevMode && JSONP_ERR_WRONG_METHOD);\n    } else if (req.responseType !== 'json') {\n      throw new _RuntimeError(2811 /* RuntimeErrorCode.JSONP_WRONG_RESPONSE_TYPE */, ngDevMode && JSONP_ERR_WRONG_RESPONSE_TYPE);\n    }\n    // Check the request headers. JSONP doesn't support headers and\n    // cannot set any that were supplied.\n    if (req.headers.keys().length > 0) {\n      throw new _RuntimeError(2812 /* RuntimeErrorCode.JSONP_HEADERS_NOT_SUPPORTED */, ngDevMode && JSONP_ERR_HEADERS_NOT_SUPPORTED);\n    }\n    // Everything else happens inside the Observable boundary.\n    return new Observable(observer => {\n      // The first step to make a request is to generate the callback name, and replace the\n      // callback placeholder in the URL with the name. Care has to be taken here to ensure\n      // a trailing &, if matched, gets inserted back into the URL in the correct place.\n      const callback = this.nextCallback();\n      const url = req.urlWithParams.replace(/=JSONP_CALLBACK(&|$)/, `=${callback}$1`);\n      // Construct the <script> tag and point it at the URL.\n      const node = this.document.createElement('script');\n      node.src = url;\n      // A JSONP request requires waiting for multiple callbacks. These variables\n      // are closed over and track state across those callbacks.\n      // The response object, if one has been received, or null otherwise.\n      let body = null;\n      // Whether the response callback has been called.\n      let finished = false;\n      // Set the response callback in this.callbackMap (which will be the window\n      // object in the browser. The script being loaded via the <script> tag will\n      // eventually call this callback.\n      this.callbackMap[callback] = data => {\n        // Data has been received from the JSONP script. Firstly, delete this callback.\n        delete this.callbackMap[callback];\n        // Set state to indicate data was received.\n        body = data;\n        finished = true;\n      };\n      // cleanup() is a utility closure that removes the <script> from the page and\n      // the response callback from the window. This logic is used in both the\n      // success, error, and cancellation paths, so it's extracted out for convenience.\n      const cleanup = () => {\n        node.removeEventListener('load', onLoad);\n        node.removeEventListener('error', onError);\n        // Remove the <script> tag if it's still on the page.\n        node.remove();\n        // Remove the response callback from the callbackMap (window object in the\n        // browser).\n        delete this.callbackMap[callback];\n      };\n      // onLoad() is the success callback which runs after the response callback\n      // if the JSONP script loads successfully. The event itself is unimportant.\n      // If something went wrong, onLoad() may run without the response callback\n      // having been invoked.\n      const onLoad = () => {\n        // We wrap it in an extra Promise, to ensure the microtask\n        // is scheduled after the loaded endpoint has executed any potential microtask itself,\n        // which is not guaranteed in Internet Explorer and EdgeHTML. See issue #39496\n        this.resolvedPromise.then(() => {\n          // Cleanup the page.\n          cleanup();\n          // Check whether the response callback has run.\n          if (!finished) {\n            // It hasn't, something went wrong with the request. Return an error via\n            // the Observable error path. All JSONP errors have status 0.\n            observer.error(new HttpErrorResponse({\n              url,\n              status: 0,\n              statusText: 'JSONP Error',\n              error: new Error(JSONP_ERR_NO_CALLBACK)\n            }));\n            return;\n          }\n          // Success. body either contains the response body or null if none was\n          // returned.\n          observer.next(new HttpResponse({\n            body,\n            status: HTTP_STATUS_CODE_OK,\n            statusText: 'OK',\n            url\n          }));\n          // Complete the stream, the response is over.\n          observer.complete();\n        });\n      };\n      // onError() is the error callback, which runs if the script returned generates\n      // a Javascript error. It emits the error via the Observable error channel as\n      // a HttpErrorResponse.\n      const onError = error => {\n        cleanup();\n        // Wrap the error in a HttpErrorResponse.\n        observer.error(new HttpErrorResponse({\n          error,\n          status: 0,\n          statusText: 'JSONP Error',\n          url\n        }));\n      };\n      // Subscribe to both the success (load) and error events on the <script> tag,\n      // and add it to the page.\n      node.addEventListener('load', onLoad);\n      node.addEventListener('error', onError);\n      this.document.body.appendChild(node);\n      // The request has now been successfully sent.\n      observer.next({\n        type: HttpEventType.Sent\n      });\n      // Cancellation handler.\n      return () => {\n        if (!finished) {\n          this.removeListeners(node);\n        }\n        // And finally, clean up the page.\n        cleanup();\n      };\n    });\n  }\n  removeListeners(script) {\n    // Issue #34818\n    // Changing <script>'s ownerDocument will prevent it from execution.\n    // https://html.spec.whatwg.org/multipage/scripting.html#execute-the-script-block\n    foreignDocument ??= this.document.implementation.createHTMLDocument();\n    foreignDocument.adoptNode(script);\n  }\n  static ɵfac = function JsonpClientBackend_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || JsonpClientBackend)(i0.ɵɵinject(JsonpCallbackContext), i0.ɵɵinject(DOCUMENT));\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: JsonpClientBackend,\n    factory: JsonpClientBackend.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(JsonpClientBackend, [{\n    type: Injectable\n  }], () => [{\n    type: JsonpCallbackContext\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }], null);\n})();\n/**\n * Identifies requests with the method JSONP and shifts them to the `JsonpClientBackend`.\n */\nfunction jsonpInterceptorFn(req, next) {\n  if (req.method === 'JSONP') {\n    return inject(JsonpClientBackend).handle(req);\n  }\n  // Fall through for normal HTTP requests.\n  return next(req);\n}\n/**\n * Identifies requests with the method JSONP and\n * shifts them to the `JsonpClientBackend`.\n *\n * @see {@link HttpInterceptor}\n *\n * @publicApi\n */\nclass JsonpInterceptor {\n  injector;\n  constructor(injector) {\n    this.injector = injector;\n  }\n  /**\n   * Identifies and handles a given JSONP request.\n   * @param initialRequest The outgoing request object to handle.\n   * @param next The next interceptor in the chain, or the backend\n   * if no interceptors remain in the chain.\n   * @returns An observable of the event stream.\n   */\n  intercept(initialRequest, next) {\n    return runInInjectionContext(this.injector, () => jsonpInterceptorFn(initialRequest, downstreamRequest => next.handle(downstreamRequest)));\n  }\n  static ɵfac = function JsonpInterceptor_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || JsonpInterceptor)(i0.ɵɵinject(i0.EnvironmentInjector));\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: JsonpInterceptor,\n    factory: JsonpInterceptor.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(JsonpInterceptor, [{\n    type: Injectable\n  }], () => [{\n    type: i0.EnvironmentInjector\n  }], null);\n})();\nconst XSSI_PREFIX = /^\\)\\]\\}',?\\n/;\nconst X_REQUEST_URL_REGEXP = RegExp(`^${X_REQUEST_URL_HEADER}:`, 'm');\n/**\n * Determine an appropriate URL for the response, by checking either\n * XMLHttpRequest.responseURL or the X-Request-URL header.\n */\nfunction getResponseUrl(xhr) {\n  if ('responseURL' in xhr && xhr.responseURL) {\n    return xhr.responseURL;\n  }\n  if (X_REQUEST_URL_REGEXP.test(xhr.getAllResponseHeaders())) {\n    return xhr.getResponseHeader(X_REQUEST_URL_HEADER);\n  }\n  return null;\n}\n/**\n * Validates whether the request is compatible with the XHR backend.\n * Show a warning if the request contains options that are not supported by XHR.\n */\nfunction validateXhrCompatibility(req) {\n  const unsupportedOptions = [{\n    property: 'keepalive',\n    errorCode: 2813 /* RuntimeErrorCode.KEEPALIVE_NOT_SUPPORTED_WITH_XHR */\n  }, {\n    property: 'cache',\n    errorCode: 2814 /* RuntimeErrorCode.CACHE_NOT_SUPPORTED_WITH_XHR */\n  }, {\n    property: 'priority',\n    errorCode: 2815 /* RuntimeErrorCode.PRIORITY_NOT_SUPPORTED_WITH_XHR */\n  }, {\n    property: 'mode',\n    errorCode: 2816 /* RuntimeErrorCode.MODE_NOT_SUPPORTED_WITH_XHR */\n  }, {\n    property: 'redirect',\n    errorCode: 2817 /* RuntimeErrorCode.REDIRECT_NOT_SUPPORTED_WITH_XHR */\n  }, {\n    property: 'credentials',\n    errorCode: 2818 /* RuntimeErrorCode.CREDENTIALS_NOT_SUPPORTED_WITH_XHR */\n  }];\n  // Check each unsupported option and warn if present\n  for (const {\n    property,\n    errorCode\n  } of unsupportedOptions) {\n    if (req[property]) {\n      console.warn(_formatRuntimeError(errorCode, `Angular detected that a \\`HttpClient\\` request with the \\`${property}\\` option was sent using XHR, which does not support it. To use the \\`${property}\\` option, enable Fetch API support by passing \\`withFetch()\\` as an argument to \\`provideHttpClient()\\`.`));\n    }\n  }\n}\n/**\n * Uses `XMLHttpRequest` to send requests to a backend server.\n * @see {@link HttpHandler}\n * @see {@link JsonpClientBackend}\n *\n * @publicApi\n */\nclass HttpXhrBackend {\n  xhrFactory;\n  constructor(xhrFactory) {\n    this.xhrFactory = xhrFactory;\n  }\n  /**\n   * Processes a request and returns a stream of response events.\n   * @param req The request object.\n   * @returns An observable of the response events.\n   */\n  handle(req) {\n    // Quick check to give a better error message when a user attempts to use\n    // HttpClient.jsonp() without installing the HttpClientJsonpModule\n    if (req.method === 'JSONP') {\n      throw new _RuntimeError(-2800 /* RuntimeErrorCode.MISSING_JSONP_MODULE */, (typeof ngDevMode === 'undefined' || ngDevMode) && `Cannot make a JSONP request without JSONP support. To fix the problem, either add the \\`withJsonpSupport()\\` call (if \\`provideHttpClient()\\` is used) or import the \\`HttpClientJsonpModule\\` in the root NgModule.`);\n    }\n    // Validate that the request is compatible with the XHR backend.\n    ngDevMode && validateXhrCompatibility(req);\n    // Check whether this factory has a special function to load an XHR implementation\n    // for various non-browser environments. We currently limit it to only `ServerXhr`\n    // class, which needs to load an XHR implementation.\n    const xhrFactory = this.xhrFactory;\n    const source =\n    // Note that `ɵloadImpl` is never defined in client bundles and can be\n    // safely dropped whenever we're running in the browser.\n    // This branching is redundant.\n    // The `ngServerMode` guard also enables tree-shaking of the `from()`\n    // function from the common bundle, as it's only used in server code.\n    typeof ngServerMode !== 'undefined' && ngServerMode && xhrFactory.ɵloadImpl ? from(xhrFactory.ɵloadImpl()) : of(null);\n    return source.pipe(switchMap(() => {\n      // Everything happens on Observable subscription.\n      return new Observable(observer => {\n        // Start by setting up the XHR object with request method, URL, and withCredentials\n        // flag.\n        const xhr = xhrFactory.build();\n        xhr.open(req.method, req.urlWithParams);\n        if (req.withCredentials) {\n          xhr.withCredentials = true;\n        }\n        // Add all the requested headers.\n        req.headers.forEach((name, values) => xhr.setRequestHeader(name, values.join(',')));\n        // Add an Accept header if one isn't present already.\n        if (!req.headers.has(ACCEPT_HEADER)) {\n          xhr.setRequestHeader(ACCEPT_HEADER, ACCEPT_HEADER_VALUE);\n        }\n        // Auto-detect the Content-Type header if one isn't present already.\n        if (!req.headers.has(CONTENT_TYPE_HEADER)) {\n          const detectedType = req.detectContentTypeHeader();\n          // Sometimes Content-Type detection fails.\n          if (detectedType !== null) {\n            xhr.setRequestHeader(CONTENT_TYPE_HEADER, detectedType);\n          }\n        }\n        if (req.timeout) {\n          xhr.timeout = req.timeout;\n        }\n        // Set the responseType if one was requested.\n        if (req.responseType) {\n          const responseType = req.responseType.toLowerCase();\n          // JSON responses need to be processed as text. This is because if the server\n          // returns an XSSI-prefixed JSON response, the browser will fail to parse it,\n          // xhr.response will be null, and xhr.responseText cannot be accessed to\n          // retrieve the prefixed JSON data in order to strip the prefix. Thus, all JSON\n          // is parsed by first requesting text and then applying JSON.parse.\n          xhr.responseType = responseType !== 'json' ? responseType : 'text';\n        }\n        // Serialize the request body if one is present. If not, this will be set to null.\n        const reqBody = req.serializeBody();\n        // If progress events are enabled, response headers will be delivered\n        // in two events - the HttpHeaderResponse event and the full HttpResponse\n        // event. However, since response headers don't change in between these\n        // two events, it doesn't make sense to parse them twice. So headerResponse\n        // caches the data extracted from the response whenever it's first parsed,\n        // to ensure parsing isn't duplicated.\n        let headerResponse = null;\n        // partialFromXhr extracts the HttpHeaderResponse from the current XMLHttpRequest\n        // state, and memoizes it into headerResponse.\n        const partialFromXhr = () => {\n          if (headerResponse !== null) {\n            return headerResponse;\n          }\n          const statusText = xhr.statusText || 'OK';\n          // Parse headers from XMLHttpRequest - this step is lazy.\n          const headers = new HttpHeaders(xhr.getAllResponseHeaders());\n          // Read the response URL from the XMLHttpResponse instance and fall back on the\n          // request URL.\n          const url = getResponseUrl(xhr) || req.url;\n          // Construct the HttpHeaderResponse and memoize it.\n          headerResponse = new HttpHeaderResponse({\n            headers,\n            status: xhr.status,\n            statusText,\n            url\n          });\n          return headerResponse;\n        };\n        // Next, a few closures are defined for the various events which XMLHttpRequest can\n        // emit. This allows them to be unregistered as event listeners later.\n        // First up is the load event, which represents a response being fully available.\n        const onLoad = () => {\n          // Read response state from the memoized partial data.\n          let {\n            headers,\n            status,\n            statusText,\n            url\n          } = partialFromXhr();\n          // The body will be read out if present.\n          let body = null;\n          if (status !== HTTP_STATUS_CODE_NO_CONTENT) {\n            // Use XMLHttpRequest.response if set, responseText otherwise.\n            body = typeof xhr.response === 'undefined' ? xhr.responseText : xhr.response;\n          }\n          // Normalize another potential bug (this one comes from CORS).\n          if (status === 0) {\n            status = !!body ? HTTP_STATUS_CODE_OK : 0;\n          }\n          // ok determines whether the response will be transmitted on the event or\n          // error channel. Unsuccessful status codes (not 2xx) will always be errors,\n          // but a successful status code can still result in an error if the user\n          // asked for JSON data and the body cannot be parsed as such.\n          let ok = status >= 200 && status < 300;\n          // Check whether the body needs to be parsed as JSON (in many cases the browser\n          // will have done that already).\n          if (req.responseType === 'json' && typeof body === 'string') {\n            // Save the original body, before attempting XSSI prefix stripping.\n            const originalBody = body;\n            body = body.replace(XSSI_PREFIX, '');\n            try {\n              // Attempt the parse. If it fails, a parse error should be delivered to the\n              // user.\n              body = body !== '' ? JSON.parse(body) : null;\n            } catch (error) {\n              // Since the JSON.parse failed, it's reasonable to assume this might not have\n              // been a JSON response. Restore the original body (including any XSSI prefix)\n              // to deliver a better error response.\n              body = originalBody;\n              // If this was an error request to begin with, leave it as a string, it\n              // probably just isn't JSON. Otherwise, deliver the parsing error to the user.\n              if (ok) {\n                // Even though the response status was 2xx, this is still an error.\n                ok = false;\n                // The parse error contains the text of the body that failed to parse.\n                body = {\n                  error,\n                  text: body\n                };\n              }\n            }\n          }\n          if (ok) {\n            // A successful response is delivered on the event stream.\n            observer.next(new HttpResponse({\n              body,\n              headers,\n              status,\n              statusText,\n              url: url || undefined\n            }));\n            // The full body has been received and delivered, no further events\n            // are possible. This request is complete.\n            observer.complete();\n          } else {\n            // An unsuccessful request is delivered on the error channel.\n            observer.error(new HttpErrorResponse({\n              // The error in this case is the response body (error from the server).\n              error: body,\n              headers,\n              status,\n              statusText,\n              url: url || undefined\n            }));\n          }\n        };\n        // The onError callback is called when something goes wrong at the network level.\n        // Connection timeout, DNS error, offline, etc. These are actual errors, and are\n        // transmitted on the error channel.\n        const onError = error => {\n          const {\n            url\n          } = partialFromXhr();\n          const res = new HttpErrorResponse({\n            error,\n            status: xhr.status || 0,\n            statusText: xhr.statusText || 'Unknown Error',\n            url: url || undefined\n          });\n          observer.error(res);\n        };\n        let onTimeout = onError;\n        if (req.timeout) {\n          onTimeout = _ => {\n            const {\n              url\n            } = partialFromXhr();\n            const res = new HttpErrorResponse({\n              error: new DOMException('Request timed out', 'TimeoutError'),\n              status: xhr.status || 0,\n              statusText: xhr.statusText || 'Request timeout',\n              url: url || undefined\n            });\n            observer.error(res);\n          };\n        }\n        // The sentHeaders flag tracks whether the HttpResponseHeaders event\n        // has been sent on the stream. This is necessary to track if progress\n        // is enabled since the event will be sent on only the first download\n        // progress event.\n        let sentHeaders = false;\n        // The download progress event handler, which is only registered if\n        // progress events are enabled.\n        const onDownProgress = event => {\n          // Send the HttpResponseHeaders event if it hasn't been sent already.\n          if (!sentHeaders) {\n            observer.next(partialFromXhr());\n            sentHeaders = true;\n          }\n          // Start building the download progress event to deliver on the response\n          // event stream.\n          let progressEvent = {\n            type: HttpEventType.DownloadProgress,\n            loaded: event.loaded\n          };\n          // Set the total number of bytes in the event if it's available.\n          if (event.lengthComputable) {\n            progressEvent.total = event.total;\n          }\n          // If the request was for text content and a partial response is\n          // available on XMLHttpRequest, include it in the progress event\n          // to allow for streaming reads.\n          if (req.responseType === 'text' && !!xhr.responseText) {\n            progressEvent.partialText = xhr.responseText;\n          }\n          // Finally, fire the event.\n          observer.next(progressEvent);\n        };\n        // The upload progress event handler, which is only registered if\n        // progress events are enabled.\n        const onUpProgress = event => {\n          // Upload progress events are simpler. Begin building the progress\n          // event.\n          let progress = {\n            type: HttpEventType.UploadProgress,\n            loaded: event.loaded\n          };\n          // If the total number of bytes being uploaded is available, include\n          // it.\n          if (event.lengthComputable) {\n            progress.total = event.total;\n          }\n          // Send the event.\n          observer.next(progress);\n        };\n        // By default, register for load and error events.\n        xhr.addEventListener('load', onLoad);\n        xhr.addEventListener('error', onError);\n        xhr.addEventListener('timeout', onTimeout);\n        xhr.addEventListener('abort', onError);\n        // Progress events are only enabled if requested.\n        if (req.reportProgress) {\n          // Download progress is always enabled if requested.\n          xhr.addEventListener('progress', onDownProgress);\n          // Upload progress depends on whether there is a body to upload.\n          if (reqBody !== null && xhr.upload) {\n            xhr.upload.addEventListener('progress', onUpProgress);\n          }\n        }\n        // Fire the request, and notify the event stream that it was fired.\n        xhr.send(reqBody);\n        observer.next({\n          type: HttpEventType.Sent\n        });\n        // This is the return from the Observable function, which is the\n        // request cancellation handler.\n        return () => {\n          // On a cancellation, remove all registered event listeners.\n          xhr.removeEventListener('error', onError);\n          xhr.removeEventListener('abort', onError);\n          xhr.removeEventListener('load', onLoad);\n          xhr.removeEventListener('timeout', onTimeout);\n          if (req.reportProgress) {\n            xhr.removeEventListener('progress', onDownProgress);\n            if (reqBody !== null && xhr.upload) {\n              xhr.upload.removeEventListener('progress', onUpProgress);\n            }\n          }\n          // Finally, abort the in-flight request.\n          if (xhr.readyState !== xhr.DONE) {\n            xhr.abort();\n          }\n        };\n      });\n    }));\n  }\n  static ɵfac = function HttpXhrBackend_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || HttpXhrBackend)(i0.ɵɵinject(XhrFactory));\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: HttpXhrBackend,\n    factory: HttpXhrBackend.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HttpXhrBackend, [{\n    type: Injectable\n  }], () => [{\n    type: XhrFactory\n  }], null);\n})();\nconst XSRF_ENABLED = new InjectionToken(ngDevMode ? 'XSRF_ENABLED' : '');\nconst XSRF_DEFAULT_COOKIE_NAME = 'XSRF-TOKEN';\nconst XSRF_COOKIE_NAME = new InjectionToken(ngDevMode ? 'XSRF_COOKIE_NAME' : '', {\n  providedIn: 'root',\n  factory: () => XSRF_DEFAULT_COOKIE_NAME\n});\nconst XSRF_DEFAULT_HEADER_NAME = 'X-XSRF-TOKEN';\nconst XSRF_HEADER_NAME = new InjectionToken(ngDevMode ? 'XSRF_HEADER_NAME' : '', {\n  providedIn: 'root',\n  factory: () => XSRF_DEFAULT_HEADER_NAME\n});\n/**\n * Retrieves the current XSRF token to use with the next outgoing request.\n *\n * @publicApi\n */\nclass HttpXsrfTokenExtractor {}\n/**\n * `HttpXsrfTokenExtractor` which retrieves the token from a cookie.\n */\nclass HttpXsrfCookieExtractor {\n  doc;\n  cookieName;\n  lastCookieString = '';\n  lastToken = null;\n  /**\n   * @internal for testing\n   */\n  parseCount = 0;\n  constructor(doc, cookieName) {\n    this.doc = doc;\n    this.cookieName = cookieName;\n  }\n  getToken() {\n    if (typeof ngServerMode !== 'undefined' && ngServerMode) {\n      return null;\n    }\n    const cookieString = this.doc.cookie || '';\n    if (cookieString !== this.lastCookieString) {\n      this.parseCount++;\n      this.lastToken = parseCookieValue(cookieString, this.cookieName);\n      this.lastCookieString = cookieString;\n    }\n    return this.lastToken;\n  }\n  static ɵfac = function HttpXsrfCookieExtractor_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || HttpXsrfCookieExtractor)(i0.ɵɵinject(DOCUMENT), i0.ɵɵinject(XSRF_COOKIE_NAME));\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: HttpXsrfCookieExtractor,\n    factory: HttpXsrfCookieExtractor.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HttpXsrfCookieExtractor, [{\n    type: Injectable\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [XSRF_COOKIE_NAME]\n    }]\n  }], null);\n})();\nfunction xsrfInterceptorFn(req, next) {\n  const lcUrl = req.url.toLowerCase();\n  // Skip both non-mutating requests and absolute URLs.\n  // Non-mutating requests don't require a token, and absolute URLs require special handling\n  // anyway as the cookie set\n  // on our origin is not the same as the token expected by another origin.\n  if (!inject(XSRF_ENABLED) || req.method === 'GET' || req.method === 'HEAD' || lcUrl.startsWith('http://') || lcUrl.startsWith('https://')) {\n    return next(req);\n  }\n  const token = inject(HttpXsrfTokenExtractor).getToken();\n  const headerName = inject(XSRF_HEADER_NAME);\n  // Be careful not to overwrite an existing header of the same name.\n  if (token != null && !req.headers.has(headerName)) {\n    req = req.clone({\n      headers: req.headers.set(headerName, token)\n    });\n  }\n  return next(req);\n}\n/**\n * `HttpInterceptor` which adds an XSRF token to eligible outgoing requests.\n */\nclass HttpXsrfInterceptor {\n  injector;\n  constructor(injector) {\n    this.injector = injector;\n  }\n  intercept(initialRequest, next) {\n    return runInInjectionContext(this.injector, () => xsrfInterceptorFn(initialRequest, downstreamRequest => next.handle(downstreamRequest)));\n  }\n  static ɵfac = function HttpXsrfInterceptor_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || HttpXsrfInterceptor)(i0.ɵɵinject(i0.EnvironmentInjector));\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: HttpXsrfInterceptor,\n    factory: HttpXsrfInterceptor.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HttpXsrfInterceptor, [{\n    type: Injectable\n  }], () => [{\n    type: i0.EnvironmentInjector\n  }], null);\n})();\n\n/**\n * Identifies a particular kind of `HttpFeature`.\n *\n * @publicApi\n */\nvar HttpFeatureKind;\n(function (HttpFeatureKind) {\n  HttpFeatureKind[HttpFeatureKind[\"Interceptors\"] = 0] = \"Interceptors\";\n  HttpFeatureKind[HttpFeatureKind[\"LegacyInterceptors\"] = 1] = \"LegacyInterceptors\";\n  HttpFeatureKind[HttpFeatureKind[\"CustomXsrfConfiguration\"] = 2] = \"CustomXsrfConfiguration\";\n  HttpFeatureKind[HttpFeatureKind[\"NoXsrfProtection\"] = 3] = \"NoXsrfProtection\";\n  HttpFeatureKind[HttpFeatureKind[\"JsonpSupport\"] = 4] = \"JsonpSupport\";\n  HttpFeatureKind[HttpFeatureKind[\"RequestsMadeViaParent\"] = 5] = \"RequestsMadeViaParent\";\n  HttpFeatureKind[HttpFeatureKind[\"Fetch\"] = 6] = \"Fetch\";\n})(HttpFeatureKind || (HttpFeatureKind = {}));\nfunction makeHttpFeature(kind, providers) {\n  return {\n    ɵkind: kind,\n    ɵproviders: providers\n  };\n}\n/**\n * Configures Angular's `HttpClient` service to be available for injection.\n *\n * By default, `HttpClient` will be configured for injection with its default options for XSRF\n * protection of outgoing requests. Additional configuration options can be provided by passing\n * feature functions to `provideHttpClient`. For example, HTTP interceptors can be added using the\n * `withInterceptors(...)` feature.\n *\n * <div class=\"docs-alert docs-alert-helpful\">\n *\n * It's strongly recommended to enable\n * [`fetch`](https://developer.mozilla.org/en-US/docs/Web/API/Fetch_API) for applications that use\n * Server-Side Rendering for better performance and compatibility. To enable `fetch`, add\n * `withFetch()` feature to the `provideHttpClient()` call at the root of the application:\n *\n * ```ts\n * provideHttpClient(withFetch());\n * ```\n *\n * </div>\n *\n * @see {@link withInterceptors}\n * @see {@link withInterceptorsFromDi}\n * @see {@link withXsrfConfiguration}\n * @see {@link withNoXsrfProtection}\n * @see {@link withJsonpSupport}\n * @see {@link withRequestsMadeViaParent}\n * @see {@link withFetch}\n */\nfunction provideHttpClient(...features) {\n  if (ngDevMode) {\n    const featureKinds = new Set(features.map(f => f.ɵkind));\n    if (featureKinds.has(HttpFeatureKind.NoXsrfProtection) && featureKinds.has(HttpFeatureKind.CustomXsrfConfiguration)) {\n      throw new Error(ngDevMode ? `Configuration error: found both withXsrfConfiguration() and withNoXsrfProtection() in the same call to provideHttpClient(), which is a contradiction.` : '');\n    }\n  }\n  const providers = [HttpClient, HttpXhrBackend, HttpInterceptorHandler, {\n    provide: HttpHandler,\n    useExisting: HttpInterceptorHandler\n  }, {\n    provide: HttpBackend,\n    useFactory: () => {\n      return inject(FETCH_BACKEND, {\n        optional: true\n      }) ?? inject(HttpXhrBackend);\n    }\n  }, {\n    provide: HTTP_INTERCEPTOR_FNS,\n    useValue: xsrfInterceptorFn,\n    multi: true\n  }, {\n    provide: XSRF_ENABLED,\n    useValue: true\n  }, {\n    provide: HttpXsrfTokenExtractor,\n    useClass: HttpXsrfCookieExtractor\n  }];\n  for (const feature of features) {\n    providers.push(...feature.ɵproviders);\n  }\n  return makeEnvironmentProviders(providers);\n}\n/**\n * Adds one or more functional-style HTTP interceptors to the configuration of the `HttpClient`\n * instance.\n *\n * @see {@link HttpInterceptorFn}\n * @see {@link provideHttpClient}\n * @publicApi\n */\nfunction withInterceptors(interceptorFns) {\n  return makeHttpFeature(HttpFeatureKind.Interceptors, interceptorFns.map(interceptorFn => {\n    return {\n      provide: HTTP_INTERCEPTOR_FNS,\n      useValue: interceptorFn,\n      multi: true\n    };\n  }));\n}\nconst LEGACY_INTERCEPTOR_FN = new InjectionToken(ngDevMode ? 'LEGACY_INTERCEPTOR_FN' : '');\n/**\n * Includes class-based interceptors configured using a multi-provider in the current injector into\n * the configured `HttpClient` instance.\n *\n * Prefer `withInterceptors` and functional interceptors instead, as support for DI-provided\n * interceptors may be phased out in a later release.\n *\n * @see {@link HttpInterceptor}\n * @see {@link HTTP_INTERCEPTORS}\n * @see {@link provideHttpClient}\n */\nfunction withInterceptorsFromDi() {\n  // Note: the legacy interceptor function is provided here via an intermediate token\n  // (`LEGACY_INTERCEPTOR_FN`), using a pattern which guarantees that if these providers are\n  // included multiple times, all of the multi-provider entries will have the same instance of the\n  // interceptor function. That way, the `HttpINterceptorHandler` will dedup them and legacy\n  // interceptors will not run multiple times.\n  return makeHttpFeature(HttpFeatureKind.LegacyInterceptors, [{\n    provide: LEGACY_INTERCEPTOR_FN,\n    useFactory: legacyInterceptorFnFactory\n  }, {\n    provide: HTTP_INTERCEPTOR_FNS,\n    useExisting: LEGACY_INTERCEPTOR_FN,\n    multi: true\n  }]);\n}\n/**\n * Customizes the XSRF protection for the configuration of the current `HttpClient` instance.\n *\n * This feature is incompatible with the `withNoXsrfProtection` feature.\n *\n * @see {@link provideHttpClient}\n */\nfunction withXsrfConfiguration({\n  cookieName,\n  headerName\n}) {\n  const providers = [];\n  if (cookieName !== undefined) {\n    providers.push({\n      provide: XSRF_COOKIE_NAME,\n      useValue: cookieName\n    });\n  }\n  if (headerName !== undefined) {\n    providers.push({\n      provide: XSRF_HEADER_NAME,\n      useValue: headerName\n    });\n  }\n  return makeHttpFeature(HttpFeatureKind.CustomXsrfConfiguration, providers);\n}\n/**\n * Disables XSRF protection in the configuration of the current `HttpClient` instance.\n *\n * This feature is incompatible with the `withXsrfConfiguration` feature.\n *\n * @see {@link provideHttpClient}\n */\nfunction withNoXsrfProtection() {\n  return makeHttpFeature(HttpFeatureKind.NoXsrfProtection, [{\n    provide: XSRF_ENABLED,\n    useValue: false\n  }]);\n}\n/**\n * Add JSONP support to the configuration of the current `HttpClient` instance.\n *\n * @see {@link provideHttpClient}\n */\nfunction withJsonpSupport() {\n  return makeHttpFeature(HttpFeatureKind.JsonpSupport, [JsonpClientBackend, {\n    provide: JsonpCallbackContext,\n    useFactory: jsonpCallbackContext\n  }, {\n    provide: HTTP_INTERCEPTOR_FNS,\n    useValue: jsonpInterceptorFn,\n    multi: true\n  }]);\n}\n/**\n * Configures the current `HttpClient` instance to make requests via the parent injector's\n * `HttpClient` instead of directly.\n *\n * By default, `provideHttpClient` configures `HttpClient` in its injector to be an independent\n * instance. For example, even if `HttpClient` is configured in the parent injector with\n * one or more interceptors, they will not intercept requests made via this instance.\n *\n * With this option enabled, once the request has passed through the current injector's\n * interceptors, it will be delegated to the parent injector's `HttpClient` chain instead of\n * dispatched directly, and interceptors in the parent configuration will be applied to the request.\n *\n * If there are several `HttpClient` instances in the injector hierarchy, it's possible for\n * `withRequestsMadeViaParent` to be used at multiple levels, which will cause the request to\n * \"bubble up\" until either reaching the root level or an `HttpClient` which was not configured with\n * this option.\n *\n * @see {@link provideHttpClient}\n * @publicApi 19.0\n */\nfunction withRequestsMadeViaParent() {\n  return makeHttpFeature(HttpFeatureKind.RequestsMadeViaParent, [{\n    provide: HttpBackend,\n    useFactory: () => {\n      const handlerFromParent = inject(HttpHandler, {\n        skipSelf: true,\n        optional: true\n      });\n      if (ngDevMode && handlerFromParent === null) {\n        throw new Error('withRequestsMadeViaParent() can only be used when the parent injector also configures HttpClient');\n      }\n      return handlerFromParent;\n    }\n  }]);\n}\n/**\n * Configures the current `HttpClient` instance to make requests using the fetch API.\n *\n * Note: The Fetch API doesn't support progress report on uploads.\n *\n * @publicApi\n */\nfunction withFetch() {\n  return makeHttpFeature(HttpFeatureKind.Fetch, [FetchBackend, {\n    provide: FETCH_BACKEND,\n    useExisting: FetchBackend\n  }, {\n    provide: HttpBackend,\n    useExisting: FetchBackend\n  }]);\n}\n\n/**\n * Configures XSRF protection support for outgoing requests.\n *\n * For a server that supports a cookie-based XSRF protection system,\n * use directly to configure XSRF protection with the correct\n * cookie and header names.\n *\n * If no names are supplied, the default cookie name is `XSRF-TOKEN`\n * and the default header name is `X-XSRF-TOKEN`.\n *\n * @publicApi\n * @deprecated Use withXsrfConfiguration({cookieName: 'XSRF-TOKEN', headerName: 'X-XSRF-TOKEN'}) as\n *     providers instead or `withNoXsrfProtection` if you want to disabled XSRF protection.\n */\nclass HttpClientXsrfModule {\n  /**\n   * Disable the default XSRF protection.\n   */\n  static disable() {\n    return {\n      ngModule: HttpClientXsrfModule,\n      providers: [withNoXsrfProtection().ɵproviders]\n    };\n  }\n  /**\n   * Configure XSRF protection.\n   * @param options An object that can specify either or both\n   * cookie name or header name.\n   * - Cookie name default is `XSRF-TOKEN`.\n   * - Header name default is `X-XSRF-TOKEN`.\n   *\n   */\n  static withOptions(options = {}) {\n    return {\n      ngModule: HttpClientXsrfModule,\n      providers: withXsrfConfiguration(options).ɵproviders\n    };\n  }\n  static ɵfac = function HttpClientXsrfModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || HttpClientXsrfModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: HttpClientXsrfModule\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    providers: [HttpXsrfInterceptor, {\n      provide: HTTP_INTERCEPTORS,\n      useExisting: HttpXsrfInterceptor,\n      multi: true\n    }, {\n      provide: HttpXsrfTokenExtractor,\n      useClass: HttpXsrfCookieExtractor\n    }, withXsrfConfiguration({\n      cookieName: XSRF_DEFAULT_COOKIE_NAME,\n      headerName: XSRF_DEFAULT_HEADER_NAME\n    }).ɵproviders, {\n      provide: XSRF_ENABLED,\n      useValue: true\n    }]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HttpClientXsrfModule, [{\n    type: NgModule,\n    args: [{\n      providers: [HttpXsrfInterceptor, {\n        provide: HTTP_INTERCEPTORS,\n        useExisting: HttpXsrfInterceptor,\n        multi: true\n      }, {\n        provide: HttpXsrfTokenExtractor,\n        useClass: HttpXsrfCookieExtractor\n      }, withXsrfConfiguration({\n        cookieName: XSRF_DEFAULT_COOKIE_NAME,\n        headerName: XSRF_DEFAULT_HEADER_NAME\n      }).ɵproviders, {\n        provide: XSRF_ENABLED,\n        useValue: true\n      }]\n    }]\n  }], null, null);\n})();\n/**\n * Configures the dependency injector for `HttpClient`\n * with supporting services for XSRF. Automatically imported by `HttpClientModule`.\n *\n * You can add interceptors to the chain behind `HttpClient` by binding them to the\n * multiprovider for built-in DI token `HTTP_INTERCEPTORS`.\n *\n * @publicApi\n * @deprecated use `provideHttpClient(withInterceptorsFromDi())` as providers instead\n */\nclass HttpClientModule {\n  static ɵfac = function HttpClientModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || HttpClientModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: HttpClientModule\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    providers: [provideHttpClient(withInterceptorsFromDi())]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HttpClientModule, [{\n    type: NgModule,\n    args: [{\n      /**\n       * Configures the dependency injector where it is imported\n       * with supporting services for HTTP communications.\n       */\n      providers: [provideHttpClient(withInterceptorsFromDi())]\n    }]\n  }], null, null);\n})();\n/**\n * Configures the dependency injector for `HttpClient`\n * with supporting services for JSONP.\n * Without this module, Jsonp requests reach the backend\n * with method JSONP, where they are rejected.\n *\n * @publicApi\n * @deprecated `withJsonpSupport()` as providers instead\n */\nclass HttpClientJsonpModule {\n  static ɵfac = function HttpClientJsonpModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || HttpClientJsonpModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: HttpClientJsonpModule\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    providers: [withJsonpSupport().ɵproviders]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HttpClientJsonpModule, [{\n    type: NgModule,\n    args: [{\n      providers: [withJsonpSupport().ɵproviders]\n    }]\n  }], null, null);\n})();\nexport { FetchBackend, HTTP_INTERCEPTORS, HTTP_ROOT_INTERCEPTOR_FNS, HttpBackend, HttpClient, HttpClientJsonpModule, HttpClientModule, HttpClientXsrfModule, HttpContext, HttpContextToken, HttpErrorResponse, HttpEventType, HttpFeatureKind, HttpHandler, HttpHeaderResponse, HttpHeaders, HttpInterceptorHandler, HttpParams, HttpRequest, HttpResponse, HttpResponseBase, HttpStatusCode, HttpUrlEncodingCodec, HttpXhrBackend, HttpXsrfTokenExtractor, JsonpClientBackend, JsonpInterceptor, REQUESTS_CONTRIBUTE_TO_STABILITY, provideHttpClient, withFetch, withInterceptors, withInterceptorsFromDi, withJsonpSupport, withNoXsrfProtection, withRequestsMadeViaParent, withXsrfConfiguration };\n", "/**\n * @license Angular v20.1.1\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport { HttpHeaders, HttpParams, HttpRequest, HttpEventType, HttpErrorResponse, HttpClient, HTTP_ROOT_INTERCEPTOR_FNS, HttpResponse } from './module.mjs';\nexport { FetchBackend, HTTP_INTERCEPTORS, HttpBackend, HttpClientJsonpModule, HttpClientModule, HttpClientXsrfModule, HttpContext, HttpContextToken, HttpFeatureKind, HttpHandler, HttpHeaderResponse, HttpResponseBase, HttpStatusCode, HttpUrlEncodingCodec, HttpXhrBackend, HttpXsrfTokenExtractor, JsonpClientBackend, JsonpInterceptor, provideHttpClient, withFetch, withInterceptors, withInterceptorsFromDi, withJsonpSupport, withNoXsrfProtection, withRequestsMadeViaParent, withXsrfConfiguration, HttpInterceptorHandler as ɵHttpInterceptingHandler, HttpInterceptorHandler as ɵHttpInterceptorHandler, REQUESTS_CONTRIBUTE_TO_STABILITY as ɵREQUESTS_CONTRIBUTE_TO_STABILITY } from './module.mjs';\nimport { assertInInjectionContext, inject, Injector, ɵResourceImpl as _ResourceImpl, linkedSignal, computed, signal, ɵencapsulateResourceError as _encapsulateResourceError, ɵRuntimeError as _RuntimeError, InjectionToken, ɵperformanceMarkFeature as _performanceMarkFeature, APP_BOOTSTRAP_LISTENER, ApplicationRef, TransferState, makeStateKey, ɵtruncateMiddle as _truncateMiddle, ɵformatRuntimeError as _formatRuntimeError } from '@angular/core';\nimport { of } from 'rxjs';\nimport { tap } from 'rxjs/operators';\nimport './xhr.mjs';\n\n/**\n * `httpResource` makes a reactive HTTP request and exposes the request status and response value as\n * a `WritableResource`. By default, it assumes that the backend will return JSON data. To make a\n * request that expects a different kind of data, you can use a sub-constructor of `httpResource`,\n * such as `httpResource.text`.\n *\n * @experimental 19.2\n * @initializerApiFunction\n */\nconst httpResource = (() => {\n    const jsonFn = makeHttpResourceFn('json');\n    jsonFn.arrayBuffer = makeHttpResourceFn('arraybuffer');\n    jsonFn.blob = makeHttpResourceFn('blob');\n    jsonFn.text = makeHttpResourceFn('text');\n    return jsonFn;\n})();\nfunction makeHttpResourceFn(responseType) {\n    return function httpResource(request, options) {\n        if (ngDevMode && !options?.injector) {\n            assertInInjectionContext(httpResource);\n        }\n        const injector = options?.injector ?? inject(Injector);\n        return new HttpResourceImpl(injector, () => normalizeRequest(request, responseType), options?.defaultValue, options?.parse, options?.equal);\n    };\n}\nfunction normalizeRequest(request, responseType) {\n    let unwrappedRequest = typeof request === 'function' ? request() : request;\n    if (unwrappedRequest === undefined) {\n        return undefined;\n    }\n    else if (typeof unwrappedRequest === 'string') {\n        unwrappedRequest = { url: unwrappedRequest };\n    }\n    const headers = unwrappedRequest.headers instanceof HttpHeaders\n        ? unwrappedRequest.headers\n        : new HttpHeaders(unwrappedRequest.headers);\n    const params = unwrappedRequest.params instanceof HttpParams\n        ? unwrappedRequest.params\n        : new HttpParams({ fromObject: unwrappedRequest.params });\n    return new HttpRequest(unwrappedRequest.method ?? 'GET', unwrappedRequest.url, unwrappedRequest.body ?? null, {\n        headers,\n        params,\n        reportProgress: unwrappedRequest.reportProgress,\n        withCredentials: unwrappedRequest.withCredentials,\n        keepalive: unwrappedRequest.keepalive,\n        cache: unwrappedRequest.cache,\n        priority: unwrappedRequest.priority,\n        mode: unwrappedRequest.mode,\n        redirect: unwrappedRequest.redirect,\n        responseType,\n        context: unwrappedRequest.context,\n        transferCache: unwrappedRequest.transferCache,\n        credentials: unwrappedRequest.credentials,\n        timeout: unwrappedRequest.timeout,\n    });\n}\nclass HttpResourceImpl extends _ResourceImpl {\n    client;\n    _headers = linkedSignal({\n        source: this.extRequest,\n        computation: () => undefined,\n    });\n    _progress = linkedSignal({\n        source: this.extRequest,\n        computation: () => undefined,\n    });\n    _statusCode = linkedSignal({\n        source: this.extRequest,\n        computation: () => undefined,\n    });\n    headers = computed(() => this.status() === 'resolved' || this.status() === 'error' ? this._headers() : undefined, ...(ngDevMode ? [{ debugName: \"headers\" }] : []));\n    progress = this._progress.asReadonly();\n    statusCode = this._statusCode.asReadonly();\n    constructor(injector, request, defaultValue, parse, equal) {\n        super(request, ({ params: request, abortSignal }) => {\n            let sub;\n            // Track the abort listener so it can be removed if the Observable completes (as a memory\n            // optimization).\n            const onAbort = () => sub.unsubscribe();\n            abortSignal.addEventListener('abort', onAbort);\n            // Start off stream as undefined.\n            const stream = signal({ value: undefined }, ...(ngDevMode ? [{ debugName: \"stream\" }] : []));\n            let resolve;\n            const promise = new Promise((r) => (resolve = r));\n            const send = (value) => {\n                stream.set(value);\n                resolve?.(stream);\n                resolve = undefined;\n            };\n            sub = this.client.request(request).subscribe({\n                next: (event) => {\n                    switch (event.type) {\n                        case HttpEventType.Response:\n                            this._headers.set(event.headers);\n                            this._statusCode.set(event.status);\n                            try {\n                                send({ value: parse ? parse(event.body) : event.body });\n                            }\n                            catch (error) {\n                                send({ error: _encapsulateResourceError(error) });\n                            }\n                            break;\n                        case HttpEventType.DownloadProgress:\n                            this._progress.set(event);\n                            break;\n                    }\n                },\n                error: (error) => {\n                    if (error instanceof HttpErrorResponse) {\n                        this._headers.set(error.headers);\n                        this._statusCode.set(error.status);\n                    }\n                    send({ error });\n                    abortSignal.removeEventListener('abort', onAbort);\n                },\n                complete: () => {\n                    if (resolve) {\n                        send({\n                            error: new _RuntimeError(991 /* ɵRuntimeErrorCode.RESOURCE_COMPLETED_BEFORE_PRODUCING_VALUE */, ngDevMode && 'Resource completed before producing a value'),\n                        });\n                    }\n                    abortSignal.removeEventListener('abort', onAbort);\n                },\n            });\n            return promise;\n        }, defaultValue, equal, injector);\n        this.client = injector.get(HttpClient);\n    }\n}\n\n/**\n * If your application uses different HTTP origins to make API calls (via `HttpClient`) on the server and\n * on the client, the `HTTP_TRANSFER_CACHE_ORIGIN_MAP` token allows you to establish a mapping\n * between those origins, so that `HttpTransferCache` feature can recognize those requests as the same\n * ones and reuse the data cached on the server during hydration on the client.\n *\n * **Important note**: the `HTTP_TRANSFER_CACHE_ORIGIN_MAP` token should *only* be provided in\n * the *server* code of your application (typically in the `app.server.config.ts` script). Angular throws an\n * error if it detects that the token is defined while running on the client.\n *\n * @usageNotes\n *\n * When the same API endpoint is accessed via `http://internal-domain.com:8080` on the server and\n * via `https://external-domain.com` on the client, you can use the following configuration:\n * ```ts\n * // in app.server.config.ts\n * {\n *     provide: HTTP_TRANSFER_CACHE_ORIGIN_MAP,\n *     useValue: {\n *         'http://internal-domain.com:8080': 'https://external-domain.com'\n *     }\n * }\n * ```\n *\n * @publicApi\n */\nconst HTTP_TRANSFER_CACHE_ORIGIN_MAP = new InjectionToken(ngDevMode ? 'HTTP_TRANSFER_CACHE_ORIGIN_MAP' : '');\n/**\n * Keys within cached response data structure.\n */\nconst BODY = 'b';\nconst HEADERS = 'h';\nconst STATUS = 's';\nconst STATUS_TEXT = 'st';\nconst REQ_URL = 'u';\nconst RESPONSE_TYPE = 'rt';\nconst CACHE_OPTIONS = new InjectionToken(ngDevMode ? 'HTTP_TRANSFER_STATE_CACHE_OPTIONS' : '');\n/**\n * A list of allowed HTTP methods to cache.\n */\nconst ALLOWED_METHODS = ['GET', 'HEAD'];\nfunction transferCacheInterceptorFn(req, next) {\n    const { isCacheActive, ...globalOptions } = inject(CACHE_OPTIONS);\n    const { transferCache: requestOptions, method: requestMethod } = req;\n    // In the following situations we do not want to cache the request\n    if (!isCacheActive ||\n        requestOptions === false ||\n        // POST requests are allowed either globally or at request level\n        (requestMethod === 'POST' && !globalOptions.includePostRequests && !requestOptions) ||\n        (requestMethod !== 'POST' && !ALLOWED_METHODS.includes(requestMethod)) ||\n        // Do not cache request that require authorization when includeRequestsWithAuthHeaders is falsey\n        (!globalOptions.includeRequestsWithAuthHeaders && hasAuthHeaders(req)) ||\n        globalOptions.filter?.(req) === false) {\n        return next(req);\n    }\n    const transferState = inject(TransferState);\n    const originMap = inject(HTTP_TRANSFER_CACHE_ORIGIN_MAP, {\n        optional: true,\n    });\n    if (typeof ngServerMode !== 'undefined' && !ngServerMode && originMap) {\n        throw new _RuntimeError(2803 /* RuntimeErrorCode.HTTP_ORIGIN_MAP_USED_IN_CLIENT */, ngDevMode &&\n            'Angular detected that the `HTTP_TRANSFER_CACHE_ORIGIN_MAP` token is configured and ' +\n                'present in the client side code. Please ensure that this token is only provided in the ' +\n                'server code of the application.');\n    }\n    const requestUrl = typeof ngServerMode !== 'undefined' && ngServerMode && originMap\n        ? mapRequestOriginUrl(req.url, originMap)\n        : req.url;\n    const storeKey = makeCacheKey(req, requestUrl);\n    const response = transferState.get(storeKey, null);\n    let headersToInclude = globalOptions.includeHeaders;\n    if (typeof requestOptions === 'object' && requestOptions.includeHeaders) {\n        // Request-specific config takes precedence over the global config.\n        headersToInclude = requestOptions.includeHeaders;\n    }\n    if (response) {\n        const { [BODY]: undecodedBody, [RESPONSE_TYPE]: responseType, [HEADERS]: httpHeaders, [STATUS]: status, [STATUS_TEXT]: statusText, [REQ_URL]: url, } = response;\n        // Request found in cache. Respond using it.\n        let body = undecodedBody;\n        switch (responseType) {\n            case 'arraybuffer':\n                body = new TextEncoder().encode(undecodedBody).buffer;\n                break;\n            case 'blob':\n                body = new Blob([undecodedBody]);\n                break;\n        }\n        // We want to warn users accessing a header provided from the cache\n        // That HttpTransferCache alters the headers\n        // The warning will be logged a single time by HttpHeaders instance\n        let headers = new HttpHeaders(httpHeaders);\n        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n            // Append extra logic in dev mode to produce a warning when a header\n            // that was not transferred to the client is accessed in the code via `get`\n            // and `has` calls.\n            headers = appendMissingHeadersDetection(req.url, headers, headersToInclude ?? []);\n        }\n        return of(new HttpResponse({\n            body,\n            headers,\n            status,\n            statusText,\n            url,\n        }));\n    }\n    const event$ = next(req);\n    if (typeof ngServerMode !== 'undefined' && ngServerMode) {\n        // Request not found in cache. Make the request and cache it if on the server.\n        return event$.pipe(tap((event) => {\n            // Only cache successful HTTP responses.\n            if (event instanceof HttpResponse) {\n                transferState.set(storeKey, {\n                    [BODY]: event.body,\n                    [HEADERS]: getFilteredHeaders(event.headers, headersToInclude),\n                    [STATUS]: event.status,\n                    [STATUS_TEXT]: event.statusText,\n                    [REQ_URL]: requestUrl,\n                    [RESPONSE_TYPE]: req.responseType,\n                });\n            }\n        }));\n    }\n    return event$;\n}\n/** @returns true when the requests contains autorization related headers. */\nfunction hasAuthHeaders(req) {\n    return req.headers.has('authorization') || req.headers.has('proxy-authorization');\n}\nfunction getFilteredHeaders(headers, includeHeaders) {\n    if (!includeHeaders) {\n        return {};\n    }\n    const headersMap = {};\n    for (const key of includeHeaders) {\n        const values = headers.getAll(key);\n        if (values !== null) {\n            headersMap[key] = values;\n        }\n    }\n    return headersMap;\n}\nfunction sortAndConcatParams(params) {\n    return [...params.keys()]\n        .sort()\n        .map((k) => `${k}=${params.getAll(k)}`)\n        .join('&');\n}\nfunction makeCacheKey(request, mappedRequestUrl) {\n    // make the params encoded same as a url so it's easy to identify\n    const { params, method, responseType } = request;\n    const encodedParams = sortAndConcatParams(params);\n    let serializedBody = request.serializeBody();\n    if (serializedBody instanceof URLSearchParams) {\n        serializedBody = sortAndConcatParams(serializedBody);\n    }\n    else if (typeof serializedBody !== 'string') {\n        serializedBody = '';\n    }\n    const key = [method, responseType, mappedRequestUrl, serializedBody, encodedParams].join('|');\n    const hash = generateHash(key);\n    return makeStateKey(hash);\n}\n/**\n * A method that returns a hash representation of a string using a variant of DJB2 hash\n * algorithm.\n *\n * This is the same hashing logic that is used to generate component ids.\n */\nfunction generateHash(value) {\n    let hash = 0;\n    for (const char of value) {\n        hash = (Math.imul(31, hash) + char.charCodeAt(0)) << 0;\n    }\n    // Force positive number hash.\n    // ********** = equivalent of Integer.MAX_VALUE.\n    hash += ********** + 1;\n    return hash.toString();\n}\n/**\n * Returns the DI providers needed to enable HTTP transfer cache.\n *\n * By default, when using server rendering, requests are performed twice: once on the server and\n * other one on the browser.\n *\n * When these providers are added, requests performed on the server are cached and reused during the\n * bootstrapping of the application in the browser thus avoiding duplicate requests and reducing\n * load time.\n *\n */\nfunction withHttpTransferCache(cacheOptions) {\n    return [\n        {\n            provide: CACHE_OPTIONS,\n            useFactory: () => {\n                _performanceMarkFeature('NgHttpTransferCache');\n                return { isCacheActive: true, ...cacheOptions };\n            },\n        },\n        {\n            provide: HTTP_ROOT_INTERCEPTOR_FNS,\n            useValue: transferCacheInterceptorFn,\n            multi: true,\n        },\n        {\n            provide: APP_BOOTSTRAP_LISTENER,\n            multi: true,\n            useFactory: () => {\n                const appRef = inject(ApplicationRef);\n                const cacheState = inject(CACHE_OPTIONS);\n                return () => {\n                    appRef.whenStable().then(() => {\n                        cacheState.isCacheActive = false;\n                    });\n                };\n            },\n        },\n    ];\n}\n/**\n * This function will add a proxy to an HttpHeader to intercept calls to get/has\n * and log a warning if the header entry requested has been removed\n */\nfunction appendMissingHeadersDetection(url, headers, headersToInclude) {\n    const warningProduced = new Set();\n    return new Proxy(headers, {\n        get(target, prop) {\n            const value = Reflect.get(target, prop);\n            const methods = new Set(['get', 'has', 'getAll']);\n            if (typeof value !== 'function' || !methods.has(prop)) {\n                return value;\n            }\n            return (headerName) => {\n                // We log when the key has been removed and a warning hasn't been produced for the header\n                const key = (prop + ':' + headerName).toLowerCase(); // e.g. `get:cache-control`\n                if (!headersToInclude.includes(headerName) && !warningProduced.has(key)) {\n                    warningProduced.add(key);\n                    const truncatedUrl = _truncateMiddle(url);\n                    // TODO: create Error guide for this warning\n                    console.warn(_formatRuntimeError(2802 /* RuntimeErrorCode.HEADERS_ALTERED_BY_TRANSFER_CACHE */, `Angular detected that the \\`${headerName}\\` header is accessed, but the value of the header ` +\n                        `was not transferred from the server to the client by the HttpTransferCache. ` +\n                        `To include the value of the \\`${headerName}\\` header for the \\`${truncatedUrl}\\` request, ` +\n                        `use the \\`includeHeaders\\` list. The \\`includeHeaders\\` can be defined either ` +\n                        `on a request level by adding the \\`transferCache\\` parameter, or on an application ` +\n                        `level by adding the \\`httpCacheTransfer.includeHeaders\\` argument to the ` +\n                        `\\`provideClientHydration()\\` call. `));\n                }\n                // invoking the original method\n                return value.apply(target, [headerName]);\n            };\n        },\n    });\n}\nfunction mapRequestOriginUrl(url, originMap) {\n    const origin = new URL(url, 'resolve://').origin;\n    const mappedOrigin = originMap[origin];\n    if (!mappedOrigin) {\n        return url;\n    }\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n        verifyMappedOrigin(mappedOrigin);\n    }\n    return url.replace(origin, mappedOrigin);\n}\nfunction verifyMappedOrigin(url) {\n    if (new URL(url, 'resolve://').pathname !== '/') {\n        throw new _RuntimeError(2804 /* RuntimeErrorCode.HTTP_ORIGIN_MAP_CONTAINS_PATH */, 'Angular detected a URL with a path segment in the value provided for the ' +\n            `\\`HTTP_TRANSFER_CACHE_ORIGIN_MAP\\` token: ${url}. The map should only contain origins ` +\n            'without any other segments.');\n    }\n}\n\nexport { HTTP_TRANSFER_CACHE_ORIGIN_MAP, HttpClient, HttpErrorResponse, HttpEventType, HttpHeaders, HttpParams, HttpRequest, HttpResponse, httpResource, HTTP_ROOT_INTERCEPTOR_FNS as ɵHTTP_ROOT_INTERCEPTOR_FNS, withHttpTransferCache as ɵwithHttpTransferCache };\n\n", "/**\n * @license Angular v20.1.1\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\nexport { BrowserModule, bootstrapApplication, createApplication, platformBrowser, provideProtractorTestingSupport, BrowserDomAdapter as ɵBrowserDomAdapter, BrowserGetTestability as ɵBrowserGetTestability, DomEventsPlugin as ɵDomEventsPlugin, KeyEventsPlugin as ɵKeyEventsPlugin } from './browser.mjs';\nimport { ɵgetDOM as _getDOM, DOCUMENT } from '@angular/common';\nexport { ɵgetDOM } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Injectable, Inject, ɵglobal as _global, ApplicationRef, InjectionToken, ɵConsole as _Console, Optional, Injector, NgModule, forwardRef, ɵRuntimeError as _RuntimeError, ɵXSS_SECURITY_URL as _XSS_SECURITY_URL, SecurityContext, ɵallowSanitizationBypassAndThrow as _allowSanitizationBypassAndThrow, ɵunwrapSafeValue as _unwrapSafeValue, ɵ_sanitizeUrl as __sanitizeUrl, ɵ_sanitizeHtml as __sanitizeHtml, ɵbypassSanitizationTrustHtml as _bypassSanitizationTrustHtml, ɵbypassSanitizationTrustStyle as _bypassSanitizationTrustStyle, ɵbypassSanitizationTrustScript as _bypassSanitizationTrustScript, ɵbypassSanitizationTrustUrl as _bypassSanitizationTrustUrl, ɵbypassSanitizationTrustResourceUrl as _bypassSanitizationTrustResourceUrl, ɵwithI18nSupport as _withI18nSupport, ɵwithEventReplay as _withEventReplay, ɵwithIncrementalHydration as _withIncrementalHydration, makeEnvironmentProviders, ɵwithDomHydration as _withDomHydration, ENVIRONMENT_INITIALIZER, inject, NgZone, ɵZONELESS_ENABLED as _ZONELESS_ENABLED, ɵformatRuntimeError as _formatRuntimeError, Version } from '@angular/core';\nimport { EventManagerPlugin, EVENT_MANAGER_PLUGINS } from './dom_renderer.mjs';\nexport { EventManager, REMOVE_STYLES_ON_COMPONENT_DESTROY, DomRendererFactory2 as ɵDomRendererFactory2, SharedStylesHost as ɵSharedStylesHost } from './dom_renderer.mjs';\nimport { ɵwithHttpTransferCache as _withHttpTransferCache } from '@angular/common/http';\n\n/**\n * A service for managing HTML `<meta>` tags.\n *\n * Properties of the `MetaDefinition` object match the attributes of the\n * HTML `<meta>` tag. These tags define document metadata that is important for\n * things like configuring a Content Security Policy, defining browser compatibility\n * and security settings, setting HTTP Headers, defining rich content for social sharing,\n * and Search Engine Optimization (SEO).\n *\n * To identify specific `<meta>` tags in a document, use an attribute selection\n * string in the format `\"tag_attribute='value string'\"`.\n * For example, an `attrSelector` value of `\"name='description'\"` matches a tag\n * whose `name` attribute has the value `\"description\"`.\n * Selectors are used with the `querySelector()` Document method,\n * in the format `meta[{attrSelector}]`.\n *\n * @see [HTML meta tag](https://developer.mozilla.org/docs/Web/HTML/Element/meta)\n * @see [Document.querySelector()](https://developer.mozilla.org/docs/Web/API/Document/querySelector)\n *\n *\n * @publicApi\n */\nclass Meta {\n  _doc;\n  _dom;\n  constructor(_doc) {\n    this._doc = _doc;\n    this._dom = _getDOM();\n  }\n  /**\n   * Retrieves or creates a specific `<meta>` tag element in the current HTML document.\n   * In searching for an existing tag, Angular attempts to match the `name` or `property` attribute\n   * values in the provided tag definition, and verifies that all other attribute values are equal.\n   * If an existing element is found, it is returned and is not modified in any way.\n   * @param tag The definition of a `<meta>` element to match or create.\n   * @param forceCreation True to create a new element without checking whether one already exists.\n   * @returns The existing element with the same attributes and values if found,\n   * the new element if no match is found, or `null` if the tag parameter is not defined.\n   */\n  addTag(tag, forceCreation = false) {\n    if (!tag) return null;\n    return this._getOrCreateElement(tag, forceCreation);\n  }\n  /**\n   * Retrieves or creates a set of `<meta>` tag elements in the current HTML document.\n   * In searching for an existing tag, Angular attempts to match the `name` or `property` attribute\n   * values in the provided tag definition, and verifies that all other attribute values are equal.\n   * @param tags An array of tag definitions to match or create.\n   * @param forceCreation True to create new elements without checking whether they already exist.\n   * @returns The matching elements if found, or the new elements.\n   */\n  addTags(tags, forceCreation = false) {\n    if (!tags) return [];\n    return tags.reduce((result, tag) => {\n      if (tag) {\n        result.push(this._getOrCreateElement(tag, forceCreation));\n      }\n      return result;\n    }, []);\n  }\n  /**\n   * Retrieves a `<meta>` tag element in the current HTML document.\n   * @param attrSelector The tag attribute and value to match against, in the format\n   * `\"tag_attribute='value string'\"`.\n   * @returns The matching element, if any.\n   */\n  getTag(attrSelector) {\n    if (!attrSelector) return null;\n    return this._doc.querySelector(`meta[${attrSelector}]`) || null;\n  }\n  /**\n   * Retrieves a set of `<meta>` tag elements in the current HTML document.\n   * @param attrSelector The tag attribute and value to match against, in the format\n   * `\"tag_attribute='value string'\"`.\n   * @returns The matching elements, if any.\n   */\n  getTags(attrSelector) {\n    if (!attrSelector) return [];\n    const list /*NodeList*/ = this._doc.querySelectorAll(`meta[${attrSelector}]`);\n    return list ? [].slice.call(list) : [];\n  }\n  /**\n   * Modifies an existing `<meta>` tag element in the current HTML document.\n   * @param tag The tag description with which to replace the existing tag content.\n   * @param selector A tag attribute and value to match against, to identify\n   * an existing tag. A string in the format `\"tag_attribute=`value string`\"`.\n   * If not supplied, matches a tag with the same `name` or `property` attribute value as the\n   * replacement tag.\n   * @return The modified element.\n   */\n  updateTag(tag, selector) {\n    if (!tag) return null;\n    selector = selector || this._parseSelector(tag);\n    const meta = this.getTag(selector);\n    if (meta) {\n      return this._setMetaElementAttributes(tag, meta);\n    }\n    return this._getOrCreateElement(tag, true);\n  }\n  /**\n   * Removes an existing `<meta>` tag element from the current HTML document.\n   * @param attrSelector A tag attribute and value to match against, to identify\n   * an existing tag. A string in the format `\"tag_attribute=`value string`\"`.\n   */\n  removeTag(attrSelector) {\n    this.removeTagElement(this.getTag(attrSelector));\n  }\n  /**\n   * Removes an existing `<meta>` tag element from the current HTML document.\n   * @param meta The tag definition to match against to identify an existing tag.\n   */\n  removeTagElement(meta) {\n    if (meta) {\n      this._dom.remove(meta);\n    }\n  }\n  _getOrCreateElement(meta, forceCreation = false) {\n    if (!forceCreation) {\n      const selector = this._parseSelector(meta);\n      // It's allowed to have multiple elements with the same name so it's not enough to\n      // just check that element with the same name already present on the page. We also need to\n      // check if element has tag attributes\n      const elem = this.getTags(selector).filter(elem => this._containsAttributes(meta, elem))[0];\n      if (elem !== undefined) return elem;\n    }\n    const element = this._dom.createElement('meta');\n    this._setMetaElementAttributes(meta, element);\n    const head = this._doc.getElementsByTagName('head')[0];\n    head.appendChild(element);\n    return element;\n  }\n  _setMetaElementAttributes(tag, el) {\n    Object.keys(tag).forEach(prop => el.setAttribute(this._getMetaKeyMap(prop), tag[prop]));\n    return el;\n  }\n  _parseSelector(tag) {\n    const attr = tag.name ? 'name' : 'property';\n    return `${attr}=\"${tag[attr]}\"`;\n  }\n  _containsAttributes(tag, elem) {\n    return Object.keys(tag).every(key => elem.getAttribute(this._getMetaKeyMap(key)) === tag[key]);\n  }\n  _getMetaKeyMap(prop) {\n    return META_KEYS_MAP[prop] || prop;\n  }\n  static ɵfac = function Meta_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || Meta)(i0.ɵɵinject(DOCUMENT));\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: Meta,\n    factory: Meta.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Meta, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }], null);\n})();\n/**\n * Mapping for MetaDefinition properties with their correct meta attribute names\n */\nconst META_KEYS_MAP = {\n  httpEquiv: 'http-equiv'\n};\n\n/**\n * A service that can be used to get and set the title of a current HTML document.\n *\n * Since an Angular application can't be bootstrapped on the entire HTML document (`<html>` tag)\n * it is not possible to bind to the `text` property of the `HTMLTitleElement` elements\n * (representing the `<title>` tag). Instead, this service can be used to set and get the current\n * title value.\n *\n * @publicApi\n */\nclass Title {\n  _doc;\n  constructor(_doc) {\n    this._doc = _doc;\n  }\n  /**\n   * Get the title of the current HTML document.\n   */\n  getTitle() {\n    return this._doc.title;\n  }\n  /**\n   * Set the title of the current HTML document.\n   * @param newTitle\n   */\n  setTitle(newTitle) {\n    this._doc.title = newTitle || '';\n  }\n  static ɵfac = function Title_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || Title)(i0.ɵɵinject(DOCUMENT));\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: Title,\n    factory: Title.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Title, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }], null);\n})();\n\n/// <reference path=\"../../../goog.d.ts\" />\n/**\n * Exports the value under a given `name` in the global property `ng`. For example `ng.probe` if\n * `name` is `'probe'`.\n * @param name Name under which it will be exported. Keep in mind this will be a property of the\n * global `ng` object.\n * @param value The value to export.\n */\nfunction exportNgVar(name, value) {\n  if (typeof COMPILED === 'undefined' || !COMPILED) {\n    // Note: we can't export `ng` when using closure enhanced optimization as:\n    // - closure declares globals itself for minified names, which sometimes clobber our `ng` global\n    // - we can't declare a closure extern as the namespace `ng` is already used within Google\n    //   for typings for angularJS (via `goog.provide('ng....')`).\n    const ng = _global['ng'] = _global['ng'] || {};\n    ng[name] = value;\n  }\n}\nclass ChangeDetectionPerfRecord {\n  msPerTick;\n  numTicks;\n  constructor(msPerTick, numTicks) {\n    this.msPerTick = msPerTick;\n    this.numTicks = numTicks;\n  }\n}\n/**\n * Entry point for all Angular profiling-related debug tools. This object\n * corresponds to the `ng.profiler` in the dev console.\n */\nclass AngularProfiler {\n  appRef;\n  constructor(ref) {\n    this.appRef = ref.injector.get(ApplicationRef);\n  }\n  // tslint:disable:no-console\n  /**\n   * Exercises change detection in a loop and then prints the average amount of\n   * time in milliseconds how long a single round of change detection takes for\n   * the current state of the UI. It runs a minimum of 5 rounds for a minimum\n   * of 500 milliseconds.\n   *\n   * Optionally, a user may pass a `config` parameter containing a map of\n   * options. Supported options are:\n   *\n   * `record` (boolean) - causes the profiler to record a CPU profile while\n   * it exercises the change detector. Example:\n   *\n   * ```ts\n   * ng.profiler.timeChangeDetection({record: true})\n   * ```\n   */\n  timeChangeDetection(config) {\n    const record = config && config['record'];\n    const profileName = 'Change Detection';\n    // Profiler is not available in Android browsers without dev tools opened\n    if (record && 'profile' in console && typeof console.profile === 'function') {\n      console.profile(profileName);\n    }\n    const start = performance.now();\n    let numTicks = 0;\n    while (numTicks < 5 || performance.now() - start < 500) {\n      this.appRef.tick();\n      numTicks++;\n    }\n    const end = performance.now();\n    if (record && 'profileEnd' in console && typeof console.profileEnd === 'function') {\n      console.profileEnd(profileName);\n    }\n    const msPerTick = (end - start) / numTicks;\n    console.log(`ran ${numTicks} change detection cycles`);\n    console.log(`${msPerTick.toFixed(2)} ms per check`);\n    return new ChangeDetectionPerfRecord(msPerTick, numTicks);\n  }\n}\nconst PROFILER_GLOBAL_NAME = 'profiler';\n/**\n * Enabled Angular debug tools that are accessible via your browser's\n * developer console.\n *\n * Usage:\n *\n * 1. Open developer console (e.g. in Chrome Ctrl + Shift + j)\n * 1. Type `ng.` (usually the console will show auto-complete suggestion)\n * 1. Try the change detection profiler `ng.profiler.timeChangeDetection()`\n *    then hit Enter.\n *\n * @publicApi\n */\nfunction enableDebugTools(ref) {\n  exportNgVar(PROFILER_GLOBAL_NAME, new AngularProfiler(ref));\n  return ref;\n}\n/**\n * Disables Angular tools.\n *\n * @publicApi\n */\nfunction disableDebugTools() {\n  exportNgVar(PROFILER_GLOBAL_NAME, null);\n}\n\n/**\n * Predicates for use with {@link DebugElement}'s query functions.\n *\n * @publicApi\n */\nclass By {\n  /**\n   * Match all nodes.\n   *\n   * @usageNotes\n   * ### Example\n   *\n   * {@example platform-browser/dom/debug/ts/by/by.ts region='by_all'}\n   */\n  static all() {\n    return () => true;\n  }\n  /**\n   * Match elements by the given CSS selector.\n   *\n   * @usageNotes\n   * ### Example\n   *\n   * {@example platform-browser/dom/debug/ts/by/by.ts region='by_css'}\n   */\n  static css(selector) {\n    return debugElement => {\n      return debugElement.nativeElement != null ? elementMatches(debugElement.nativeElement, selector) : false;\n    };\n  }\n  /**\n   * Match nodes that have the given directive present.\n   *\n   * @usageNotes\n   * ### Example\n   *\n   * {@example platform-browser/dom/debug/ts/by/by.ts region='by_directive'}\n   */\n  static directive(type) {\n    return debugNode => debugNode.providerTokens.indexOf(type) !== -1;\n  }\n}\nfunction elementMatches(n, selector) {\n  if (_getDOM().isElementNode(n)) {\n    return n.matches && n.matches(selector) || n.msMatchesSelector && n.msMatchesSelector(selector) || n.webkitMatchesSelector && n.webkitMatchesSelector(selector);\n  }\n  return false;\n}\n\n/// <reference types=\"hammerjs\" />\n/**\n * Supported HammerJS recognizer event names.\n */\nconst EVENT_NAMES = {\n  // pan\n  'pan': true,\n  'panstart': true,\n  'panmove': true,\n  'panend': true,\n  'pancancel': true,\n  'panleft': true,\n  'panright': true,\n  'panup': true,\n  'pandown': true,\n  // pinch\n  'pinch': true,\n  'pinchstart': true,\n  'pinchmove': true,\n  'pinchend': true,\n  'pinchcancel': true,\n  'pinchin': true,\n  'pinchout': true,\n  // press\n  'press': true,\n  'pressup': true,\n  // rotate\n  'rotate': true,\n  'rotatestart': true,\n  'rotatemove': true,\n  'rotateend': true,\n  'rotatecancel': true,\n  // swipe\n  'swipe': true,\n  'swipeleft': true,\n  'swiperight': true,\n  'swipeup': true,\n  'swipedown': true,\n  // tap\n  'tap': true,\n  'doubletap': true\n};\n/**\n * DI token for providing [HammerJS](https://hammerjs.github.io/) support to Angular.\n * @see {@link HammerGestureConfig}\n *\n * @ngModule HammerModule\n * @publicApi\n *\n * @deprecated The HammerJS integration is deprecated. Replace it by your own implementation.\n */\nconst HAMMER_GESTURE_CONFIG = new InjectionToken(typeof ngDevMode === 'undefined' || ngDevMode ? 'HammerGestureConfig' : '');\n/**\n * Injection token used to provide a HammerLoader to Angular.\n *\n * @see {@link HammerLoader}\n *\n * @publicApi\n *\n * @deprecated The HammerJS integration is deprecated. Replace it by your own implementation.\n */\nconst HAMMER_LOADER = new InjectionToken(typeof ngDevMode === 'undefined' || ngDevMode ? 'HammerLoader' : '');\n/**\n * An injectable [HammerJS Manager](https://hammerjs.github.io/api/#hammermanager)\n * for gesture recognition. Configures specific event recognition.\n * @publicApi\n *\n * @deprecated The HammerJS integration is deprecated. Replace it by your own implementation.\n */\nclass HammerGestureConfig {\n  /**\n   * A set of supported event names for gestures to be used in Angular.\n   * Angular supports all built-in recognizers, as listed in\n   * [HammerJS documentation](https://hammerjs.github.io/).\n   */\n  events = [];\n  /**\n   * Maps gesture event names to a set of configuration options\n   * that specify overrides to the default values for specific properties.\n   *\n   * The key is a supported event name to be configured,\n   * and the options object contains a set of properties, with override values\n   * to be applied to the named recognizer event.\n   * For example, to disable recognition of the rotate event, specify\n   *  `{\"rotate\": {\"enable\": false}}`.\n   *\n   * Properties that are not present take the HammerJS default values.\n   * For information about which properties are supported for which events,\n   * and their allowed and default values, see\n   * [HammerJS documentation](https://hammerjs.github.io/).\n   *\n   */\n  overrides = {};\n  /**\n   * Properties whose default values can be overridden for a given event.\n   * Different sets of properties apply to different events.\n   * For information about which properties are supported for which events,\n   * and their allowed and default values, see\n   * [HammerJS documentation](https://hammerjs.github.io/).\n   */\n  options;\n  /**\n   * Creates a [HammerJS Manager](https://hammerjs.github.io/api/#hammermanager)\n   * and attaches it to a given HTML element.\n   * @param element The element that will recognize gestures.\n   * @returns A HammerJS event-manager object.\n   */\n  buildHammer(element) {\n    const mc = new Hammer(element, this.options);\n    mc.get('pinch').set({\n      enable: true\n    });\n    mc.get('rotate').set({\n      enable: true\n    });\n    for (const eventName in this.overrides) {\n      mc.get(eventName).set(this.overrides[eventName]);\n    }\n    return mc;\n  }\n  static ɵfac = function HammerGestureConfig_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || HammerGestureConfig)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: HammerGestureConfig,\n    factory: HammerGestureConfig.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HammerGestureConfig, [{\n    type: Injectable\n  }], null, null);\n})();\n/**\n * Event plugin that adds Hammer support to an application.\n *\n * @ngModule HammerModule\n */\nclass HammerGesturesPlugin extends EventManagerPlugin {\n  _config;\n  _injector;\n  loader;\n  _loaderPromise = null;\n  constructor(doc, _config, _injector, loader) {\n    super(doc);\n    this._config = _config;\n    this._injector = _injector;\n    this.loader = loader;\n  }\n  supports(eventName) {\n    if (!EVENT_NAMES.hasOwnProperty(eventName.toLowerCase()) && !this.isCustomEvent(eventName)) {\n      return false;\n    }\n    if (!window.Hammer && !this.loader) {\n      if (typeof ngDevMode === 'undefined' || ngDevMode) {\n        // Get a `Console` through an injector to tree-shake the\n        // class when it is unused in production.\n        const _console = this._injector.get(_Console);\n        _console.warn(`The \"${eventName}\" event cannot be bound because Hammer.JS is not ` + `loaded and no custom loader has been specified.`);\n      }\n      return false;\n    }\n    return true;\n  }\n  addEventListener(element, eventName, handler) {\n    const zone = this.manager.getZone();\n    eventName = eventName.toLowerCase();\n    // If Hammer is not present but a loader is specified, we defer adding the event listener\n    // until Hammer is loaded.\n    if (!window.Hammer && this.loader) {\n      this._loaderPromise = this._loaderPromise || zone.runOutsideAngular(() => this.loader());\n      // This `addEventListener` method returns a function to remove the added listener.\n      // Until Hammer is loaded, the returned function needs to *cancel* the registration rather\n      // than remove anything.\n      let cancelRegistration = false;\n      let deregister = () => {\n        cancelRegistration = true;\n      };\n      zone.runOutsideAngular(() => this._loaderPromise.then(() => {\n        // If Hammer isn't actually loaded when the custom loader resolves, give up.\n        if (!window.Hammer) {\n          if (typeof ngDevMode === 'undefined' || ngDevMode) {\n            const _console = this._injector.get(_Console);\n            _console.warn(`The custom HAMMER_LOADER completed, but Hammer.JS is not present.`);\n          }\n          deregister = () => {};\n          return;\n        }\n        if (!cancelRegistration) {\n          // Now that Hammer is loaded and the listener is being loaded for real,\n          // the deregistration function changes from canceling registration to\n          // removal.\n          deregister = this.addEventListener(element, eventName, handler);\n        }\n      }).catch(() => {\n        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n          const _console = this._injector.get(_Console);\n          _console.warn(`The \"${eventName}\" event cannot be bound because the custom ` + `Hammer.JS loader failed.`);\n        }\n        deregister = () => {};\n      }));\n      // Return a function that *executes* `deregister` (and not `deregister` itself) so that we\n      // can change the behavior of `deregister` once the listener is added. Using a closure in\n      // this way allows us to avoid any additional data structures to track listener removal.\n      return () => {\n        deregister();\n      };\n    }\n    return zone.runOutsideAngular(() => {\n      // Creating the manager bind events, must be done outside of angular\n      const mc = this._config.buildHammer(element);\n      const callback = function (eventObj) {\n        zone.runGuarded(function () {\n          handler(eventObj);\n        });\n      };\n      mc.on(eventName, callback);\n      return () => {\n        mc.off(eventName, callback);\n        // destroy mc to prevent memory leak\n        if (typeof mc.destroy === 'function') {\n          mc.destroy();\n        }\n      };\n    });\n  }\n  isCustomEvent(eventName) {\n    return this._config.events.indexOf(eventName) > -1;\n  }\n  static ɵfac = function HammerGesturesPlugin_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || HammerGesturesPlugin)(i0.ɵɵinject(DOCUMENT), i0.ɵɵinject(HAMMER_GESTURE_CONFIG), i0.ɵɵinject(i0.Injector), i0.ɵɵinject(HAMMER_LOADER, 8));\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: HammerGesturesPlugin,\n    factory: HammerGesturesPlugin.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HammerGesturesPlugin, [{\n    type: Injectable\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }, {\n    type: HammerGestureConfig,\n    decorators: [{\n      type: Inject,\n      args: [HAMMER_GESTURE_CONFIG]\n    }]\n  }, {\n    type: i0.Injector\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [HAMMER_LOADER]\n    }]\n  }], null);\n})();\n/**\n * Adds support for HammerJS.\n *\n * Import this module at the root of your application so that Angular can work with\n * HammerJS to detect gesture events.\n *\n * Note that applications still need to include the HammerJS script itself. This module\n * simply sets up the coordination layer between HammerJS and Angular's `EventManager`.\n *\n * @publicApi\n *\n * @deprecated The hammer integration is deprecated. Replace it by your own implementation.\n */\nclass HammerModule {\n  static ɵfac = function HammerModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || HammerModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: HammerModule\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    providers: [{\n      provide: EVENT_MANAGER_PLUGINS,\n      useClass: HammerGesturesPlugin,\n      multi: true,\n      deps: [DOCUMENT, HAMMER_GESTURE_CONFIG, Injector, [new Optional(), HAMMER_LOADER]]\n    }, {\n      provide: HAMMER_GESTURE_CONFIG,\n      useClass: HammerGestureConfig\n    }]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HammerModule, [{\n    type: NgModule,\n    args: [{\n      providers: [{\n        provide: EVENT_MANAGER_PLUGINS,\n        useClass: HammerGesturesPlugin,\n        multi: true,\n        deps: [DOCUMENT, HAMMER_GESTURE_CONFIG, Injector, [new Optional(), HAMMER_LOADER]]\n      }, {\n        provide: HAMMER_GESTURE_CONFIG,\n        useClass: HammerGestureConfig\n      }]\n    }]\n  }], null, null);\n})();\n\n/**\n * DomSanitizer helps preventing Cross Site Scripting Security bugs (XSS) by sanitizing\n * values to be safe to use in the different DOM contexts.\n *\n * For example, when binding a URL in an `<a [href]=\"someValue\">` hyperlink, `someValue` will be\n * sanitized so that an attacker cannot inject e.g. a `javascript:` URL that would execute code on\n * the website.\n *\n * In specific situations, it might be necessary to disable sanitization, for example if the\n * application genuinely needs to produce a `javascript:` style link with a dynamic value in it.\n * Users can bypass security by constructing a value with one of the `bypassSecurityTrust...`\n * methods, and then binding to that value from the template.\n *\n * These situations should be very rare, and extraordinary care must be taken to avoid creating a\n * Cross Site Scripting (XSS) security bug!\n *\n * When using `bypassSecurityTrust...`, make sure to call the method as early as possible and as\n * close as possible to the source of the value, to make it easy to verify no security bug is\n * created by its use.\n *\n * It is not required (and not recommended) to bypass security if the value is safe, e.g. a URL that\n * does not start with a suspicious protocol, or an HTML snippet that does not contain dangerous\n * code. The sanitizer leaves safe values intact.\n *\n * @security Calling any of the `bypassSecurityTrust...` APIs disables Angular's built-in\n * sanitization for the value passed in. Carefully check and audit all values and code paths going\n * into this call. Make sure any user data is appropriately escaped for this security context.\n * For more detail, see the [Security Guide](https://g.co/ng/security).\n *\n * @publicApi\n */\nclass DomSanitizer {\n  static ɵfac = function DomSanitizer_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || DomSanitizer)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: DomSanitizer,\n    factory: function DomSanitizer_Factory(__ngFactoryType__) {\n      let __ngConditionalFactory__ = null;\n      if (__ngFactoryType__) {\n        __ngConditionalFactory__ = new (__ngFactoryType__ || DomSanitizer)();\n      } else {\n        __ngConditionalFactory__ = i0.ɵɵinject(DomSanitizerImpl);\n      }\n      return __ngConditionalFactory__;\n    },\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DomSanitizer, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root',\n      useExisting: forwardRef(() => DomSanitizerImpl)\n    }]\n  }], null, null);\n})();\nclass DomSanitizerImpl extends DomSanitizer {\n  _doc;\n  constructor(_doc) {\n    super();\n    this._doc = _doc;\n  }\n  sanitize(ctx, value) {\n    if (value == null) return null;\n    switch (ctx) {\n      case SecurityContext.NONE:\n        return value;\n      case SecurityContext.HTML:\n        if (_allowSanitizationBypassAndThrow(value, \"HTML\" /* BypassType.Html */)) {\n          return _unwrapSafeValue(value);\n        }\n        return __sanitizeHtml(this._doc, String(value)).toString();\n      case SecurityContext.STYLE:\n        if (_allowSanitizationBypassAndThrow(value, \"Style\" /* BypassType.Style */)) {\n          return _unwrapSafeValue(value);\n        }\n        return value;\n      case SecurityContext.SCRIPT:\n        if (_allowSanitizationBypassAndThrow(value, \"Script\" /* BypassType.Script */)) {\n          return _unwrapSafeValue(value);\n        }\n        throw new _RuntimeError(5200 /* RuntimeErrorCode.SANITIZATION_UNSAFE_SCRIPT */, (typeof ngDevMode === 'undefined' || ngDevMode) && 'unsafe value used in a script context');\n      case SecurityContext.URL:\n        if (_allowSanitizationBypassAndThrow(value, \"URL\" /* BypassType.Url */)) {\n          return _unwrapSafeValue(value);\n        }\n        return __sanitizeUrl(String(value));\n      case SecurityContext.RESOURCE_URL:\n        if (_allowSanitizationBypassAndThrow(value, \"ResourceURL\" /* BypassType.ResourceUrl */)) {\n          return _unwrapSafeValue(value);\n        }\n        throw new _RuntimeError(5201 /* RuntimeErrorCode.SANITIZATION_UNSAFE_RESOURCE_URL */, (typeof ngDevMode === 'undefined' || ngDevMode) && `unsafe value used in a resource URL context (see ${_XSS_SECURITY_URL})`);\n      default:\n        throw new _RuntimeError(5202 /* RuntimeErrorCode.SANITIZATION_UNEXPECTED_CTX */, (typeof ngDevMode === 'undefined' || ngDevMode) && `Unexpected SecurityContext ${ctx} (see ${_XSS_SECURITY_URL})`);\n    }\n  }\n  bypassSecurityTrustHtml(value) {\n    return _bypassSanitizationTrustHtml(value);\n  }\n  bypassSecurityTrustStyle(value) {\n    return _bypassSanitizationTrustStyle(value);\n  }\n  bypassSecurityTrustScript(value) {\n    return _bypassSanitizationTrustScript(value);\n  }\n  bypassSecurityTrustUrl(value) {\n    return _bypassSanitizationTrustUrl(value);\n  }\n  bypassSecurityTrustResourceUrl(value) {\n    return _bypassSanitizationTrustResourceUrl(value);\n  }\n  static ɵfac = function DomSanitizerImpl_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || DomSanitizerImpl)(i0.ɵɵinject(DOCUMENT));\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: DomSanitizerImpl,\n    factory: DomSanitizerImpl.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DomSanitizerImpl, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }], null);\n})();\n\n/**\n * The list of features as an enum to uniquely type each `HydrationFeature`.\n * @see {@link HydrationFeature}\n *\n * @publicApi\n */\nvar HydrationFeatureKind;\n(function (HydrationFeatureKind) {\n  HydrationFeatureKind[HydrationFeatureKind[\"NoHttpTransferCache\"] = 0] = \"NoHttpTransferCache\";\n  HydrationFeatureKind[HydrationFeatureKind[\"HttpTransferCacheOptions\"] = 1] = \"HttpTransferCacheOptions\";\n  HydrationFeatureKind[HydrationFeatureKind[\"I18nSupport\"] = 2] = \"I18nSupport\";\n  HydrationFeatureKind[HydrationFeatureKind[\"EventReplay\"] = 3] = \"EventReplay\";\n  HydrationFeatureKind[HydrationFeatureKind[\"IncrementalHydration\"] = 4] = \"IncrementalHydration\";\n})(HydrationFeatureKind || (HydrationFeatureKind = {}));\n/**\n * Helper function to create an object that represents a Hydration feature.\n */\nfunction hydrationFeature(ɵkind, ɵproviders = [], ɵoptions = {}) {\n  return {\n    ɵkind,\n    ɵproviders\n  };\n}\n/**\n * Disables HTTP transfer cache. Effectively causes HTTP requests to be performed twice: once on the\n * server and other one on the browser.\n *\n * @publicApi\n */\nfunction withNoHttpTransferCache() {\n  // This feature has no providers and acts as a flag that turns off\n  // HTTP transfer cache (which otherwise is turned on by default).\n  return hydrationFeature(HydrationFeatureKind.NoHttpTransferCache);\n}\n/**\n * The function accepts an object, which allows to configure cache parameters,\n * such as which headers should be included (no headers are included by default),\n * whether POST requests should be cached or a callback function to determine if a\n * particular request should be cached.\n *\n * @publicApi\n */\nfunction withHttpTransferCacheOptions(options) {\n  // This feature has no providers and acts as a flag to pass options to the HTTP transfer cache.\n  return hydrationFeature(HydrationFeatureKind.HttpTransferCacheOptions, _withHttpTransferCache(options));\n}\n/**\n * Enables support for hydrating i18n blocks.\n *\n * @publicApi 20.0\n */\nfunction withI18nSupport() {\n  return hydrationFeature(HydrationFeatureKind.I18nSupport, _withI18nSupport());\n}\n/**\n * Enables support for replaying user events (e.g. `click`s) that happened on a page\n * before hydration logic has completed. Once an application is hydrated, all captured\n * events are replayed and relevant event listeners are executed.\n *\n * @usageNotes\n *\n * Basic example of how you can enable event replay in your application when\n * `bootstrapApplication` function is used:\n * ```ts\n * bootstrapApplication(AppComponent, {\n *   providers: [provideClientHydration(withEventReplay())]\n * });\n * ```\n * @publicApi\n * @see {@link provideClientHydration}\n */\nfunction withEventReplay() {\n  return hydrationFeature(HydrationFeatureKind.EventReplay, _withEventReplay());\n}\n/**\n * Enables support for incremental hydration using the `hydrate` trigger syntax.\n *\n * @usageNotes\n *\n * Basic example of how you can enable incremental hydration in your application when\n * the `bootstrapApplication` function is used:\n * ```ts\n * bootstrapApplication(AppComponent, {\n *   providers: [provideClientHydration(withIncrementalHydration())]\n * });\n * ```\n * @publicApi 20.0\n * @see {@link provideClientHydration}\n */\nfunction withIncrementalHydration() {\n  return hydrationFeature(HydrationFeatureKind.IncrementalHydration, _withIncrementalHydration());\n}\n/**\n * Returns an `ENVIRONMENT_INITIALIZER` token setup with a function\n * that verifies whether compatible ZoneJS was used in an application\n * and logs a warning in a console if it's not the case.\n */\nfunction provideZoneJsCompatibilityDetector() {\n  return [{\n    provide: ENVIRONMENT_INITIALIZER,\n    useValue: () => {\n      const ngZone = inject(NgZone);\n      const isZoneless = inject(_ZONELESS_ENABLED);\n      // Checking `ngZone instanceof NgZone` would be insufficient here,\n      // because custom implementations might use NgZone as a base class.\n      if (!isZoneless && ngZone.constructor !== NgZone) {\n        const console = inject(_Console);\n        const message = _formatRuntimeError(-5000 /* RuntimeErrorCode.UNSUPPORTED_ZONEJS_INSTANCE */, 'Angular detected that hydration was enabled for an application ' + 'that uses a custom or a noop Zone.js implementation. ' + 'This is not yet a fully supported configuration.');\n        console.warn(message);\n      }\n    },\n    multi: true\n  }];\n}\n/**\n * Sets up providers necessary to enable hydration functionality for the application.\n *\n * By default, the function enables the recommended set of features for the optimal\n * performance for most of the applications. It includes the following features:\n *\n * * Reconciling DOM hydration. Learn more about it [here](guide/hydration).\n * * [`HttpClient`](api/common/http/HttpClient) response caching while running on the server and\n * transferring this cache to the client to avoid extra HTTP requests. Learn more about data caching\n * [here](guide/ssr#caching-data-when-using-httpclient).\n *\n * These functions allow you to disable some of the default features or enable new ones:\n *\n * * {@link withNoHttpTransferCache} to disable HTTP transfer cache\n * * {@link withHttpTransferCacheOptions} to configure some HTTP transfer cache options\n * * {@link withI18nSupport} to enable hydration support for i18n blocks\n * * {@link withEventReplay} to enable support for replaying user events\n *\n * @usageNotes\n *\n * Basic example of how you can enable hydration in your application when\n * `bootstrapApplication` function is used:\n * ```ts\n * bootstrapApplication(AppComponent, {\n *   providers: [provideClientHydration()]\n * });\n * ```\n *\n * Alternatively if you are using NgModules, you would add `provideClientHydration`\n * to your root app module's provider list.\n * ```ts\n * @NgModule({\n *   declarations: [RootCmp],\n *   bootstrap: [RootCmp],\n *   providers: [provideClientHydration()],\n * })\n * export class AppModule {}\n * ```\n *\n * @see {@link withNoHttpTransferCache}\n * @see {@link withHttpTransferCacheOptions}\n * @see {@link withI18nSupport}\n * @see {@link withEventReplay}\n *\n * @param features Optional features to configure additional hydration behaviors.\n * @returns A set of providers to enable hydration.\n *\n * @publicApi 17.0\n */\nfunction provideClientHydration(...features) {\n  const providers = [];\n  const featuresKind = new Set();\n  for (const {\n    ɵproviders,\n    ɵkind\n  } of features) {\n    featuresKind.add(ɵkind);\n    if (ɵproviders.length) {\n      providers.push(ɵproviders);\n    }\n  }\n  const hasHttpTransferCacheOptions = featuresKind.has(HydrationFeatureKind.HttpTransferCacheOptions);\n  if (typeof ngDevMode !== 'undefined' && ngDevMode && featuresKind.has(HydrationFeatureKind.NoHttpTransferCache) && hasHttpTransferCacheOptions) {\n    throw new _RuntimeError(5001 /* RuntimeErrorCode.HYDRATION_CONFLICTING_FEATURES */, 'Configuration error: found both withHttpTransferCacheOptions() and withNoHttpTransferCache() in the same call to provideClientHydration(), which is a contradiction.');\n  }\n  return makeEnvironmentProviders([typeof ngDevMode !== 'undefined' && ngDevMode ? provideZoneJsCompatibilityDetector() : [], _withDomHydration(), featuresKind.has(HydrationFeatureKind.NoHttpTransferCache) || hasHttpTransferCacheOptions ? [] : _withHttpTransferCache({}), providers]);\n}\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of the platform-browser package.\n */\n/**\n * @publicApi\n */\nconst VERSION = new Version('20.1.1');\nexport { By, DomSanitizer, EVENT_MANAGER_PLUGINS, EventManagerPlugin, HAMMER_GESTURE_CONFIG, HAMMER_LOADER, HammerGestureConfig, HammerModule, HydrationFeatureKind, Meta, Title, VERSION, disableDebugTools, enableDebugTools, provideClientHydration, withEventReplay, withHttpTransferCacheOptions, withI18nSupport, withIncrementalHydration, withNoHttpTransferCache, DomSanitizerImpl as ɵDomSanitizerImpl, HammerGesturesPlugin as ɵHammerGesturesPlugin };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAeA,IAAM,wBAAwB,IAAI,eAAe,YAAY,wBAAwB,EAAE;AAOvF,IAAM,eAAN,MAAM,cAAa;AAAA,EACjB;AAAA,EACA;AAAA,EACA,qBAAqB,oBAAI,IAAI;AAAA;AAAA;AAAA;AAAA,EAI7B,YAAY,SAAS,OAAO;AAC1B,SAAK,QAAQ;AACb,YAAQ,QAAQ,YAAU;AACxB,aAAO,UAAU;AAAA,IACnB,CAAC;AACD,SAAK,WAAW,QAAQ,MAAM,EAAE,QAAQ;AAAA,EAC1C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,iBAAiB,SAAS,WAAW,SAAS,SAAS;AACrD,UAAM,SAAS,KAAK,eAAe,SAAS;AAC5C,WAAO,OAAO,iBAAiB,SAAS,WAAW,SAAS,OAAO;AAAA,EACrE;AAAA;AAAA;AAAA;AAAA,EAIA,UAAU;AACR,WAAO,KAAK;AAAA,EACd;AAAA;AAAA,EAEA,eAAe,WAAW;AACxB,QAAI,SAAS,KAAK,mBAAmB,IAAI,SAAS;AAClD,QAAI,QAAQ;AACV,aAAO;AAAA,IACT;AACA,UAAM,UAAU,KAAK;AACrB,aAAS,QAAQ,KAAK,CAAAA,YAAUA,QAAO,SAAS,SAAS,CAAC;AAC1D,QAAI,CAAC,QAAQ;AACX,YAAM,IAAI,aAAc,OAAkD,OAAO,cAAc,eAAe,cAAc,2CAA2C,SAAS,EAAE;AAAA,IACpL;AACA,SAAK,mBAAmB,IAAI,WAAW,MAAM;AAC7C,WAAO;AAAA,EACT;AAAA,EACA,OAAO,OAAO,SAAS,qBAAqB,mBAAmB;AAC7D,WAAO,KAAK,qBAAqB,eAAiB,SAAS,qBAAqB,GAAM,SAAY,MAAM,CAAC;AAAA,EAC3G;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,cAAa;AAAA,EACxB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,cAAc,CAAC;AAAA,IACrF,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,qBAAqB;AAAA,IAC9B,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG,IAAI;AACV,GAAG;AASH,IAAM,qBAAN,MAAyB;AAAA,EACvB;AAAA;AAAA,EAEA,YAAY,MAAM;AAChB,SAAK,OAAO;AAAA,EACd;AAAA;AAAA,EAEA;AACF;AAGA,IAAM,wBAAwB;AAK9B,SAAS,eAAe,UAAU;AAChC,aAAW,WAAW,UAAU;AAC9B,YAAQ,OAAO;AAAA,EACjB;AACF;AAOA,SAAS,mBAAmB,OAAO,KAAK;AACtC,QAAM,eAAe,IAAI,cAAc,OAAO;AAC9C,eAAa,cAAc;AAC3B,SAAO;AACT;AASA,SAAS,gBAAgB,KAAK,OAAO,QAAQ,UAAU;AACrD,QAAM,WAAW,IAAI,MAAM,iBAAiB,SAAS,qBAAqB,KAAK,KAAK,WAAW,qBAAqB,KAAK,KAAK,IAAI;AAClI,MAAI,UAAU;AACZ,eAAW,gBAAgB,UAAU;AACnC,mBAAa,gBAAgB,qBAAqB;AAClD,UAAI,wBAAwB,iBAAiB;AAG3C,iBAAS,IAAI,aAAa,KAAK,MAAM,aAAa,KAAK,YAAY,GAAG,IAAI,CAAC,GAAG;AAAA,UAC5E,OAAO;AAAA,UACP,UAAU,CAAC,YAAY;AAAA,QACzB,CAAC;AAAA,MACH,WAAW,aAAa,aAAa;AACnC,eAAO,IAAI,aAAa,aAAa;AAAA,UACnC,OAAO;AAAA,UACP,UAAU,CAAC,YAAY;AAAA,QACzB,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF;AACF;AAOA,SAAS,kBAAkB,KAAK,KAAK;AACnC,QAAM,cAAc,IAAI,cAAc,MAAM;AAC5C,cAAY,aAAa,OAAO,YAAY;AAC5C,cAAY,aAAa,QAAQ,GAAG;AACpC,SAAO;AACT;AACA,IAAM,mBAAN,MAAM,kBAAiB;AAAA,EACrB;AAAA,EACA;AAAA,EACA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,SAAS,oBAAI,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA,EAKjB,WAAW,oBAAI,IAAI;AAAA;AAAA;AAAA;AAAA,EAInB,QAAQ,oBAAI,IAAI;AAAA,EAChB,YAAY,KAAK,OAAO,OAGxB,aAAa,CAAC,GAAG;AACf,SAAK,MAAM;AACX,SAAK,QAAQ;AACb,SAAK,QAAQ;AACb,oBAAgB,KAAK,OAAO,KAAK,QAAQ,KAAK,QAAQ;AACtD,SAAK,MAAM,IAAI,IAAI,IAAI;AAAA,EACzB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,UAAU,QAAQ,MAAM;AACtB,eAAW,SAAS,QAAQ;AAC1B,WAAK,SAAS,OAAO,KAAK,QAAQ,kBAAkB;AAAA,IACtD;AACA,UAAM,QAAQ,WAAS,KAAK,SAAS,OAAO,KAAK,UAAU,iBAAiB,CAAC;AAAA,EAC/E;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,aAAa,QAAQ,MAAM;AACzB,eAAW,SAAS,QAAQ;AAC1B,WAAK,YAAY,OAAO,KAAK,MAAM;AAAA,IACrC;AACA,UAAM,QAAQ,WAAS,KAAK,YAAY,OAAO,KAAK,QAAQ,CAAC;AAAA,EAC/D;AAAA,EACA,SAAS,OAAO,QAAQ,SAAS;AAE/B,UAAM,SAAS,OAAO,IAAI,KAAK;AAE/B,QAAI,QAAQ;AACV,WAAK,OAAO,cAAc,eAAe,cAAc,OAAO,UAAU,GAAG;AAGzE,eAAO,SAAS,QAAQ,aAAW,QAAQ,aAAa,mBAAmB,EAAE,CAAC;AAAA,MAChF;AACA,aAAO;AAAA,IACT,OAAO;AAEL,aAAO,IAAI,OAAO;AAAA,QAChB,OAAO;AAAA,QACP,UAAU,CAAC,GAAG,KAAK,KAAK,EAAE,IAAI,UAAQ,KAAK,WAAW,MAAM,QAAQ,OAAO,KAAK,GAAG,CAAC,CAAC;AAAA,MACvF,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,YAAY,OAAO,QAAQ;AAEzB,UAAM,SAAS,OAAO,IAAI,KAAK;AAG/B,QAAI,QAAQ;AACV,aAAO;AACP,UAAI,OAAO,SAAS,GAAG;AACrB,uBAAe,OAAO,QAAQ;AAC9B,eAAO,OAAO,KAAK;AAAA,MACrB;AAAA,IACF;AAAA,EACF;AAAA,EACA,cAAc;AACZ,eAAW,CAAC,EAAE;AAAA,MACZ;AAAA,IACF,CAAC,KAAK,CAAC,GAAG,KAAK,QAAQ,GAAG,KAAK,QAAQ,GAAG;AACxC,qBAAe,QAAQ;AAAA,IACzB;AACA,SAAK,MAAM,MAAM;AAAA,EACnB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,QAAQ,UAAU;AAChB,SAAK,MAAM,IAAI,QAAQ;AAEvB,eAAW,CAAC,OAAO;AAAA,MACjB;AAAA,IACF,CAAC,KAAK,KAAK,QAAQ;AACjB,eAAS,KAAK,KAAK,WAAW,UAAU,mBAAmB,OAAO,KAAK,GAAG,CAAC,CAAC;AAAA,IAC9E;AACA,eAAW,CAAC,KAAK;AAAA,MACf;AAAA,IACF,CAAC,KAAK,KAAK,UAAU;AACnB,eAAS,KAAK,KAAK,WAAW,UAAU,kBAAkB,KAAK,KAAK,GAAG,CAAC,CAAC;AAAA,IAC3E;AAAA,EACF;AAAA,EACA,WAAW,UAAU;AACnB,SAAK,MAAM,OAAO,QAAQ;AAAA,EAC5B;AAAA,EACA,WAAW,MAAM,SAAS;AAExB,QAAI,KAAK,OAAO;AACd,cAAQ,aAAa,SAAS,KAAK,KAAK;AAAA,IAC1C;AAEA,QAA2C,OAAc;AACvD,cAAQ,aAAa,uBAAuB,KAAK,KAAK;AAAA,IACxD;AAEA,WAAO,KAAK,YAAY,OAAO;AAAA,EACjC;AAAA,EACA,OAAO,OAAO,SAAS,yBAAyB,mBAAmB;AACjE,WAAO,KAAK,qBAAqB,mBAAqB,SAAS,QAAQ,GAAM,SAAS,MAAM,GAAM,SAAS,WAAW,CAAC,GAAM,SAAS,WAAW,CAAC;AAAA,EACpJ;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,kBAAiB;AAAA,EAC5B,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,QAAQ;AAAA,IACjB,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,MAAM;AAAA,IACf,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,SAAS;AAAA,IAClB,GAAG;AAAA,MACD,MAAM;AAAA,IACR,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,WAAW;AAAA,IACpB,CAAC;AAAA,EACH,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAM,iBAAiB;AAAA,EACrB,OAAO;AAAA,EACP,SAAS;AAAA,EACT,SAAS;AAAA,EACT,OAAO;AAAA,EACP,SAAS;AAAA,EACT,QAAQ;AACV;AACA,IAAM,kBAAkB;AACxB,IAAM,uBAAuB;AAC7B,IAAM,kBAAkB;AACxB,IAAM,qBAAqB;AAC3B,IAAM,YAAY,WAAW,kBAAkB;AAC/C,IAAM,eAAe,cAAc,kBAAkB;AAIrD,IAAM,6CAA6C;AAQnD,IAAM,qCAAqC,IAAI,eAAe,YAAY,8BAA8B,IAAI;AAAA,EAC1G,YAAY;AAAA,EACZ,SAAS,MAAM;AACjB,CAAC;AACD,SAAS,qBAAqB,kBAAkB;AAC9C,SAAO,aAAa,QAAQ,iBAAiB,gBAAgB;AAC/D;AACA,SAAS,kBAAkB,kBAAkB;AAC3C,SAAO,UAAU,QAAQ,iBAAiB,gBAAgB;AAC5D;AACA,SAAS,kBAAkB,QAAQ,QAAQ;AACzC,SAAO,OAAO,IAAI,OAAK,EAAE,QAAQ,iBAAiB,MAAM,CAAC;AAC3D;AAmBA,SAAS,0BAA0B,UAAU,QAAQ;AACnD,MAAI,CAAC,UAAU;AACb,WAAO;AAAA,EACT;AACA,QAAM,sBAAsB,IAAI,IAAI,UAAU,kBAAkB;AAChE,SAAO,OAAO,IAAI,gBAAc;AAC9B,QAAI,CAAC,WAAW,SAAS,mBAAmB,GAAG;AAC7C,aAAO;AAAA,IACT;AACA,WAAO,WAAW,QAAQ,sBAAsB,CAAC,GAAG,iBAAiB;AACnE,UAAI,aAAa,CAAC,MAAM,OAAO,aAAa,WAAW,OAAO,KAAK,gBAAgB,KAAK,YAAY,GAAG;AACrG,eAAO,wBAAwB,YAAY;AAAA,MAC7C;AACA,YAAM;AAAA,QACJ,UAAU;AAAA,MACZ,IAAI,IAAI,IAAI,cAAc,mBAAmB;AAC7C,aAAO,wBAAwB,oBAAoB;AAAA,IACrD,CAAC;AAAA,EACH,CAAC;AACH;AACA,IAAM,sBAAN,MAAM,qBAAoB;AAAA,EACxB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,mBAAmB,oBAAI,IAAI;AAAA,EAC3B;AAAA,EACA;AAAA,EACA,YAAY,cAAc,kBAAkB,OAAO,2BAA2B,KAAK,YAAY,QAAQ,QAAQ,MAAM,iBAAiB,MAAM;AAC1I,SAAK,eAAe;AACpB,SAAK,mBAAmB;AACxB,SAAK,QAAQ;AACb,SAAK,4BAA4B;AACjC,SAAK,MAAM;AACX,SAAK,aAAa;AAClB,SAAK,SAAS;AACd,SAAK,QAAQ;AACb,SAAK,iBAAiB;AACtB,SAAK,mBAA0D;AAC/D,SAAK,kBAAkB,IAAI,oBAAoB,cAAc,KAAK,QAAQ,KAAK,kBAAkB,KAAK,cAAc;AAAA,EACtH;AAAA,EACA,eAAe,SAAS,MAAM;AAC5B,QAAI,CAAC,WAAW,CAAC,MAAM;AACrB,aAAO,KAAK;AAAA,IACd;AACA,QAA2C,OAAoE;AAE7G,aAAO,iCACF,OADE;AAAA,QAEL,eAAe,kBAAkB;AAAA,MACnC;AAAA,IACF;AACA,UAAM,WAAW,KAAK,oBAAoB,SAAS,IAAI;AAGvD,QAAI,oBAAoB,mCAAmC;AACzD,eAAS,YAAY,OAAO;AAAA,IAC9B,WAAW,oBAAoB,8BAA8B;AAC3D,eAAS,YAAY;AAAA,IACvB;AACA,WAAO;AAAA,EACT;AAAA,EACA,oBAAoB,SAAS,MAAM;AACjC,UAAM,mBAAmB,KAAK;AAC9B,QAAI,WAAW,iBAAiB,IAAI,KAAK,EAAE;AAC3C,QAAI,CAAC,UAAU;AACb,YAAM,MAAM,KAAK;AACjB,YAAM,SAAS,KAAK;AACpB,YAAM,eAAe,KAAK;AAC1B,YAAM,mBAAmB,KAAK;AAC9B,YAAM,4BAA4B,KAAK;AACvC,YAAM,mBAAmB,KAAK;AAC9B,YAAM,iBAAiB,KAAK;AAC5B,cAAQ,KAAK,eAAe;AAAA,QAC1B,KAAK,kBAAkB;AACrB,qBAAW,IAAI,kCAAkC,cAAc,kBAAkB,MAAM,KAAK,OAAO,2BAA2B,KAAK,QAAQ,kBAAkB,cAAc;AAC3K;AAAA,QACF,KAAK,kBAAkB;AACrB,iBAAO,IAAI,kBAAkB,cAAc,kBAAkB,SAAS,MAAM,KAAK,QAAQ,KAAK,OAAO,kBAAkB,cAAc;AAAA,QACvI;AACE,qBAAW,IAAI,6BAA6B,cAAc,kBAAkB,MAAM,2BAA2B,KAAK,QAAQ,kBAAkB,cAAc;AAC1J;AAAA,MACJ;AACA,uBAAiB,IAAI,KAAK,IAAI,QAAQ;AAAA,IACxC;AACA,WAAO;AAAA,EACT;AAAA,EACA,cAAc;AACZ,SAAK,iBAAiB,MAAM;AAAA,EAC9B;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,kBAAkB,aAAa;AAC7B,SAAK,iBAAiB,OAAO,WAAW;AAAA,EAC1C;AAAA,EACA,OAAO,OAAO,SAAS,4BAA4B,mBAAmB;AACpE,WAAO,KAAK,qBAAqB,sBAAwB,SAAS,YAAY,GAAM,SAAS,gBAAgB,GAAM,SAAS,MAAM,GAAM,SAAS,kCAAkC,GAAM,SAAS,QAAQ,GAAM,SAAS,WAAW,GAAM,SAAY,MAAM,GAAM,SAAS,SAAS,GAAM,SAAS,gBAAiB,CAAC,CAAC;AAAA,EACxT;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,qBAAoB;AAAA,EAC/B,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,qBAAqB,CAAC;AAAA,IAC5F,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,MAAM;AAAA,IACf,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,kCAAkC;AAAA,IAC3C,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,QAAQ;AAAA,IACjB,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,WAAW;AAAA,IACpB,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,SAAS;AAAA,IAClB,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAS;AAAA,IACT,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,cAAe;AAAA,IACxB,GAAG;AAAA,MACD,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAM,sBAAN,MAA0B;AAAA,EACxB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,OAAO,uBAAO,OAAO,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA,EAKzB,wBAAwB;AAAA,EACxB,YAAY,cAAc,KAAK,QAAQ,kBAAkB,gBAAgB;AACvE,SAAK,eAAe;AACpB,SAAK,MAAM;AACX,SAAK,SAAS;AACd,SAAK,mBAAmB;AACxB,SAAK,iBAAiB;AAAA,EACxB;AAAA,EACA,UAAU;AAAA,EAAC;AAAA,EACX,cAAc;AAAA,EACd,cAAc,MAAM,WAAW;AAC7B,QAAI,WAAW;AAUb,aAAO,KAAK,IAAI,gBAAgB,eAAe,SAAS,KAAK,WAAW,IAAI;AAAA,IAC9E;AACA,WAAO,KAAK,IAAI,cAAc,IAAI;AAAA,EACpC;AAAA,EACA,cAAc,OAAO;AACnB,WAAO,KAAK,IAAI,cAAc,KAAK;AAAA,EACrC;AAAA,EACA,WAAW,OAAO;AAChB,WAAO,KAAK,IAAI,eAAe,KAAK;AAAA,EACtC;AAAA,EACA,YAAY,QAAQ,UAAU;AAC5B,UAAM,eAAe,eAAe,MAAM,IAAI,OAAO,UAAU;AAC/D,iBAAa,YAAY,QAAQ;AAAA,EACnC;AAAA,EACA,aAAa,QAAQ,UAAU,UAAU;AACvC,QAAI,QAAQ;AACV,YAAM,eAAe,eAAe,MAAM,IAAI,OAAO,UAAU;AAC/D,mBAAa,aAAa,UAAU,QAAQ;AAAA,IAC9C;AAAA,EACF;AAAA,EACA,YAAY,SAAS,UAAU;AAC7B,aAAS,OAAO;AAAA,EAClB;AAAA,EACA,kBAAkB,gBAAgB,iBAAiB;AACjD,QAAI,KAAK,OAAO,mBAAmB,WAAW,KAAK,IAAI,cAAc,cAAc,IAAI;AACvF,QAAI,CAAC,IAAI;AACP,YAAM,IAAI,aAAc,QAAmD,OAAO,cAAc,eAAe,cAAc,iBAAiB,cAAc,8BAA8B;AAAA,IAC5L;AACA,QAAI,CAAC,iBAAiB;AACpB,SAAG,cAAc;AAAA,IACnB;AACA,WAAO;AAAA,EACT;AAAA,EACA,WAAW,MAAM;AACf,WAAO,KAAK;AAAA,EACd;AAAA,EACA,YAAY,MAAM;AAChB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,aAAa,IAAI,MAAM,OAAO,WAAW;AACvC,QAAI,WAAW;AACb,aAAO,YAAY,MAAM;AACzB,YAAM,eAAe,eAAe,SAAS;AAC7C,UAAI,cAAc;AAChB,WAAG,eAAe,cAAc,MAAM,KAAK;AAAA,MAC7C,OAAO;AACL,WAAG,aAAa,MAAM,KAAK;AAAA,MAC7B;AAAA,IACF,OAAO;AACL,SAAG,aAAa,MAAM,KAAK;AAAA,IAC7B;AAAA,EACF;AAAA,EACA,gBAAgB,IAAI,MAAM,WAAW;AACnC,QAAI,WAAW;AACb,YAAM,eAAe,eAAe,SAAS;AAC7C,UAAI,cAAc;AAChB,WAAG,kBAAkB,cAAc,IAAI;AAAA,MACzC,OAAO;AACL,WAAG,gBAAgB,GAAG,SAAS,IAAI,IAAI,EAAE;AAAA,MAC3C;AAAA,IACF,OAAO;AACL,SAAG,gBAAgB,IAAI;AAAA,IACzB;AAAA,EACF;AAAA,EACA,SAAS,IAAI,MAAM;AACjB,OAAG,UAAU,IAAI,IAAI;AAAA,EACvB;AAAA,EACA,YAAY,IAAI,MAAM;AACpB,OAAG,UAAU,OAAO,IAAI;AAAA,EAC1B;AAAA,EACA,SAAS,IAAI,OAAO,OAAO,OAAO;AAChC,QAAI,SAAS,oBAAoB,WAAW,oBAAoB,YAAY;AAC1E,SAAG,MAAM,YAAY,OAAO,OAAO,QAAQ,oBAAoB,YAAY,cAAc,EAAE;AAAA,IAC7F,OAAO;AACL,SAAG,MAAM,KAAK,IAAI;AAAA,IACpB;AAAA,EACF;AAAA,EACA,YAAY,IAAI,OAAO,OAAO;AAC5B,QAAI,QAAQ,oBAAoB,UAAU;AAExC,SAAG,MAAM,eAAe,KAAK;AAAA,IAC/B,OAAO;AACL,SAAG,MAAM,KAAK,IAAI;AAAA,IACpB;AAAA,EACF;AAAA,EACA,YAAY,IAAI,MAAM,OAAO;AAC3B,QAAI,MAAM,MAAM;AACd;AAAA,IACF;AACA,KAAC,OAAO,cAAc,eAAe,cAAc,KAAK,yBAAyB,qBAAqB,MAAM,UAAU;AACtH,OAAG,IAAI,IAAI;AAAA,EACb;AAAA,EACA,SAAS,MAAM,OAAO;AACpB,SAAK,YAAY;AAAA,EACnB;AAAA,EACA,OAAO,QAAQ,OAAO,UAAU,SAAS;AACvC,KAAC,OAAO,cAAc,eAAe,cAAc,KAAK,yBAAyB,qBAAqB,OAAO,UAAU;AACvH,QAAI,OAAO,WAAW,UAAU;AAC9B,eAAS,OAAQ,EAAE,qBAAqB,KAAK,KAAK,MAAM;AACxD,UAAI,CAAC,QAAQ;AACX,cAAM,IAAI,aAAc,OAAuD,OAAO,cAAc,eAAe,cAAc,4BAA4B,MAAM,cAAc,KAAK,EAAE;AAAA,MAC1L;AAAA,IACF;AACA,QAAI,kBAAkB,KAAK,uBAAuB,QAAQ;AAC1D,QAAI,KAAK,gBAAgB,mBAAmB;AAC1C,wBAAkB,KAAK,eAAe,kBAAkB,QAAQ,OAAO,eAAe;AAAA,IACxF;AACA,WAAO,KAAK,aAAa,iBAAiB,QAAQ,OAAO,iBAAiB,OAAO;AAAA,EACnF;AAAA,EACA,uBAAuB,cAAc;AAKnC,WAAO,WAAS;AAMd,UAAI,UAAU,gBAAgB;AAC5B,eAAO;AAAA,MACT;AAGA,YAAM,uBAA8D,QAAe,KAAK,OAAO,WAAW,MAAM,aAAa,KAAK,CAAC,IAAI,aAAa,KAAK;AACzJ,UAAI,yBAAyB,OAAO;AAClC,cAAM,eAAe;AAAA,MACvB;AACA,aAAO;AAAA,IACT;AAAA,EACF;AACF;AACA,IAAM,eAAe,MAAM,IAAI,WAAW,CAAC,GAAG;AAC9C,SAAS,qBAAqB,MAAM,UAAU;AAC5C,MAAI,KAAK,WAAW,CAAC,MAAM,aAAa;AACtC,UAAM,IAAI,aAAc,MAA2D,wBAAwB,QAAQ,IAAI,IAAI;AAAA;AAAA,+DAEhE,IAAI,iIAAiI;AAAA,EAClM;AACF;AACA,SAAS,eAAe,MAAM;AAC5B,SAAO,KAAK,YAAY,cAAc,KAAK,YAAY;AACzD;AACA,IAAM,oBAAN,cAAgC,oBAAoB;AAAA,EAClD;AAAA,EACA;AAAA,EACA;AAAA,EACA,YAAY,cAAc,kBAAkB,QAAQ,WAAW,KAAK,QAAQ,OAAO,kBAAkB,gBAAgB;AACnH,UAAM,cAAc,KAAK,QAAQ,kBAAkB,cAAc;AACjE,SAAK,mBAAmB;AACxB,SAAK,SAAS;AACd,SAAK,aAAa,OAAO,aAAa;AAAA,MACpC,MAAM;AAAA,IACR,CAAC;AACD,SAAK,iBAAiB,QAAQ,KAAK,UAAU;AAC7C,QAAI,SAAS,UAAU;AACvB,QAAI,WAAW;AAEb,YAAM,WAAW,OAAQ,EAAE,YAAY,GAAG,KAAK;AAC/C,eAAS,0BAA0B,UAAU,MAAM;AAAA,IACrD;AACA,aAAS,kBAAkB,UAAU,IAAI,MAAM;AAC/C,eAAW,SAAS,QAAQ;AAC1B,YAAM,UAAU,SAAS,cAAc,OAAO;AAC9C,UAAI,OAAO;AACT,gBAAQ,aAAa,SAAS,KAAK;AAAA,MACrC;AACA,cAAQ,cAAc;AACtB,WAAK,WAAW,YAAY,OAAO;AAAA,IACrC;AAOA,UAAM,YAAY,UAAU,oBAAoB;AAChD,QAAI,WAAW;AACb,iBAAW,YAAY,WAAW;AAChC,cAAM,SAAS,kBAAkB,UAAU,GAAG;AAC9C,YAAI,OAAO;AACT,iBAAO,aAAa,SAAS,KAAK;AAAA,QACpC;AACA,aAAK,WAAW,YAAY,MAAM;AAAA,MACpC;AAAA,IACF;AAAA,EACF;AAAA,EACA,iBAAiB,MAAM;AACrB,WAAO,SAAS,KAAK,SAAS,KAAK,aAAa;AAAA,EAClD;AAAA,EACA,YAAY,QAAQ,UAAU;AAC5B,WAAO,MAAM,YAAY,KAAK,iBAAiB,MAAM,GAAG,QAAQ;AAAA,EAClE;AAAA,EACA,aAAa,QAAQ,UAAU,UAAU;AACvC,WAAO,MAAM,aAAa,KAAK,iBAAiB,MAAM,GAAG,UAAU,QAAQ;AAAA,EAC7E;AAAA,EACA,YAAY,SAAS,UAAU;AAC7B,WAAO,MAAM,YAAY,MAAM,QAAQ;AAAA,EACzC;AAAA,EACA,WAAW,MAAM;AACf,WAAO,KAAK,iBAAiB,MAAM,WAAW,KAAK,iBAAiB,IAAI,CAAC,CAAC;AAAA,EAC5E;AAAA,EACA,UAAU;AACR,SAAK,iBAAiB,WAAW,KAAK,UAAU;AAAA,EAClD;AACF;AACA,IAAM,+BAAN,cAA2C,oBAAoB;AAAA,EAC7D;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,YAAY,cAAc,kBAAkB,WAAW,2BAA2B,KAAK,QAAQ,kBAAkB,gBAAgB,QAAQ;AACvI,UAAM,cAAc,KAAK,QAAQ,kBAAkB,cAAc;AACjE,SAAK,mBAAmB;AACxB,SAAK,4BAA4B;AACjC,QAAI,SAAS,UAAU;AACvB,QAAI,WAAW;AAEb,YAAM,WAAW,OAAQ,EAAE,YAAY,GAAG,KAAK;AAC/C,eAAS,0BAA0B,UAAU,MAAM;AAAA,IACrD;AACA,SAAK,SAAS,SAAS,kBAAkB,QAAQ,MAAM,IAAI;AAC3D,SAAK,YAAY,UAAU,oBAAoB,MAAM;AAAA,EACvD;AAAA,EACA,cAAc;AACZ,SAAK,iBAAiB,UAAU,KAAK,QAAQ,KAAK,SAAS;AAAA,EAC7D;AAAA,EACA,UAAU;AACR,QAAI,CAAC,KAAK,2BAA2B;AACnC;AAAA,IACF;AACA,SAAK,iBAAiB,aAAa,KAAK,QAAQ,KAAK,SAAS;AAAA,EAChE;AACF;AACA,IAAM,oCAAN,cAAgD,6BAA6B;AAAA,EAC3E;AAAA,EACA;AAAA,EACA,YAAY,cAAc,kBAAkB,WAAW,OAAO,2BAA2B,KAAK,QAAQ,kBAAkB,gBAAgB;AACtI,UAAM,SAAS,QAAQ,MAAM,UAAU;AACvC,UAAM,cAAc,kBAAkB,WAAW,2BAA2B,KAAK,QAAQ,kBAAkB,gBAAgB,MAAM;AACjI,SAAK,cAAc,qBAAqB,MAAM;AAC9C,SAAK,WAAW,kBAAkB,MAAM;AAAA,EAC1C;AAAA,EACA,YAAY,SAAS;AACnB,SAAK,YAAY;AACjB,SAAK,aAAa,SAAS,KAAK,UAAU,EAAE;AAAA,EAC9C;AAAA,EACA,cAAc,QAAQ,MAAM;AAC1B,UAAM,KAAK,MAAM,cAAc,QAAQ,IAAI;AAC3C,UAAM,aAAa,IAAI,KAAK,aAAa,EAAE;AAC3C,WAAO;AAAA,EACT;AACF;;;ACjzBA,IAAM,oBAAN,MAAM,2BAA0B,WAAY;AAAA,EAC1C,oBAAoB;AAAA,EACpB,OAAO,cAAc;AACnB,sBAAmB,IAAI,mBAAkB,CAAC;AAAA,EAC5C;AAAA,EACA,YAAY,IAAI,KAAK,UAAU,SAAS;AACtC,OAAG,iBAAiB,KAAK,UAAU,OAAO;AAC1C,WAAO,MAAM;AACX,SAAG,oBAAoB,KAAK,UAAU,OAAO;AAAA,IAC/C;AAAA,EACF;AAAA,EACA,cAAc,IAAI,KAAK;AACrB,OAAG,cAAc,GAAG;AAAA,EACtB;AAAA,EACA,OAAO,MAAM;AACX,SAAK,OAAO;AAAA,EACd;AAAA,EACA,cAAc,SAAS,KAAK;AAC1B,UAAM,OAAO,KAAK,mBAAmB;AACrC,WAAO,IAAI,cAAc,OAAO;AAAA,EAClC;AAAA,EACA,qBAAqB;AACnB,WAAO,SAAS,eAAe,mBAAmB,WAAW;AAAA,EAC/D;AAAA,EACA,qBAAqB;AACnB,WAAO;AAAA,EACT;AAAA,EACA,cAAc,MAAM;AAClB,WAAO,KAAK,aAAa,KAAK;AAAA,EAChC;AAAA,EACA,aAAa,MAAM;AACjB,WAAO,gBAAgB;AAAA,EACzB;AAAA;AAAA,EAEA,qBAAqB,KAAK,QAAQ;AAChC,QAAI,WAAW,UAAU;AACvB,aAAO;AAAA,IACT;AACA,QAAI,WAAW,YAAY;AACzB,aAAO;AAAA,IACT;AACA,QAAI,WAAW,QAAQ;AACrB,aAAO,IAAI;AAAA,IACb;AACA,WAAO;AAAA,EACT;AAAA,EACA,YAAY,KAAK;AACf,UAAM,OAAO,mBAAmB;AAChC,WAAO,QAAQ,OAAO,OAAO,aAAa,IAAI;AAAA,EAChD;AAAA,EACA,mBAAmB;AACjB,kBAAc;AAAA,EAChB;AAAA,EACA,eAAe;AACb,WAAO,OAAO,UAAU;AAAA,EAC1B;AAAA,EACA,UAAU,MAAM;AACd,WAAO,iBAAkB,SAAS,QAAQ,IAAI;AAAA,EAChD;AACF;AACA,IAAI,cAAc;AAClB,SAAS,qBAAqB;AAC5B,gBAAc,eAAe,SAAS,KAAK,cAAc,MAAM;AAC/D,SAAO,cAAc,YAAY,aAAa,MAAM,IAAI;AAC1D;AACA,SAAS,aAAa,KAAK;AAGzB,SAAO,IAAI,IAAI,KAAK,SAAS,OAAO,EAAE;AACxC;AACA,IAAM,wBAAN,MAA4B;AAAA,EAC1B,YAAY,UAAU;AACpB,YAAQ,uBAAuB,IAAI,CAAC,MAAM,kBAAkB,SAAS;AACnE,YAAM,cAAc,SAAS,sBAAsB,MAAM,eAAe;AACxE,UAAI,eAAe,MAAM;AACvB,cAAM,IAAI,aAAc,OAAoD,OAAO,cAAc,eAAe,cAAc,yCAAyC;AAAA,MACzK;AACA,aAAO;AAAA,IACT;AACA,YAAQ,4BAA4B,IAAI,MAAM,SAAS,oBAAoB;AAC3E,YAAQ,2BAA2B,IAAI,MAAM,SAAS,mBAAmB;AACzE,UAAM,gBAAgB,cAAY;AAChC,YAAM,gBAAgB,QAAQ,4BAA4B,EAAE;AAC5D,UAAI,QAAQ,cAAc;AAC1B,YAAM,YAAY,WAAY;AAC5B;AACA,YAAI,SAAS,GAAG;AACd,mBAAS;AAAA,QACX;AAAA,MACF;AACA,oBAAc,QAAQ,iBAAe;AACnC,oBAAY,WAAW,SAAS;AAAA,MAClC,CAAC;AAAA,IACH;AACA,QAAI,CAAC,QAAQ,sBAAsB,GAAG;AACpC,cAAQ,sBAAsB,IAAI,CAAC;AAAA,IACrC;AACA,YAAQ,sBAAsB,EAAE,KAAK,aAAa;AAAA,EACpD;AAAA,EACA,sBAAsB,UAAU,MAAM,iBAAiB;AACrD,QAAI,QAAQ,MAAM;AAChB,aAAO;AAAA,IACT;AACA,UAAM,IAAI,SAAS,eAAe,IAAI;AACtC,QAAI,KAAK,MAAM;AACb,aAAO;AAAA,IACT,WAAW,CAAC,iBAAiB;AAC3B,aAAO;AAAA,IACT;AACA,QAAI,OAAQ,EAAE,aAAa,IAAI,GAAG;AAChC,aAAO,KAAK,sBAAsB,UAAU,KAAK,MAAM,IAAI;AAAA,IAC7D;AACA,WAAO,KAAK,sBAAsB,UAAU,KAAK,eAAe,IAAI;AAAA,EACtE;AACF;AAKA,IAAM,aAAN,MAAM,YAAW;AAAA,EACf,QAAQ;AACN,WAAO,IAAI,eAAe;AAAA,EAC5B;AAAA,EACA,OAAO,OAAO,SAAS,mBAAmB,mBAAmB;AAC3D,WAAO,KAAK,qBAAqB,aAAY;AAAA,EAC/C;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,YAAW;AAAA,EACtB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,YAAY,CAAC;AAAA,IACnF,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,kBAAN,MAAM,yBAAwB,mBAAmB;AAAA,EAC/C,YAAY,KAAK;AACf,UAAM,GAAG;AAAA,EACX;AAAA;AAAA;AAAA,EAGA,SAAS,WAAW;AAClB,WAAO;AAAA,EACT;AAAA,EACA,iBAAiB,SAAS,WAAW,SAAS,SAAS;AACrD,YAAQ,iBAAiB,WAAW,SAAS,OAAO;AACpD,WAAO,MAAM,KAAK,oBAAoB,SAAS,WAAW,SAAS,OAAO;AAAA,EAC5E;AAAA,EACA,oBAAoB,QAAQ,WAAW,UAAU,SAAS;AACxD,WAAO,OAAO,oBAAoB,WAAW,UAAU,OAAO;AAAA,EAChE;AAAA,EACA,OAAO,OAAO,SAAS,wBAAwB,mBAAmB;AAChE,WAAO,KAAK,qBAAqB,kBAAoB,SAAS,QAAQ,CAAC;AAAA,EACzE;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,iBAAgB;AAAA,EAC3B,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,QAAQ;AAAA,IACjB,CAAC;AAAA,EACH,CAAC,GAAG,IAAI;AACV,GAAG;AAKH,IAAM,gBAAgB,CAAC,OAAO,WAAW,QAAQ,OAAO;AAGxD,IAAM,UAAU;AAAA,EACd,MAAM;AAAA,EACN,KAAM;AAAA,EACN,KAAQ;AAAA,EACR,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,UAAU;AAAA,EACV,OAAO;AACT;AAIA,IAAM,uBAAuB;AAAA,EAC3B,OAAO,WAAS,MAAM;AAAA,EACtB,WAAW,WAAS,MAAM;AAAA,EAC1B,QAAQ,WAAS,MAAM;AAAA,EACvB,SAAS,WAAS,MAAM;AAC1B;AAIA,IAAM,kBAAN,MAAM,yBAAwB,mBAAmB;AAAA;AAAA;AAAA;AAAA;AAAA,EAK/C,YAAY,KAAK;AACf,UAAM,GAAG;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,SAAS,WAAW;AAClB,WAAO,iBAAgB,eAAe,SAAS,KAAK;AAAA,EACtD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,iBAAiB,SAAS,WAAW,SAAS,SAAS;AACrD,UAAM,cAAc,iBAAgB,eAAe,SAAS;AAC5D,UAAM,iBAAiB,iBAAgB,cAAc,YAAY,SAAS,GAAG,SAAS,KAAK,QAAQ,QAAQ,CAAC;AAC5G,WAAO,KAAK,QAAQ,QAAQ,EAAE,kBAAkB,MAAM;AACpD,aAAO,OAAQ,EAAE,YAAY,SAAS,YAAY,cAAc,GAAG,gBAAgB,OAAO;AAAA,IAC5F,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,OAAO,eAAe,WAAW;AAC/B,UAAM,QAAQ,UAAU,YAAY,EAAE,MAAM,GAAG;AAC/C,UAAM,eAAe,MAAM,MAAM;AACjC,QAAI,MAAM,WAAW,KAAK,EAAE,iBAAiB,aAAa,iBAAiB,UAAU;AACnF,aAAO;AAAA,IACT;AACA,UAAM,MAAM,iBAAgB,cAAc,MAAM,IAAI,CAAC;AACrD,QAAI,UAAU;AACd,QAAI,SAAS,MAAM,QAAQ,MAAM;AACjC,QAAI,SAAS,IAAI;AACf,YAAM,OAAO,QAAQ,CAAC;AACtB,gBAAU;AAAA,IACZ;AACA,kBAAc,QAAQ,kBAAgB;AACpC,YAAM,QAAQ,MAAM,QAAQ,YAAY;AACxC,UAAI,QAAQ,IAAI;AACd,cAAM,OAAO,OAAO,CAAC;AACrB,mBAAW,eAAe;AAAA,MAC5B;AAAA,IACF,CAAC;AACD,eAAW;AACX,QAAI,MAAM,UAAU,KAAK,IAAI,WAAW,GAAG;AAEzC,aAAO;AAAA,IACT;AAIA,UAAM,SAAS,CAAC;AAChB,WAAO,cAAc,IAAI;AACzB,WAAO,SAAS,IAAI;AACpB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,OAAO,sBAAsB,OAAO,aAAa;AAC/C,QAAI,UAAU,QAAQ,MAAM,GAAG,KAAK,MAAM;AAC1C,QAAI,MAAM;AACV,QAAI,YAAY,QAAQ,OAAO,IAAI,IAAI;AACrC,gBAAU,MAAM;AAChB,YAAM;AAAA,IACR;AAEA,QAAI,WAAW,QAAQ,CAAC,QAAS,QAAO;AACxC,cAAU,QAAQ,YAAY;AAC9B,QAAI,YAAY,KAAK;AACnB,gBAAU;AAAA,IACZ,WAAW,YAAY,KAAK;AAC1B,gBAAU;AAAA,IACZ;AACA,kBAAc,QAAQ,kBAAgB;AACpC,UAAI,iBAAiB,SAAS;AAC5B,cAAM,iBAAiB,qBAAqB,YAAY;AACxD,YAAI,eAAe,KAAK,GAAG;AACzB,iBAAO,eAAe;AAAA,QACxB;AAAA,MACF;AAAA,IACF,CAAC;AACD,WAAO;AACP,WAAO,QAAQ;AAAA,EACjB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,OAAO,cAAc,SAAS,SAAS,MAAM;AAC3C,WAAO,WAAS;AACd,UAAI,iBAAgB,sBAAsB,OAAO,OAAO,GAAG;AACzD,aAAK,WAAW,MAAM,QAAQ,KAAK,CAAC;AAAA,MACtC;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAEA,OAAO,cAAc,SAAS;AAC5B,WAAO,YAAY,QAAQ,WAAW;AAAA,EACxC;AAAA,EACA,OAAO,OAAO,SAAS,wBAAwB,mBAAmB;AAChE,WAAO,KAAK,qBAAqB,kBAAoB,SAAS,QAAQ,CAAC;AAAA,EACzE;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,iBAAgB;AAAA,EAC3B,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,QAAQ;AAAA,IACjB,CAAC;AAAA,EACH,CAAC,GAAG,IAAI;AACV,GAAG;AA6DH,SAAS,qBAAqB,eAAe,SAAS;AACpD,SAAO,0BAA2B;AAAA,IAChC;AAAA,KACG,sBAAsB,OAAO,EACjC;AACH;AAaA,SAAS,kBAAkB,SAAS;AAClC,SAAO,0BAA2B,sBAAsB,OAAO,CAAC;AAClE;AACA,SAAS,sBAAsB,SAAS;AACtC,SAAO;AAAA,IACL,cAAc,CAAC,GAAG,0BAA0B,GAAI,SAAS,aAAa,CAAC,CAAE;AAAA,IACzE,mBAAmB;AAAA,EACrB;AACF;AAYA,SAAS,kCAAkC;AAIzC,SAAO,CAAC,GAAG,qBAAqB;AAClC;AACA,SAAS,iBAAiB;AACxB,oBAAkB,YAAY;AAChC;AACA,SAAS,eAAe;AACtB,SAAO,IAAI,aAAa;AAC1B;AACA,SAAS,YAAY;AAEnB,cAAa,QAAQ;AACrB,SAAO;AACT;AACA,IAAM,sCAAsC,CAAC;AAAA,EAC3C,SAAS;AAAA,EACT,UAAU;AACZ,GAAG;AAAA,EACD,SAAS;AAAA,EACT,UAAU;AAAA,EACV,OAAO;AACT,GAAG;AAAA,EACD,SAAS;AAAA,EACT,YAAY;AACd,CAAC;AAOD,IAAM,kBAAkB,sBAAsB,cAAc,WAAW,mCAAmC;AAO1G,IAAM,kCAAkC,IAAI,eAAe,OAAO,cAAc,eAAe,YAAY,mCAAmC,EAAE;AAChJ,IAAM,wBAAwB,CAAC;AAAA,EAC7B,SAAS;AAAA,EACT,UAAU;AACZ,GAAG;AAAA,EACD,SAAS;AAAA,EACT,UAAU;AAAA,EACV,MAAM,CAAC,QAAQ,qBAAqB,kBAAmB;AACzD,GAAG;AAAA,EACD,SAAS;AAAA;AAAA,EAET,UAAU;AAAA,EACV,MAAM,CAAC,QAAQ,qBAAqB,kBAAmB;AACzD,CAAC;AACD,IAAM,2BAA2B,CAAC;AAAA,EAChC,SAAS;AAAA,EACT,UAAU;AACZ,GAAG;AAAA,EACD,SAAS;AAAA,EACT,YAAY;AACd,GAAG;AAAA,EACD,SAAS;AAAA,EACT,UAAU;AAAA,EACV,OAAO;AAAA,EACP,MAAM,CAAC,QAAQ;AACjB,GAAG;AAAA,EACD,SAAS;AAAA,EACT,UAAU;AAAA,EACV,OAAO;AAAA,EACP,MAAM,CAAC,QAAQ;AACjB,GAAG,qBAAqB,kBAAkB,cAAc;AAAA,EACtD,SAAS;AAAA,EACT,aAAa;AACf,GAAG;AAAA,EACD,SAAS;AAAA,EACT,UAAU;AACZ,GAAG,OAAO,cAAc,eAAe,YAAY;AAAA,EACjD,SAAS;AAAA,EACT,UAAU;AACZ,IAAI,CAAC,CAAC;AAUN,IAAM,gBAAN,MAAM,eAAc;AAAA,EAClB,cAAc;AACZ,QAAI,OAAO,cAAc,eAAe,WAAW;AACjD,YAAM,0BAA0B,OAAO,iCAAiC;AAAA,QACtE,UAAU;AAAA,QACV,UAAU;AAAA,MACZ,CAAC;AACD,UAAI,yBAAyB;AAC3B,cAAM,IAAI,aAAc,MAA2D,qKAA0K;AAAA,MAC/P;AAAA,IACF;AAAA,EACF;AAAA,EACA,OAAO,OAAO,SAAS,sBAAsB,mBAAmB;AAC9D,WAAO,KAAK,qBAAqB,gBAAe;AAAA,EAClD;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,SAAS,CAAC,cAAc,iBAAiB;AAAA,EAC3C,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,WAAW,CAAC,GAAG,0BAA0B,GAAG,qBAAqB;AAAA,IACjE,SAAS,CAAC,cAAc,iBAAiB;AAAA,EAC3C,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,WAAW,CAAC,GAAG,0BAA0B,GAAG,qBAAqB;AAAA,MACjE,SAAS,CAAC,cAAc,iBAAiB;AAAA,IAC3C,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;;;ACpjBH,IAAM,cAAN,MAAkB;AAAC;AAWnB,IAAM,cAAN,MAAkB;AAAC;AASnB,IAAM,cAAN,MAAM,aAAY;AAAA;AAAA;AAAA;AAAA,EAIhB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,kBAAkB,oBAAI,IAAI;AAAA;AAAA;AAAA;AAAA,EAI1B;AAAA;AAAA;AAAA;AAAA,EAIA,aAAa;AAAA;AAAA,EAEb,YAAY,SAAS;AACnB,QAAI,CAAC,SAAS;AACZ,WAAK,UAAU,oBAAI,IAAI;AAAA,IACzB,WAAW,OAAO,YAAY,UAAU;AACtC,WAAK,WAAW,MAAM;AACpB,aAAK,UAAU,oBAAI,IAAI;AACvB,gBAAQ,MAAM,IAAI,EAAE,QAAQ,UAAQ;AAClC,gBAAM,QAAQ,KAAK,QAAQ,GAAG;AAC9B,cAAI,QAAQ,GAAG;AACb,kBAAM,OAAO,KAAK,MAAM,GAAG,KAAK;AAChC,kBAAM,QAAQ,KAAK,MAAM,QAAQ,CAAC,EAAE,KAAK;AACzC,iBAAK,eAAe,MAAM,KAAK;AAAA,UACjC;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF,WAAW,OAAO,YAAY,eAAe,mBAAmB,SAAS;AACvE,WAAK,UAAU,oBAAI,IAAI;AACvB,cAAQ,QAAQ,CAAC,OAAO,SAAS;AAC/B,aAAK,eAAe,MAAM,KAAK;AAAA,MACjC,CAAC;AAAA,IACH,OAAO;AACL,WAAK,WAAW,MAAM;AACpB,YAAI,OAAO,cAAc,eAAe,WAAW;AACjD,6BAAmB,OAAO;AAAA,QAC5B;AACA,aAAK,UAAU,oBAAI,IAAI;AACvB,eAAO,QAAQ,OAAO,EAAE,QAAQ,CAAC,CAAC,MAAM,MAAM,MAAM;AAClD,eAAK,iBAAiB,MAAM,MAAM;AAAA,QACpC,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,IAAI,MAAM;AACR,SAAK,KAAK;AACV,WAAO,KAAK,QAAQ,IAAI,KAAK,YAAY,CAAC;AAAA,EAC5C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,IAAI,MAAM;AACR,SAAK,KAAK;AACV,UAAM,SAAS,KAAK,QAAQ,IAAI,KAAK,YAAY,CAAC;AAClD,WAAO,UAAU,OAAO,SAAS,IAAI,OAAO,CAAC,IAAI;AAAA,EACnD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,OAAO;AACL,SAAK,KAAK;AACV,WAAO,MAAM,KAAK,KAAK,gBAAgB,OAAO,CAAC;AAAA,EACjD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,OAAO,MAAM;AACX,SAAK,KAAK;AACV,WAAO,KAAK,QAAQ,IAAI,KAAK,YAAY,CAAC,KAAK;AAAA,EACjD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,OAAO,MAAM,OAAO;AAClB,WAAO,KAAK,MAAM;AAAA,MAChB;AAAA,MACA;AAAA,MACA,IAAI;AAAA,IACN,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,IAAI,MAAM,OAAO;AACf,WAAO,KAAK,MAAM;AAAA,MAChB;AAAA,MACA;AAAA,MACA,IAAI;AAAA,IACN,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,OAAO,MAAM,OAAO;AAClB,WAAO,KAAK,MAAM;AAAA,MAChB;AAAA,MACA;AAAA,MACA,IAAI;AAAA,IACN,CAAC;AAAA,EACH;AAAA,EACA,uBAAuB,MAAM,QAAQ;AACnC,QAAI,CAAC,KAAK,gBAAgB,IAAI,MAAM,GAAG;AACrC,WAAK,gBAAgB,IAAI,QAAQ,IAAI;AAAA,IACvC;AAAA,EACF;AAAA,EACA,OAAO;AACL,QAAI,CAAC,CAAC,KAAK,UAAU;AACnB,UAAI,KAAK,oBAAoB,cAAa;AACxC,aAAK,SAAS,KAAK,QAAQ;AAAA,MAC7B,OAAO;AACL,aAAK,SAAS;AAAA,MAChB;AACA,WAAK,WAAW;AAChB,UAAI,CAAC,CAAC,KAAK,YAAY;AACrB,aAAK,WAAW,QAAQ,YAAU,KAAK,YAAY,MAAM,CAAC;AAC1D,aAAK,aAAa;AAAA,MACpB;AAAA,IACF;AAAA,EACF;AAAA,EACA,SAAS,OAAO;AACd,UAAM,KAAK;AACX,UAAM,KAAK,MAAM,QAAQ,KAAK,CAAC,EAAE,QAAQ,SAAO;AAC9C,WAAK,QAAQ,IAAI,KAAK,MAAM,QAAQ,IAAI,GAAG,CAAC;AAC5C,WAAK,gBAAgB,IAAI,KAAK,MAAM,gBAAgB,IAAI,GAAG,CAAC;AAAA,IAC9D,CAAC;AAAA,EACH;AAAA,EACA,MAAM,QAAQ;AACZ,UAAM,QAAQ,IAAI,aAAY;AAC9B,UAAM,WAAW,CAAC,CAAC,KAAK,YAAY,KAAK,oBAAoB,eAAc,KAAK,WAAW;AAC3F,UAAM,cAAc,KAAK,cAAc,CAAC,GAAG,OAAO,CAAC,MAAM,CAAC;AAC1D,WAAO;AAAA,EACT;AAAA,EACA,YAAY,QAAQ;AAClB,UAAM,MAAM,OAAO,KAAK,YAAY;AACpC,YAAQ,OAAO,IAAI;AAAA,MACjB,KAAK;AAAA,MACL,KAAK;AACH,YAAI,QAAQ,OAAO;AACnB,YAAI,OAAO,UAAU,UAAU;AAC7B,kBAAQ,CAAC,KAAK;AAAA,QAChB;AACA,YAAI,MAAM,WAAW,GAAG;AACtB;AAAA,QACF;AACA,aAAK,uBAAuB,OAAO,MAAM,GAAG;AAC5C,cAAM,QAAQ,OAAO,OAAO,MAAM,KAAK,QAAQ,IAAI,GAAG,IAAI,WAAc,CAAC;AACzE,aAAK,KAAK,GAAG,KAAK;AAClB,aAAK,QAAQ,IAAI,KAAK,IAAI;AAC1B;AAAA,MACF,KAAK;AACH,cAAM,WAAW,OAAO;AACxB,YAAI,CAAC,UAAU;AACb,eAAK,QAAQ,OAAO,GAAG;AACvB,eAAK,gBAAgB,OAAO,GAAG;AAAA,QACjC,OAAO;AACL,cAAI,WAAW,KAAK,QAAQ,IAAI,GAAG;AACnC,cAAI,CAAC,UAAU;AACb;AAAA,UACF;AACA,qBAAW,SAAS,OAAO,CAAAC,WAAS,SAAS,QAAQA,MAAK,MAAM,EAAE;AAClE,cAAI,SAAS,WAAW,GAAG;AACzB,iBAAK,QAAQ,OAAO,GAAG;AACvB,iBAAK,gBAAgB,OAAO,GAAG;AAAA,UACjC,OAAO;AACL,iBAAK,QAAQ,IAAI,KAAK,QAAQ;AAAA,UAChC;AAAA,QACF;AACA;AAAA,IACJ;AAAA,EACF;AAAA,EACA,eAAe,MAAM,OAAO;AAC1B,UAAM,MAAM,KAAK,YAAY;AAC7B,SAAK,uBAAuB,MAAM,GAAG;AACrC,QAAI,KAAK,QAAQ,IAAI,GAAG,GAAG;AACzB,WAAK,QAAQ,IAAI,GAAG,EAAE,KAAK,KAAK;AAAA,IAClC,OAAO;AACL,WAAK,QAAQ,IAAI,KAAK,CAAC,KAAK,CAAC;AAAA,IAC/B;AAAA,EACF;AAAA,EACA,iBAAiB,MAAM,QAAQ;AAC7B,UAAM,gBAAgB,MAAM,QAAQ,MAAM,IAAI,SAAS,CAAC,MAAM,GAAG,IAAI,WAAS,MAAM,SAAS,CAAC;AAC9F,UAAM,MAAM,KAAK,YAAY;AAC7B,SAAK,QAAQ,IAAI,KAAK,YAAY;AAClC,SAAK,uBAAuB,MAAM,GAAG;AAAA,EACvC;AAAA;AAAA;AAAA;AAAA,EAIA,QAAQ,IAAI;AACV,SAAK,KAAK;AACV,UAAM,KAAK,KAAK,gBAAgB,KAAK,CAAC,EAAE,QAAQ,SAAO,GAAG,KAAK,gBAAgB,IAAI,GAAG,GAAG,KAAK,QAAQ,IAAI,GAAG,CAAC,CAAC;AAAA,EACjH;AACF;AAMA,SAAS,mBAAmB,SAAS;AACnC,aAAW,CAAC,KAAK,KAAK,KAAK,OAAO,QAAQ,OAAO,GAAG;AAClD,QAAI,EAAE,OAAO,UAAU,YAAY,OAAO,UAAU,aAAa,CAAC,MAAM,QAAQ,KAAK,GAAG;AACtF,YAAM,IAAI,MAAM,6BAA6B,GAAG,mFAAwF,KAAK,KAAK;AAAA,IACpJ;AAAA,EACF;AACF;AAYA,IAAM,uBAAN,MAA2B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMzB,UAAU,KAAK;AACb,WAAO,iBAAiB,GAAG;AAAA,EAC7B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,YAAY,OAAO;AACjB,WAAO,iBAAiB,KAAK;AAAA,EAC/B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,UAAU,KAAK;AACb,WAAO,mBAAmB,GAAG;AAAA,EAC/B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,YAAY,OAAO;AACjB,WAAO,mBAAmB,KAAK;AAAA,EACjC;AACF;AACA,SAAS,YAAY,WAAW,OAAO;AACrC,QAAMC,OAAM,oBAAI,IAAI;AACpB,MAAI,UAAU,SAAS,GAAG;AAIxB,UAAM,SAAS,UAAU,QAAQ,OAAO,EAAE,EAAE,MAAM,GAAG;AACrD,WAAO,QAAQ,WAAS;AACtB,YAAM,QAAQ,MAAM,QAAQ,GAAG;AAC/B,YAAM,CAAC,KAAK,GAAG,IAAI,SAAS,KAAK,CAAC,MAAM,UAAU,KAAK,GAAG,EAAE,IAAI,CAAC,MAAM,UAAU,MAAM,MAAM,GAAG,KAAK,CAAC,GAAG,MAAM,YAAY,MAAM,MAAM,QAAQ,CAAC,CAAC,CAAC;AAClJ,YAAM,OAAOA,KAAI,IAAI,GAAG,KAAK,CAAC;AAC9B,WAAK,KAAK,GAAG;AACb,MAAAA,KAAI,IAAI,KAAK,IAAI;AAAA,IACnB,CAAC;AAAA,EACH;AACA,SAAOA;AACT;AAIA,IAAM,0BAA0B;AAChC,IAAM,iCAAiC;AAAA,EACrC,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AACR;AACA,SAAS,iBAAiB,GAAG;AAC3B,SAAO,mBAAmB,CAAC,EAAE,QAAQ,yBAAyB,CAAC,GAAG,MAAM,+BAA+B,CAAC,KAAK,CAAC;AAChH;AACA,SAAS,cAAc,OAAO;AAC5B,SAAO,GAAG,KAAK;AACjB;AASA,IAAM,aAAN,MAAM,YAAW;AAAA,EACf;AAAA,EACA;AAAA,EACA,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,YAAY,UAAU,CAAC,GAAG;AACxB,SAAK,UAAU,QAAQ,WAAW,IAAI,qBAAqB;AAC3D,QAAI,QAAQ,YAAY;AACtB,UAAI,QAAQ,YAAY;AACtB,cAAM,IAAI,aAAc,MAA6E,aAAa,gDAAgD;AAAA,MACpK;AACA,WAAK,MAAM,YAAY,QAAQ,YAAY,KAAK,OAAO;AAAA,IACzD,WAAW,CAAC,CAAC,QAAQ,YAAY;AAC/B,WAAK,MAAM,oBAAI,IAAI;AACnB,aAAO,KAAK,QAAQ,UAAU,EAAE,QAAQ,SAAO;AAC7C,cAAM,QAAQ,QAAQ,WAAW,GAAG;AAEpC,cAAM,SAAS,MAAM,QAAQ,KAAK,IAAI,MAAM,IAAI,aAAa,IAAI,CAAC,cAAc,KAAK,CAAC;AACtF,aAAK,IAAI,IAAI,KAAK,MAAM;AAAA,MAC1B,CAAC;AAAA,IACH,OAAO;AACL,WAAK,MAAM;AAAA,IACb;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,IAAI,OAAO;AACT,SAAK,KAAK;AACV,WAAO,KAAK,IAAI,IAAI,KAAK;AAAA,EAC3B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,IAAI,OAAO;AACT,SAAK,KAAK;AACV,UAAM,MAAM,KAAK,IAAI,IAAI,KAAK;AAC9B,WAAO,CAAC,CAAC,MAAM,IAAI,CAAC,IAAI;AAAA,EAC1B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,OAAO,OAAO;AACZ,SAAK,KAAK;AACV,WAAO,KAAK,IAAI,IAAI,KAAK,KAAK;AAAA,EAChC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO;AACL,SAAK,KAAK;AACV,WAAO,MAAM,KAAK,KAAK,IAAI,KAAK,CAAC;AAAA,EACnC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,OAAO,OAAO,OAAO;AACnB,WAAO,KAAK,MAAM;AAAA,MAChB;AAAA,MACA;AAAA,MACA,IAAI;AAAA,IACN,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,UAAU,QAAQ;AAChB,UAAM,UAAU,CAAC;AACjB,WAAO,KAAK,MAAM,EAAE,QAAQ,WAAS;AACnC,YAAM,QAAQ,OAAO,KAAK;AAC1B,UAAI,MAAM,QAAQ,KAAK,GAAG;AACxB,cAAM,QAAQ,YAAU;AACtB,kBAAQ,KAAK;AAAA,YACX;AAAA,YACA,OAAO;AAAA,YACP,IAAI;AAAA,UACN,CAAC;AAAA,QACH,CAAC;AAAA,MACH,OAAO;AACL,gBAAQ,KAAK;AAAA,UACX;AAAA,UACA;AAAA,UACA,IAAI;AAAA,QACN,CAAC;AAAA,MACH;AAAA,IACF,CAAC;AACD,WAAO,KAAK,MAAM,OAAO;AAAA,EAC3B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,IAAI,OAAO,OAAO;AAChB,WAAO,KAAK,MAAM;AAAA,MAChB;AAAA,MACA;AAAA,MACA,IAAI;AAAA,IACN,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,OAAO,OAAO,OAAO;AACnB,WAAO,KAAK,MAAM;AAAA,MAChB;AAAA,MACA;AAAA,MACA,IAAI;AAAA,IACN,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW;AACT,SAAK,KAAK;AACV,WAAO,KAAK,KAAK,EAAE,IAAI,SAAO;AAC5B,YAAM,OAAO,KAAK,QAAQ,UAAU,GAAG;AAIvC,aAAO,KAAK,IAAI,IAAI,GAAG,EAAE,IAAI,WAAS,OAAO,MAAM,KAAK,QAAQ,YAAY,KAAK,CAAC,EAAE,KAAK,GAAG;AAAA,IAC9F,CAAC,EAGA,OAAO,WAAS,UAAU,EAAE,EAAE,KAAK,GAAG;AAAA,EACzC;AAAA,EACA,MAAM,QAAQ;AACZ,UAAM,QAAQ,IAAI,YAAW;AAAA,MAC3B,SAAS,KAAK;AAAA,IAChB,CAAC;AACD,UAAM,YAAY,KAAK,aAAa;AACpC,UAAM,WAAW,KAAK,WAAW,CAAC,GAAG,OAAO,MAAM;AAClD,WAAO;AAAA,EACT;AAAA,EACA,OAAO;AACL,QAAI,KAAK,QAAQ,MAAM;AACrB,WAAK,MAAM,oBAAI,IAAI;AAAA,IACrB;AACA,QAAI,KAAK,cAAc,MAAM;AAC3B,WAAK,UAAU,KAAK;AACpB,WAAK,UAAU,KAAK,EAAE,QAAQ,SAAO,KAAK,IAAI,IAAI,KAAK,KAAK,UAAU,IAAI,IAAI,GAAG,CAAC,CAAC;AACnF,WAAK,QAAQ,QAAQ,YAAU;AAC7B,gBAAQ,OAAO,IAAI;AAAA,UACjB,KAAK;AAAA,UACL,KAAK;AACH,kBAAM,QAAQ,OAAO,OAAO,MAAM,KAAK,IAAI,IAAI,OAAO,KAAK,IAAI,WAAc,CAAC;AAC9E,iBAAK,KAAK,cAAc,OAAO,KAAK,CAAC;AACrC,iBAAK,IAAI,IAAI,OAAO,OAAO,IAAI;AAC/B;AAAA,UACF,KAAK;AACH,gBAAI,OAAO,UAAU,QAAW;AAC9B,kBAAIC,QAAO,KAAK,IAAI,IAAI,OAAO,KAAK,KAAK,CAAC;AAC1C,oBAAM,MAAMA,MAAK,QAAQ,cAAc,OAAO,KAAK,CAAC;AACpD,kBAAI,QAAQ,IAAI;AACd,gBAAAA,MAAK,OAAO,KAAK,CAAC;AAAA,cACpB;AACA,kBAAIA,MAAK,SAAS,GAAG;AACnB,qBAAK,IAAI,IAAI,OAAO,OAAOA,KAAI;AAAA,cACjC,OAAO;AACL,qBAAK,IAAI,OAAO,OAAO,KAAK;AAAA,cAC9B;AAAA,YACF,OAAO;AACL,mBAAK,IAAI,OAAO,OAAO,KAAK;AAC5B;AAAA,YACF;AAAA,QACJ;AAAA,MACF,CAAC;AACD,WAAK,YAAY,KAAK,UAAU;AAAA,IAClC;AAAA,EACF;AACF;AA8CA,IAAM,cAAN,MAAkB;AAAA,EAChB,MAAM,oBAAI,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASd,IAAI,OAAO,OAAO;AAChB,SAAK,IAAI,IAAI,OAAO,KAAK;AACzB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,IAAI,OAAO;AACT,QAAI,CAAC,KAAK,IAAI,IAAI,KAAK,GAAG;AACxB,WAAK,IAAI,IAAI,OAAO,MAAM,aAAa,CAAC;AAAA,IAC1C;AACA,WAAO,KAAK,IAAI,IAAI,KAAK;AAAA,EAC3B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,OAAO,OAAO;AACZ,SAAK,IAAI,OAAO,KAAK;AACrB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,IAAI,OAAO;AACT,WAAO,KAAK,IAAI,IAAI,KAAK;AAAA,EAC3B;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO;AACL,WAAO,KAAK,IAAI,KAAK;AAAA,EACvB;AACF;AAKA,SAAS,cAAc,QAAQ;AAC7B,UAAQ,QAAQ;AAAA,IACd,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACH,aAAO;AAAA,IACT;AACE,aAAO;AAAA,EACX;AACF;AAMA,SAAS,cAAc,OAAO;AAC5B,SAAO,OAAO,gBAAgB,eAAe,iBAAiB;AAChE;AAMA,SAAS,OAAO,OAAO;AACrB,SAAO,OAAO,SAAS,eAAe,iBAAiB;AACzD;AAMA,SAAS,WAAW,OAAO;AACzB,SAAO,OAAO,aAAa,eAAe,iBAAiB;AAC7D;AAMA,SAAS,kBAAkB,OAAO;AAChC,SAAO,OAAO,oBAAoB,eAAe,iBAAiB;AACpE;AAMA,IAAM,sBAAsB;AAK5B,IAAM,gBAAgB;AAMtB,IAAM,uBAAuB;AAM7B,IAAM,oBAAoB;AAK1B,IAAM,oBAAoB;AAO1B,IAAM,sBAAsB,GAAG,iBAAiB,KAAK,iBAAiB;AAWtE,IAAM,cAAN,MAAM,aAAY;AAAA,EAChB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,OAAO;AAAA;AAAA;AAAA;AAAA,EAIP;AAAA;AAAA;AAAA;AAAA,EAIA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,iBAAiB;AAAA;AAAA;AAAA;AAAA,EAIjB,kBAAkB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKlB;AAAA;AAAA;AAAA;AAAA,EAIA,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA,EAKZ;AAAA;AAAA;AAAA;AAAA,EAIA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,eAAe;AAAA;AAAA;AAAA;AAAA,EAIf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA;AAAA;AAAA;AAAA;AAAA,EAIA;AAAA;AAAA;AAAA;AAAA,EAIA;AAAA;AAAA;AAAA;AAAA,EAIA;AAAA,EACA,YAAY,QAAQ,KAAK,OAAO,QAAQ;AACtC,SAAK,MAAM;AACX,SAAK,SAAS,OAAO,YAAY;AAGjC,QAAI;AAGJ,QAAI,cAAc,KAAK,MAAM,KAAK,CAAC,CAAC,QAAQ;AAE1C,WAAK,OAAO,UAAU,SAAY,QAAQ;AAC1C,gBAAU;AAAA,IACZ,OAAO;AAEL,gBAAU;AAAA,IACZ;AAEA,QAAI,SAAS;AAEX,WAAK,iBAAiB,CAAC,CAAC,QAAQ;AAChC,WAAK,kBAAkB,CAAC,CAAC,QAAQ;AACjC,WAAK,YAAY,CAAC,CAAC,QAAQ;AAE3B,UAAI,CAAC,CAAC,QAAQ,cAAc;AAC1B,aAAK,eAAe,QAAQ;AAAA,MAC9B;AAEA,UAAI,QAAQ,SAAS;AACnB,aAAK,UAAU,QAAQ;AAAA,MACzB;AACA,UAAI,QAAQ,SAAS;AACnB,aAAK,UAAU,QAAQ;AAAA,MACzB;AACA,UAAI,QAAQ,QAAQ;AAClB,aAAK,SAAS,QAAQ;AAAA,MACxB;AACA,UAAI,QAAQ,UAAU;AACpB,aAAK,WAAW,QAAQ;AAAA,MAC1B;AACA,UAAI,QAAQ,OAAO;AACjB,aAAK,QAAQ,QAAQ;AAAA,MACvB;AACA,UAAI,QAAQ,aAAa;AACvB,aAAK,cAAc,QAAQ;AAAA,MAC7B;AACA,UAAI,OAAO,QAAQ,YAAY,UAAU;AAEvC,YAAI,QAAQ,UAAU,KAAK,CAAC,OAAO,UAAU,QAAQ,OAAO,GAAG;AAE7D,gBAAM,IAAI,MAAM,YAAY,+CAA+C,EAAE;AAAA,QAC/E;AACA,aAAK,UAAU,QAAQ;AAAA,MACzB;AACA,UAAI,QAAQ,MAAM;AAChB,aAAK,OAAO,QAAQ;AAAA,MACtB;AACA,UAAI,QAAQ,UAAU;AACpB,aAAK,WAAW,QAAQ;AAAA,MAC1B;AAEA,WAAK,gBAAgB,QAAQ;AAAA,IAC/B;AAEA,SAAK,YAAY,IAAI,YAAY;AAEjC,SAAK,YAAY,IAAI,YAAY;AAEjC,QAAI,CAAC,KAAK,QAAQ;AAChB,WAAK,SAAS,IAAI,WAAW;AAC7B,WAAK,gBAAgB;AAAA,IACvB,OAAO;AAEL,YAAM,SAAS,KAAK,OAAO,SAAS;AACpC,UAAI,OAAO,WAAW,GAAG;AAEvB,aAAK,gBAAgB;AAAA,MACvB,OAAO;AAEL,cAAM,OAAO,IAAI,QAAQ,GAAG;AAQ5B,cAAM,MAAM,SAAS,KAAK,MAAM,OAAO,IAAI,SAAS,IAAI,MAAM;AAC9D,aAAK,gBAAgB,MAAM,MAAM;AAAA,MACnC;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,gBAAgB;AAEd,QAAI,KAAK,SAAS,MAAM;AACtB,aAAO;AAAA,IACT;AAGA,QAAI,OAAO,KAAK,SAAS,YAAY,cAAc,KAAK,IAAI,KAAK,OAAO,KAAK,IAAI,KAAK,WAAW,KAAK,IAAI,KAAK,kBAAkB,KAAK,IAAI,GAAG;AAC3I,aAAO,KAAK;AAAA,IACd;AAEA,QAAI,KAAK,gBAAgB,YAAY;AACnC,aAAO,KAAK,KAAK,SAAS;AAAA,IAC5B;AAEA,QAAI,OAAO,KAAK,SAAS,YAAY,OAAO,KAAK,SAAS,aAAa,MAAM,QAAQ,KAAK,IAAI,GAAG;AAC/F,aAAO,KAAK,UAAU,KAAK,IAAI;AAAA,IACjC;AAEA,WAAO,KAAK,KAAK,SAAS;AAAA,EAC5B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,0BAA0B;AAExB,QAAI,KAAK,SAAS,MAAM;AACtB,aAAO;AAAA,IACT;AAEA,QAAI,WAAW,KAAK,IAAI,GAAG;AACzB,aAAO;AAAA,IACT;AAGA,QAAI,OAAO,KAAK,IAAI,GAAG;AACrB,aAAO,KAAK,KAAK,QAAQ;AAAA,IAC3B;AAEA,QAAI,cAAc,KAAK,IAAI,GAAG;AAC5B,aAAO;AAAA,IACT;AAGA,QAAI,OAAO,KAAK,SAAS,UAAU;AACjC,aAAO;AAAA,IACT;AAEA,QAAI,KAAK,gBAAgB,YAAY;AACnC,aAAO;AAAA,IACT;AAEA,QAAI,OAAO,KAAK,SAAS,YAAY,OAAO,KAAK,SAAS,YAAY,OAAO,KAAK,SAAS,WAAW;AACpG,aAAO;AAAA,IACT;AAEA,WAAO;AAAA,EACT;AAAA,EACA,MAAM,SAAS,CAAC,GAAG;AAGjB,UAAM,SAAS,OAAO,UAAU,KAAK;AACrC,UAAM,MAAM,OAAO,OAAO,KAAK;AAC/B,UAAM,eAAe,OAAO,gBAAgB,KAAK;AACjD,UAAM,YAAY,OAAO,aAAa,KAAK;AAC3C,UAAM,WAAW,OAAO,YAAY,KAAK;AACzC,UAAM,QAAQ,OAAO,SAAS,KAAK;AACnC,UAAM,OAAO,OAAO,QAAQ,KAAK;AACjC,UAAM,WAAW,OAAO,YAAY,KAAK;AACzC,UAAM,cAAc,OAAO,eAAe,KAAK;AAG/C,UAAM,gBAAgB,OAAO,iBAAiB,KAAK;AACnD,UAAM,UAAU,OAAO,WAAW,KAAK;AAKvC,UAAM,OAAO,OAAO,SAAS,SAAY,OAAO,OAAO,KAAK;AAG5D,UAAM,kBAAkB,OAAO,mBAAmB,KAAK;AACvD,UAAM,iBAAiB,OAAO,kBAAkB,KAAK;AAGrD,QAAI,UAAU,OAAO,WAAW,KAAK;AACrC,QAAI,SAAS,OAAO,UAAU,KAAK;AAEnC,UAAM,UAAU,OAAO,WAAW,KAAK;AAEvC,QAAI,OAAO,eAAe,QAAW;AAEnC,gBAAU,OAAO,KAAK,OAAO,UAAU,EAAE,OAAO,CAACC,UAAS,SAASA,SAAQ,IAAI,MAAM,OAAO,WAAW,IAAI,CAAC,GAAG,OAAO;AAAA,IACxH;AAEA,QAAI,OAAO,WAAW;AAEpB,eAAS,OAAO,KAAK,OAAO,SAAS,EAAE,OAAO,CAACC,SAAQ,UAAUA,QAAO,IAAI,OAAO,OAAO,UAAU,KAAK,CAAC,GAAG,MAAM;AAAA,IACrH;AAEA,WAAO,IAAI,aAAY,QAAQ,KAAK,MAAM;AAAA,MACxC;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH;AACF;AAOA,IAAI;AAAA,CACH,SAAUC,gBAAe;AAIxB,EAAAA,eAAcA,eAAc,MAAM,IAAI,CAAC,IAAI;AAM3C,EAAAA,eAAcA,eAAc,gBAAgB,IAAI,CAAC,IAAI;AAIrD,EAAAA,eAAcA,eAAc,gBAAgB,IAAI,CAAC,IAAI;AAIrD,EAAAA,eAAcA,eAAc,kBAAkB,IAAI,CAAC,IAAI;AAIvD,EAAAA,eAAcA,eAAc,UAAU,IAAI,CAAC,IAAI;AAI/C,EAAAA,eAAcA,eAAc,MAAM,IAAI,CAAC,IAAI;AAC7C,GAAG,kBAAkB,gBAAgB,CAAC,EAAE;AAMxC,IAAM,mBAAN,MAAuB;AAAA;AAAA;AAAA;AAAA,EAIrB;AAAA;AAAA;AAAA;AAAA,EAIA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA;AAAA;AAAA;AAAA;AAAA,EAIA;AAAA;AAAA;AAAA;AAAA,EAIA;AAAA;AAAA;AAAA;AAAA,EAIA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,YAAY,MAAM,gBAAgB,KAAK,oBAAoB,MAAM;AAG/D,SAAK,UAAU,KAAK,WAAW,IAAI,YAAY;AAC/C,SAAK,SAAS,KAAK,WAAW,SAAY,KAAK,SAAS;AACxD,SAAK,aAAa,KAAK,cAAc;AACrC,SAAK,MAAM,KAAK,OAAO;AAEvB,SAAK,KAAK,KAAK,UAAU,OAAO,KAAK,SAAS;AAAA,EAChD;AACF;AAUA,IAAM,qBAAN,MAAM,4BAA2B,iBAAiB;AAAA;AAAA;AAAA;AAAA,EAIhD,YAAY,OAAO,CAAC,GAAG;AACrB,UAAM,IAAI;AAAA,EACZ;AAAA,EACA,OAAO,cAAc;AAAA;AAAA;AAAA;AAAA;AAAA,EAKrB,MAAM,SAAS,CAAC,GAAG;AAGjB,WAAO,IAAI,oBAAmB;AAAA,MAC5B,SAAS,OAAO,WAAW,KAAK;AAAA,MAChC,QAAQ,OAAO,WAAW,SAAY,OAAO,SAAS,KAAK;AAAA,MAC3D,YAAY,OAAO,cAAc,KAAK;AAAA,MACtC,KAAK,OAAO,OAAO,KAAK,OAAO;AAAA,IACjC,CAAC;AAAA,EACH;AACF;AAUA,IAAM,eAAN,MAAM,sBAAqB,iBAAiB;AAAA;AAAA;AAAA;AAAA,EAI1C;AAAA;AAAA;AAAA;AAAA,EAIA,YAAY,OAAO,CAAC,GAAG;AACrB,UAAM,IAAI;AACV,SAAK,OAAO,KAAK,SAAS,SAAY,KAAK,OAAO;AAAA,EACpD;AAAA,EACA,OAAO,cAAc;AAAA,EACrB,MAAM,SAAS,CAAC,GAAG;AACjB,WAAO,IAAI,cAAa;AAAA,MACtB,MAAM,OAAO,SAAS,SAAY,OAAO,OAAO,KAAK;AAAA,MACrD,SAAS,OAAO,WAAW,KAAK;AAAA,MAChC,QAAQ,OAAO,WAAW,SAAY,OAAO,SAAS,KAAK;AAAA,MAC3D,YAAY,OAAO,cAAc,KAAK;AAAA,MACtC,KAAK,OAAO,OAAO,KAAK,OAAO;AAAA,IACjC,CAAC;AAAA,EACH;AACF;AAcA,IAAM,oBAAN,cAAgC,iBAAiB;AAAA,EAC/C,OAAO;AAAA,EACP;AAAA,EACA;AAAA;AAAA;AAAA;AAAA,EAIA,KAAK;AAAA,EACL,YAAY,MAAM;AAEhB,UAAM,MAAM,GAAG,eAAe;AAI9B,QAAI,KAAK,UAAU,OAAO,KAAK,SAAS,KAAK;AAC3C,WAAK,UAAU,mCAAmC,KAAK,OAAO,eAAe;AAAA,IAC/E,OAAO;AACL,WAAK,UAAU,6BAA6B,KAAK,OAAO,eAAe,KAAK,KAAK,MAAM,IAAI,KAAK,UAAU;AAAA,IAC5G;AACA,SAAK,QAAQ,KAAK,SAAS;AAAA,EAC7B;AACF;AAKA,IAAM,sBAAsB;AAC5B,IAAM,8BAA8B;AAMpC,IAAI;AAAA,CACH,SAAUC,iBAAgB;AACzB,EAAAA,gBAAeA,gBAAe,UAAU,IAAI,GAAG,IAAI;AACnD,EAAAA,gBAAeA,gBAAe,oBAAoB,IAAI,GAAG,IAAI;AAC7D,EAAAA,gBAAeA,gBAAe,YAAY,IAAI,GAAG,IAAI;AACrD,EAAAA,gBAAeA,gBAAe,YAAY,IAAI,GAAG,IAAI;AACrD,EAAAA,gBAAeA,gBAAe,IAAI,IAAI,GAAG,IAAI;AAC7C,EAAAA,gBAAeA,gBAAe,SAAS,IAAI,GAAG,IAAI;AAClD,EAAAA,gBAAeA,gBAAe,UAAU,IAAI,GAAG,IAAI;AACnD,EAAAA,gBAAeA,gBAAe,6BAA6B,IAAI,GAAG,IAAI;AACtE,EAAAA,gBAAeA,gBAAe,WAAW,IAAI,GAAG,IAAI;AACpD,EAAAA,gBAAeA,gBAAe,cAAc,IAAI,GAAG,IAAI;AACvD,EAAAA,gBAAeA,gBAAe,gBAAgB,IAAI,GAAG,IAAI;AACzD,EAAAA,gBAAeA,gBAAe,aAAa,IAAI,GAAG,IAAI;AACtD,EAAAA,gBAAeA,gBAAe,iBAAiB,IAAI,GAAG,IAAI;AAC1D,EAAAA,gBAAeA,gBAAe,QAAQ,IAAI,GAAG,IAAI;AACjD,EAAAA,gBAAeA,gBAAe,iBAAiB,IAAI,GAAG,IAAI;AAC1D,EAAAA,gBAAeA,gBAAe,kBAAkB,IAAI,GAAG,IAAI;AAC3D,EAAAA,gBAAeA,gBAAe,OAAO,IAAI,GAAG,IAAI;AAChD,EAAAA,gBAAeA,gBAAe,UAAU,IAAI,GAAG,IAAI;AACnD,EAAAA,gBAAeA,gBAAe,aAAa,IAAI,GAAG,IAAI;AACtD,EAAAA,gBAAeA,gBAAe,UAAU,IAAI,GAAG,IAAI;AACnD,EAAAA,gBAAeA,gBAAe,QAAQ,IAAI,GAAG,IAAI;AACjD,EAAAA,gBAAeA,gBAAe,mBAAmB,IAAI,GAAG,IAAI;AAC5D,EAAAA,gBAAeA,gBAAe,mBAAmB,IAAI,GAAG,IAAI;AAC5D,EAAAA,gBAAeA,gBAAe,YAAY,IAAI,GAAG,IAAI;AACrD,EAAAA,gBAAeA,gBAAe,cAAc,IAAI,GAAG,IAAI;AACvD,EAAAA,gBAAeA,gBAAe,iBAAiB,IAAI,GAAG,IAAI;AAC1D,EAAAA,gBAAeA,gBAAe,WAAW,IAAI,GAAG,IAAI;AACpD,EAAAA,gBAAeA,gBAAe,UAAU,IAAI,GAAG,IAAI;AACnD,EAAAA,gBAAeA,gBAAe,kBAAkB,IAAI,GAAG,IAAI;AAC3D,EAAAA,gBAAeA,gBAAe,eAAe,IAAI,GAAG,IAAI;AACxD,EAAAA,gBAAeA,gBAAe,6BAA6B,IAAI,GAAG,IAAI;AACtE,EAAAA,gBAAeA,gBAAe,gBAAgB,IAAI,GAAG,IAAI;AACzD,EAAAA,gBAAeA,gBAAe,UAAU,IAAI,GAAG,IAAI;AACnD,EAAAA,gBAAeA,gBAAe,MAAM,IAAI,GAAG,IAAI;AAC/C,EAAAA,gBAAeA,gBAAe,gBAAgB,IAAI,GAAG,IAAI;AACzD,EAAAA,gBAAeA,gBAAe,oBAAoB,IAAI,GAAG,IAAI;AAC7D,EAAAA,gBAAeA,gBAAe,iBAAiB,IAAI,GAAG,IAAI;AAC1D,EAAAA,gBAAeA,gBAAe,YAAY,IAAI,GAAG,IAAI;AACrD,EAAAA,gBAAeA,gBAAe,sBAAsB,IAAI,GAAG,IAAI;AAC/D,EAAAA,gBAAeA,gBAAe,qBAAqB,IAAI,GAAG,IAAI;AAC9D,EAAAA,gBAAeA,gBAAe,mBAAmB,IAAI,GAAG,IAAI;AAC5D,EAAAA,gBAAeA,gBAAe,WAAW,IAAI,GAAG,IAAI;AACpD,EAAAA,gBAAeA,gBAAe,oBAAoB,IAAI,GAAG,IAAI;AAC7D,EAAAA,gBAAeA,gBAAe,qBAAqB,IAAI,GAAG,IAAI;AAC9D,EAAAA,gBAAeA,gBAAe,QAAQ,IAAI,GAAG,IAAI;AACjD,EAAAA,gBAAeA,gBAAe,kBAAkB,IAAI,GAAG,IAAI;AAC3D,EAAAA,gBAAeA,gBAAe,UAAU,IAAI,GAAG,IAAI;AACnD,EAAAA,gBAAeA,gBAAe,iBAAiB,IAAI,GAAG,IAAI;AAC1D,EAAAA,gBAAeA,gBAAe,sBAAsB,IAAI,GAAG,IAAI;AAC/D,EAAAA,gBAAeA,gBAAe,iBAAiB,IAAI,GAAG,IAAI;AAC1D,EAAAA,gBAAeA,gBAAe,6BAA6B,IAAI,GAAG,IAAI;AACtE,EAAAA,gBAAeA,gBAAe,4BAA4B,IAAI,GAAG,IAAI;AACrE,EAAAA,gBAAeA,gBAAe,qBAAqB,IAAI,GAAG,IAAI;AAC9D,EAAAA,gBAAeA,gBAAe,gBAAgB,IAAI,GAAG,IAAI;AACzD,EAAAA,gBAAeA,gBAAe,YAAY,IAAI,GAAG,IAAI;AACrD,EAAAA,gBAAeA,gBAAe,oBAAoB,IAAI,GAAG,IAAI;AAC7D,EAAAA,gBAAeA,gBAAe,gBAAgB,IAAI,GAAG,IAAI;AACzD,EAAAA,gBAAeA,gBAAe,yBAAyB,IAAI,GAAG,IAAI;AAClE,EAAAA,gBAAeA,gBAAe,uBAAuB,IAAI,GAAG,IAAI;AAChE,EAAAA,gBAAeA,gBAAe,qBAAqB,IAAI,GAAG,IAAI;AAC9D,EAAAA,gBAAeA,gBAAe,cAAc,IAAI,GAAG,IAAI;AACvD,EAAAA,gBAAeA,gBAAe,aAAa,IAAI,GAAG,IAAI;AACtD,EAAAA,gBAAeA,gBAAe,+BAA+B,IAAI,GAAG,IAAI;AAC1E,GAAG,mBAAmB,iBAAiB,CAAC,EAAE;AAY1C,SAAS,QAAQ,SAAS,MAAM;AAC9B,SAAO;AAAA,IACL;AAAA,IACA,SAAS,QAAQ;AAAA,IACjB,SAAS,QAAQ;AAAA,IACjB,SAAS,QAAQ;AAAA,IACjB,QAAQ,QAAQ;AAAA,IAChB,gBAAgB,QAAQ;AAAA,IACxB,cAAc,QAAQ;AAAA,IACtB,iBAAiB,QAAQ;AAAA,IACzB,eAAe,QAAQ;AAAA,IACvB,WAAW,QAAQ;AAAA,IACnB,UAAU,QAAQ;AAAA,IAClB,OAAO,QAAQ;AAAA,IACf,MAAM,QAAQ;AAAA,IACd,UAAU,QAAQ;AAAA,EACpB;AACF;AAsDA,IAAM,aAAN,MAAM,YAAW;AAAA,EACf;AAAA,EACA,YAAY,SAAS;AACnB,SAAK,UAAU;AAAA,EACjB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA2BA,QAAQ,OAAO,KAAK,UAAU,CAAC,GAAG;AAChC,QAAI;AAEJ,QAAI,iBAAiB,aAAa;AAGhC,YAAM;AAAA,IACR,OAAO;AAKL,UAAI,UAAU;AACd,UAAI,QAAQ,mBAAmB,aAAa;AAC1C,kBAAU,QAAQ;AAAA,MACpB,OAAO;AACL,kBAAU,IAAI,YAAY,QAAQ,OAAO;AAAA,MAC3C;AAEA,UAAI,SAAS;AACb,UAAI,CAAC,CAAC,QAAQ,QAAQ;AACpB,YAAI,QAAQ,kBAAkB,YAAY;AACxC,mBAAS,QAAQ;AAAA,QACnB,OAAO;AACL,mBAAS,IAAI,WAAW;AAAA,YACtB,YAAY,QAAQ;AAAA,UACtB,CAAC;AAAA,QACH;AAAA,MACF;AAEA,YAAM,IAAI,YAAY,OAAO,KAAK,QAAQ,SAAS,SAAY,QAAQ,OAAO,MAAM;AAAA,QAClF;AAAA,QACA,SAAS,QAAQ;AAAA,QACjB;AAAA,QACA,gBAAgB,QAAQ;AAAA;AAAA,QAExB,cAAc,QAAQ,gBAAgB;AAAA,QACtC,iBAAiB,QAAQ;AAAA,QACzB,eAAe,QAAQ;AAAA,QACvB,WAAW,QAAQ;AAAA,QACnB,UAAU,QAAQ;AAAA,QAClB,OAAO,QAAQ;AAAA,QACf,MAAM,QAAQ;AAAA,QACd,UAAU,QAAQ;AAAA,QAClB,aAAa,QAAQ;AAAA,MACvB,CAAC;AAAA,IACH;AAKA,UAAM,UAAU,GAAG,GAAG,EAAE,KAAK,UAAU,CAAAC,SAAO,KAAK,QAAQ,OAAOA,IAAG,CAAC,CAAC;AAIvE,QAAI,iBAAiB,eAAe,QAAQ,YAAY,UAAU;AAChE,aAAO;AAAA,IACT;AAIA,UAAM,OAAO,QAAQ,KAAK,OAAO,WAAS,iBAAiB,YAAY,CAAC;AAExE,YAAQ,QAAQ,WAAW,QAAQ;AAAA,MACjC,KAAK;AAMH,gBAAQ,IAAI,cAAc;AAAA,UACxB,KAAK;AACH,mBAAO,KAAK,KAAK,IAAI,SAAO;AAE1B,kBAAI,IAAI,SAAS,QAAQ,EAAE,IAAI,gBAAgB,cAAc;AAC3D,sBAAM,IAAI,aAAc,MAA6D,aAAa,iCAAiC;AAAA,cACrI;AACA,qBAAO,IAAI;AAAA,YACb,CAAC,CAAC;AAAA,UACJ,KAAK;AACH,mBAAO,KAAK,KAAK,IAAI,SAAO;AAE1B,kBAAI,IAAI,SAAS,QAAQ,EAAE,IAAI,gBAAgB,OAAO;AACpD,sBAAM,IAAI,aAAc,MAAoD,aAAa,yBAAyB;AAAA,cACpH;AACA,qBAAO,IAAI;AAAA,YACb,CAAC,CAAC;AAAA,UACJ,KAAK;AACH,mBAAO,KAAK,KAAK,IAAI,SAAO;AAE1B,kBAAI,IAAI,SAAS,QAAQ,OAAO,IAAI,SAAS,UAAU;AACrD,sBAAM,IAAI,aAAc,MAAsD,aAAa,2BAA2B;AAAA,cACxH;AACA,qBAAO,IAAI;AAAA,YACb,CAAC,CAAC;AAAA,UACJ,KAAK;AAAA,UACL;AAEE,mBAAO,KAAK,KAAK,IAAI,SAAO,IAAI,IAAI,CAAC;AAAA,QACzC;AAAA,MACF,KAAK;AAEH,eAAO;AAAA,MACT;AAEE,cAAM,IAAI,aAAc,MAAoD,aAAa,uCAAuC,QAAQ,OAAO,GAAG;AAAA,IACtJ;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,OAAO,KAAK,UAAU,CAAC,GAAG;AACxB,WAAO,KAAK,QAAQ,UAAU,KAAK,OAAO;AAAA,EAC5C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,KAAK,UAAU,CAAC,GAAG;AACrB,WAAO,KAAK,QAAQ,OAAO,KAAK,OAAO;AAAA,EACzC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,KAAK,KAAK,UAAU,CAAC,GAAG;AACtB,WAAO,KAAK,QAAQ,QAAQ,KAAK,OAAO;AAAA,EAC1C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAmBA,MAAM,KAAK,eAAe;AACxB,WAAO,KAAK,QAAQ,SAAS,KAAK;AAAA,MAChC,QAAQ,IAAI,WAAW,EAAE,OAAO,eAAe,gBAAgB;AAAA,MAC/D,SAAS;AAAA,MACT,cAAc;AAAA,IAChB,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,QAAQ,KAAK,UAAU,CAAC,GAAG;AACzB,WAAO,KAAK,QAAQ,WAAW,KAAK,OAAO;AAAA,EAC7C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,KAAK,MAAM,UAAU,CAAC,GAAG;AAC7B,WAAO,KAAK,QAAQ,SAAS,KAAK,QAAQ,SAAS,IAAI,CAAC;AAAA,EAC1D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,KAAK,KAAK,MAAM,UAAU,CAAC,GAAG;AAC5B,WAAO,KAAK,QAAQ,QAAQ,KAAK,QAAQ,SAAS,IAAI,CAAC;AAAA,EACzD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,IAAI,KAAK,MAAM,UAAU,CAAC,GAAG;AAC3B,WAAO,KAAK,QAAQ,OAAO,KAAK,QAAQ,SAAS,IAAI,CAAC;AAAA,EACxD;AAAA,EACA,OAAO,OAAO,SAAS,mBAAmB,mBAAmB;AAC3D,WAAO,KAAK,qBAAqB,aAAe,SAAS,WAAW,CAAC;AAAA,EACvE;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,YAAW;AAAA,EACtB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,YAAY,CAAC;AAAA,IACnF,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,EACR,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAM,gBAAgB;AAKtB,SAAS,iBAAiB,UAAU;AAClC,MAAI,SAAS,KAAK;AAChB,WAAO,SAAS;AAAA,EAClB;AAEA,QAAM,cAAc,qBAAqB,kBAAkB;AAC3D,SAAO,SAAS,QAAQ,IAAI,WAAW;AACzC;AAKA,IAAM,gBAAgB,IAAI,eAAe,OAAO,cAAc,eAAe,YAAY,kBAAkB,EAAE;AAY7G,IAAM,eAAN,MAAM,cAAa;AAAA;AAAA;AAAA;AAAA,EAIjB,YAAY,OAAO,cAAc;AAAA,IAC/B,UAAU;AAAA,EACZ,CAAC,GAAG,UAAU,IAAI,SAAS,WAAW,MAAM,GAAG,IAAI;AAAA,EACnD,SAAS,OAAO,MAAM;AAAA,EACtB,aAAa,OAAO,UAAU;AAAA,EAC9B,YAAY;AAAA,EACZ,cAAc;AACZ,SAAK,WAAW,UAAU,MAAM;AAC9B,WAAK,YAAY;AAAA,IACnB,CAAC;AAAA,EACH;AAAA,EACA,OAAO,SAAS;AACd,WAAO,IAAI,WAAW,cAAY;AAChC,YAAM,UAAU,IAAI,gBAAgB;AACpC,WAAK,UAAU,SAAS,QAAQ,QAAQ,QAAQ,EAAE,KAAK,MAAM,WAAS,SAAS,MAAM,IAAI,kBAAkB;AAAA,QACzG;AAAA,MACF,CAAC,CAAC,CAAC;AACH,UAAI;AACJ,UAAI,QAAQ,SAAS;AAGnB,oBAAY,KAAK,OAAO,kBAAkB,MAAM,WAAW,MAAM;AAC/D,cAAI,CAAC,QAAQ,OAAO,SAAS;AAC3B,oBAAQ,MAAM,IAAI,aAAa,oBAAoB,cAAc,CAAC;AAAA,UACpE;AAAA,QACF,GAAG,QAAQ,OAAO,CAAC;AAAA,MACrB;AACA,aAAO,MAAM;AACX,YAAI,cAAc,QAAW;AAC3B,uBAAa,SAAS;AAAA,QACxB;AACA,gBAAQ,MAAM;AAAA,MAChB;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACM,UAAU,SAASC,SAAQ,UAAU;AAAA;AACzC,YAAM,OAAO,KAAK,kBAAkB,OAAO;AAC3C,UAAI;AACJ,UAAI;AAIF,cAAM,eAAe,KAAK,OAAO,kBAAkB,MAAM,KAAK,UAAU,QAAQ,eAAe;AAAA,UAC7F,QAAAA;AAAA,WACG,KACJ,CAAC;AAIF,oDAA4C,YAAY;AAExD,iBAAS,KAAK;AAAA,UACZ,MAAM,cAAc;AAAA,QACtB,CAAC;AACD,mBAAW,MAAM;AAAA,MACnB,SAAS,OAAO;AACd,iBAAS,MAAM,IAAI,kBAAkB;AAAA,UACnC;AAAA,UACA,QAAQ,MAAM,UAAU;AAAA,UACxB,YAAY,MAAM;AAAA,UAClB,KAAK,QAAQ;AAAA,UACb,SAAS,MAAM;AAAA,QACjB,CAAC,CAAC;AACF;AAAA,MACF;AACA,YAAM,UAAU,IAAI,YAAY,SAAS,OAAO;AAChD,YAAM,aAAa,SAAS;AAC5B,YAAM,MAAM,iBAAiB,QAAQ,KAAK,QAAQ;AAClD,UAAI,SAAS,SAAS;AACtB,UAAI,OAAO;AACX,UAAI,QAAQ,gBAAgB;AAC1B,iBAAS,KAAK,IAAI,mBAAmB;AAAA,UACnC;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF,CAAC,CAAC;AAAA,MACJ;AACA,UAAI,SAAS,MAAM;AAEjB,cAAM,gBAAgB,SAAS,QAAQ,IAAI,gBAAgB;AAC3D,cAAM,SAAS,CAAC;AAChB,cAAM,SAAS,SAAS,KAAK,UAAU;AACvC,YAAI,iBAAiB;AACrB,YAAI;AACJ,YAAI;AAGJ,cAAM,UAAU,OAAO,SAAS,eAAe,KAAK;AACpD,YAAI,WAAW;AAIf,cAAM,KAAK,OAAO,kBAAkB,MAAY;AAC9C,iBAAO,MAAM;AAKX,gBAAI,KAAK,WAAW;AAKlB,oBAAM,OAAO,OAAO;AACpB,yBAAW;AACX;AAAA,YACF;AACA,kBAAM;AAAA,cACJ;AAAA,cACA;AAAA,YACF,IAAI,MAAM,OAAO,KAAK;AACtB,gBAAI,MAAM;AACR;AAAA,YACF;AACA,mBAAO,KAAK,KAAK;AACjB,8BAAkB,MAAM;AACxB,gBAAI,QAAQ,gBAAgB;AAC1B,4BAAc,QAAQ,iBAAiB,UAAU,eAAe,OAAO,YAAY,IAAI,YAAY,GAAG,OAAO,OAAO;AAAA,gBAClH,QAAQ;AAAA,cACV,CAAC,IAAI;AACL,oBAAM,iBAAiB,MAAM,SAAS,KAAK;AAAA,gBACzC,MAAM,cAAc;AAAA,gBACpB,OAAO,gBAAgB,CAAC,gBAAgB;AAAA,gBACxC,QAAQ;AAAA,gBACR;AAAA,cACF,CAAC;AACD,wBAAU,QAAQ,IAAI,cAAc,IAAI,eAAe;AAAA,YACzD;AAAA,UACF;AAAA,QACF,EAAC;AAKD,YAAI,UAAU;AACZ,mBAAS,SAAS;AAClB;AAAA,QACF;AAEA,cAAM,YAAY,KAAK,aAAa,QAAQ,cAAc;AAC1D,YAAI;AACF,gBAAM,cAAc,SAAS,QAAQ,IAAI,mBAAmB,KAAK;AACjE,iBAAO,KAAK,UAAU,SAAS,WAAW,WAAW;AAAA,QACvD,SAAS,OAAO;AAEd,mBAAS,MAAM,IAAI,kBAAkB;AAAA,YACnC;AAAA,YACA,SAAS,IAAI,YAAY,SAAS,OAAO;AAAA,YACzC,QAAQ,SAAS;AAAA,YACjB,YAAY,SAAS;AAAA,YACrB,KAAK,iBAAiB,QAAQ,KAAK,QAAQ;AAAA,UAC7C,CAAC,CAAC;AACF;AAAA,QACF;AAAA,MACF;AAEA,UAAI,WAAW,GAAG;AAChB,iBAAS,OAAO,sBAAsB;AAAA,MACxC;AAKA,YAAM,KAAK,UAAU,OAAO,SAAS;AACrC,UAAI,IAAI;AACN,iBAAS,KAAK,IAAI,aAAa;AAAA,UAC7B;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF,CAAC,CAAC;AAGF,iBAAS,SAAS;AAAA,MACpB,OAAO;AACL,iBAAS,MAAM,IAAI,kBAAkB;AAAA,UACnC,OAAO;AAAA,UACP;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF,CAAC,CAAC;AAAA,MACJ;AAAA,IACF;AAAA;AAAA,EACA,UAAU,SAAS,YAAY,aAAa;AAC1C,YAAQ,QAAQ,cAAc;AAAA,MAC5B,KAAK;AAEH,cAAM,OAAO,IAAI,YAAY,EAAE,OAAO,UAAU,EAAE,QAAQ,eAAe,EAAE;AAC3E,eAAO,SAAS,KAAK,OAAO,KAAK,MAAM,IAAI;AAAA,MAC7C,KAAK;AACH,eAAO,IAAI,YAAY,EAAE,OAAO,UAAU;AAAA,MAC5C,KAAK;AACH,eAAO,IAAI,KAAK,CAAC,UAAU,GAAG;AAAA,UAC5B,MAAM;AAAA,QACR,CAAC;AAAA,MACH,KAAK;AACH,eAAO,WAAW;AAAA,IACtB;AAAA,EACF;AAAA,EACA,kBAAkB,KAAK;AAErB,UAAM,UAAU,CAAC;AACjB,QAAI;AAGJ,kBAAc,IAAI;AAElB,QAAI,IAAI,iBAAiB;AAEvB,OAAC,OAAO,cAAc,eAAe,cAAc,sBAAsB,GAAG;AAC5E,oBAAc;AAAA,IAChB;AAEA,QAAI,QAAQ,QAAQ,CAAC,MAAM,WAAW,QAAQ,IAAI,IAAI,OAAO,KAAK,GAAG,CAAC;AAEtE,QAAI,CAAC,IAAI,QAAQ,IAAI,aAAa,GAAG;AACnC,cAAQ,aAAa,IAAI;AAAA,IAC3B;AAEA,QAAI,CAAC,IAAI,QAAQ,IAAI,mBAAmB,GAAG;AACzC,YAAM,eAAe,IAAI,wBAAwB;AAEjD,UAAI,iBAAiB,MAAM;AACzB,gBAAQ,mBAAmB,IAAI;AAAA,MACjC;AAAA,IACF;AACA,WAAO;AAAA,MACL,MAAM,IAAI,cAAc;AAAA,MACxB,QAAQ,IAAI;AAAA,MACZ;AAAA,MACA;AAAA,MACA,WAAW,IAAI;AAAA,MACf,OAAO,IAAI;AAAA,MACX,UAAU,IAAI;AAAA,MACd,MAAM,IAAI;AAAA,MACV,UAAU,IAAI;AAAA,IAChB;AAAA,EACF;AAAA,EACA,aAAa,QAAQ,aAAa;AAChC,UAAM,YAAY,IAAI,WAAW,WAAW;AAC5C,QAAI,WAAW;AACf,eAAW,SAAS,QAAQ;AAC1B,gBAAU,IAAI,OAAO,QAAQ;AAC7B,kBAAY,MAAM;AAAA,IACpB;AACA,WAAO;AAAA,EACT;AAAA,EACA,OAAO,OAAO,SAAS,qBAAqB,mBAAmB;AAC7D,WAAO,KAAK,qBAAqB,eAAc;AAAA,EACjD;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,cAAa;AAAA,EACxB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,cAAc,CAAC;AAAA,IACrF,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AAIH,IAAM,eAAN,MAAmB;AAAC;AACpB,SAAS,OAAO;AAAC;AACjB,SAAS,sBAAsB,KAAK;AAClC,MAAI,IAAI,eAAe,IAAI,iBAAiB;AAC1C,YAAQ,KAAK,mBAAoB,MAA6E,yGAAyG,IAAI,WAAW,gLAAgL,IAAI,WAAW,2BAA2B,CAAC;AAAA,EACnc;AACF;AAOA,SAAS,4CAA4C,SAAS;AAC5D,UAAQ,KAAK,MAAM,IAAI;AACzB;AACA,SAAS,sBAAsB,KAAK,gBAAgB;AAClD,SAAO,eAAe,GAAG;AAC3B;AAKA,SAAS,8BAA8B,aAAa,aAAa;AAC/D,SAAO,CAAC,gBAAgB,mBAAmB,YAAY,UAAU,gBAAgB;AAAA,IAC/E,QAAQ,uBAAqB,YAAY,mBAAmB,cAAc;AAAA,EAC5E,CAAC;AACH;AAKA,SAAS,qBAAqB,aAAa,eAAe,UAAU;AAClE,SAAO,CAAC,gBAAgB,mBAAmB,sBAAsB,UAAU,MAAM,cAAc,gBAAgB,uBAAqB,YAAY,mBAAmB,cAAc,CAAC,CAAC;AACrL;AAOA,IAAM,oBAAoB,IAAI,eAAe,YAAY,sBAAsB,EAAE;AAIjF,IAAM,uBAAuB,IAAI,eAAe,YAAY,yBAAyB,EAAE;AAIvF,IAAM,4BAA4B,IAAI,eAAe,YAAY,8BAA8B,EAAE;AAIjG,IAAM,mCAAmC,IAAI,eAAe,YAAY,qCAAqC,IAAI;AAAA,EAC/G,YAAY;AAAA,EACZ,SAAS,MAAM;AACjB,CAAC;AAKD,SAAS,6BAA6B;AACpC,MAAI,QAAQ;AACZ,SAAO,CAAC,KAAK,YAAY;AACvB,QAAI,UAAU,MAAM;AAClB,YAAM,eAAe,OAAO,mBAAmB;AAAA,QAC7C,UAAU;AAAA,MACZ,CAAC,KAAK,CAAC;AAKP,cAAQ,aAAa,YAAY,+BAA+B,qBAAqB;AAAA,IACvF;AACA,UAAM,eAAe,OAAO,YAAY;AACxC,UAAM,wBAAwB,OAAO,gCAAgC;AACrE,QAAI,uBAAuB;AACzB,YAAM,aAAa,aAAa,IAAI;AACpC,aAAO,MAAM,KAAK,OAAO,EAAE,KAAK,SAAS,UAAU,CAAC;AAAA,IACtD,OAAO;AACL,aAAO,MAAM,KAAK,OAAO;AAAA,IAC3B;AAAA,EACF;AACF;AACA,IAAI,+BAA+B;AACnC,IAAM,yBAAN,MAAM,gCAA+B,YAAY;AAAA,EAC/C;AAAA,EACA;AAAA,EACA,QAAQ;AAAA,EACR,eAAe,OAAO,YAAY;AAAA,EAClC,wBAAwB,OAAO,gCAAgC;AAAA,EAC/D,YAAY,SAAS,UAAU;AAC7B,UAAM;AACN,SAAK,UAAU;AACf,SAAK,WAAW;AAIhB,SAAK,OAAO,cAAc,eAAe,cAAc,CAAC,8BAA8B;AAKpF,YAAM,mBAAmB,KAAK,QAAQ;AACtC,UAA2C,OAA8E;AACvH,uCAA+B;AAC/B,iBAAS,IAAI,OAAQ,EAAE,KAAK,mBAAoB,MAA4D,4TAAqV,CAAC;AAAA,MACpc;AAAA,IACF;AAAA,EACF;AAAA,EACA,OAAO,gBAAgB;AACrB,QAAI,KAAK,UAAU,MAAM;AACvB,YAAM,wBAAwB,MAAM,KAAK,oBAAI,IAAI,CAAC,GAAG,KAAK,SAAS,IAAI,oBAAoB,GAAG,GAAG,KAAK,SAAS,IAAI,2BAA2B,CAAC,CAAC,CAAC,CAAC,CAAC;AAKnJ,WAAK,QAAQ,sBAAsB,YAAY,CAAC,iBAAiB,kBAAkB,qBAAqB,iBAAiB,eAAe,KAAK,QAAQ,GAAG,qBAAqB;AAAA,IAC/K;AACA,QAAI,KAAK,uBAAuB;AAC9B,YAAM,aAAa,KAAK,aAAa,IAAI;AACzC,aAAO,KAAK,MAAM,gBAAgB,uBAAqB,KAAK,QAAQ,OAAO,iBAAiB,CAAC,EAAE,KAAK,SAAS,UAAU,CAAC;AAAA,IAC1H,OAAO;AACL,aAAO,KAAK,MAAM,gBAAgB,uBAAqB,KAAK,QAAQ,OAAO,iBAAiB,CAAC;AAAA,IAC/F;AAAA,EACF;AAAA,EACA,OAAO,OAAO,SAAS,+BAA+B,mBAAmB;AACvE,WAAO,KAAK,qBAAqB,yBAA2B,SAAS,WAAW,GAAM,SAAY,mBAAmB,CAAC;AAAA,EACxH;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,wBAAuB;AAAA,EAClC,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,wBAAwB,CAAC;AAAA,IAC/F,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG,IAAI;AACV,GAAG;AAMH,IAAI,gBAAgB;AAKpB,IAAI;AAGJ,IAAM,wBAAwB;AAG9B,IAAM,yBAAyB;AAC/B,IAAM,gCAAgC;AAGtC,IAAM,kCAAkC;AAQxC,IAAM,uBAAN,MAA2B;AAAC;AAS5B,SAAS,uBAAuB;AAC9B,MAAI,OAAO,WAAW,UAAU;AAC9B,WAAO;AAAA,EACT;AACA,SAAO,CAAC;AACV;AASA,IAAM,qBAAN,MAAM,oBAAmB;AAAA,EACvB;AAAA,EACA;AAAA;AAAA;AAAA;AAAA,EAIA,kBAAkB,QAAQ,QAAQ;AAAA,EAClC,YAAY,aAAaC,WAAU;AACjC,SAAK,cAAc;AACnB,SAAK,WAAWA;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA,EAIA,eAAe;AACb,WAAO,qBAAqB,eAAe;AAAA,EAC7C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,OAAO,KAAK;AAGV,QAAI,IAAI,WAAW,SAAS;AAC1B,YAAM,IAAI,aAAc,MAAgD,aAAa,sBAAsB;AAAA,IAC7G,WAAW,IAAI,iBAAiB,QAAQ;AACtC,YAAM,IAAI,aAAc,MAAuD,aAAa,6BAA6B;AAAA,IAC3H;AAGA,QAAI,IAAI,QAAQ,KAAK,EAAE,SAAS,GAAG;AACjC,YAAM,IAAI,aAAc,MAAyD,aAAa,+BAA+B;AAAA,IAC/H;AAEA,WAAO,IAAI,WAAW,cAAY;AAIhC,YAAM,WAAW,KAAK,aAAa;AACnC,YAAM,MAAM,IAAI,cAAc,QAAQ,wBAAwB,IAAI,QAAQ,IAAI;AAE9E,YAAM,OAAO,KAAK,SAAS,cAAc,QAAQ;AACjD,WAAK,MAAM;AAIX,UAAI,OAAO;AAEX,UAAI,WAAW;AAIf,WAAK,YAAY,QAAQ,IAAI,UAAQ;AAEnC,eAAO,KAAK,YAAY,QAAQ;AAEhC,eAAO;AACP,mBAAW;AAAA,MACb;AAIA,YAAM,UAAU,MAAM;AACpB,aAAK,oBAAoB,QAAQ,MAAM;AACvC,aAAK,oBAAoB,SAAS,OAAO;AAEzC,aAAK,OAAO;AAGZ,eAAO,KAAK,YAAY,QAAQ;AAAA,MAClC;AAKA,YAAM,SAAS,MAAM;AAInB,aAAK,gBAAgB,KAAK,MAAM;AAE9B,kBAAQ;AAER,cAAI,CAAC,UAAU;AAGb,qBAAS,MAAM,IAAI,kBAAkB;AAAA,cACnC;AAAA,cACA,QAAQ;AAAA,cACR,YAAY;AAAA,cACZ,OAAO,IAAI,MAAM,qBAAqB;AAAA,YACxC,CAAC,CAAC;AACF;AAAA,UACF;AAGA,mBAAS,KAAK,IAAI,aAAa;AAAA,YAC7B;AAAA,YACA,QAAQ;AAAA,YACR,YAAY;AAAA,YACZ;AAAA,UACF,CAAC,CAAC;AAEF,mBAAS,SAAS;AAAA,QACpB,CAAC;AAAA,MACH;AAIA,YAAM,UAAU,WAAS;AACvB,gBAAQ;AAER,iBAAS,MAAM,IAAI,kBAAkB;AAAA,UACnC;AAAA,UACA,QAAQ;AAAA,UACR,YAAY;AAAA,UACZ;AAAA,QACF,CAAC,CAAC;AAAA,MACJ;AAGA,WAAK,iBAAiB,QAAQ,MAAM;AACpC,WAAK,iBAAiB,SAAS,OAAO;AACtC,WAAK,SAAS,KAAK,YAAY,IAAI;AAEnC,eAAS,KAAK;AAAA,QACZ,MAAM,cAAc;AAAA,MACtB,CAAC;AAED,aAAO,MAAM;AACX,YAAI,CAAC,UAAU;AACb,eAAK,gBAAgB,IAAI;AAAA,QAC3B;AAEA,gBAAQ;AAAA,MACV;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,gBAAgB,QAAQ;AAItB,wBAAoB,KAAK,SAAS,eAAe,mBAAmB;AACpE,oBAAgB,UAAU,MAAM;AAAA,EAClC;AAAA,EACA,OAAO,OAAO,SAAS,2BAA2B,mBAAmB;AACnE,WAAO,KAAK,qBAAqB,qBAAuB,SAAS,oBAAoB,GAAM,SAAS,QAAQ,CAAC;AAAA,EAC/G;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,oBAAmB;AAAA,EAC9B,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,oBAAoB,CAAC;AAAA,IAC3F,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,QAAQ;AAAA,IACjB,CAAC;AAAA,EACH,CAAC,GAAG,IAAI;AACV,GAAG;AAIH,SAAS,mBAAmB,KAAK,MAAM;AACrC,MAAI,IAAI,WAAW,SAAS;AAC1B,WAAO,OAAO,kBAAkB,EAAE,OAAO,GAAG;AAAA,EAC9C;AAEA,SAAO,KAAK,GAAG;AACjB;AASA,IAAM,mBAAN,MAAM,kBAAiB;AAAA,EACrB;AAAA,EACA,YAAY,UAAU;AACpB,SAAK,WAAW;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,UAAU,gBAAgB,MAAM;AAC9B,WAAO,sBAAsB,KAAK,UAAU,MAAM,mBAAmB,gBAAgB,uBAAqB,KAAK,OAAO,iBAAiB,CAAC,CAAC;AAAA,EAC3I;AAAA,EACA,OAAO,OAAO,SAAS,yBAAyB,mBAAmB;AACjE,WAAO,KAAK,qBAAqB,mBAAqB,SAAY,mBAAmB,CAAC;AAAA,EACxF;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,kBAAiB;AAAA,EAC5B,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAM,cAAc;AACpB,IAAM,uBAAuB,OAAO,IAAI,oBAAoB,KAAK,GAAG;AAKpE,SAAS,eAAe,KAAK;AAC3B,MAAI,iBAAiB,OAAO,IAAI,aAAa;AAC3C,WAAO,IAAI;AAAA,EACb;AACA,MAAI,qBAAqB,KAAK,IAAI,sBAAsB,CAAC,GAAG;AAC1D,WAAO,IAAI,kBAAkB,oBAAoB;AAAA,EACnD;AACA,SAAO;AACT;AAKA,SAAS,yBAAyB,KAAK;AACrC,QAAM,qBAAqB,CAAC;AAAA,IAC1B,UAAU;AAAA,IACV,WAAW;AAAA;AAAA,EACb,GAAG;AAAA,IACD,UAAU;AAAA,IACV,WAAW;AAAA;AAAA,EACb,GAAG;AAAA,IACD,UAAU;AAAA,IACV,WAAW;AAAA;AAAA,EACb,GAAG;AAAA,IACD,UAAU;AAAA,IACV,WAAW;AAAA;AAAA,EACb,GAAG;AAAA,IACD,UAAU;AAAA,IACV,WAAW;AAAA;AAAA,EACb,GAAG;AAAA,IACD,UAAU;AAAA,IACV,WAAW;AAAA;AAAA,EACb,CAAC;AAED,aAAW;AAAA,IACT;AAAA,IACA;AAAA,EACF,KAAK,oBAAoB;AACvB,QAAI,IAAI,QAAQ,GAAG;AACjB,cAAQ,KAAK,mBAAoB,WAAW,6DAA6D,QAAQ,yEAAyE,QAAQ,2GAA2G,CAAC;AAAA,IAChT;AAAA,EACF;AACF;AAQA,IAAM,iBAAN,MAAM,gBAAe;AAAA,EACnB;AAAA,EACA,YAAY,YAAY;AACtB,SAAK,aAAa;AAAA,EACpB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,OAAO,KAAK;AAGV,QAAI,IAAI,WAAW,SAAS;AAC1B,YAAM,IAAI,aAAc,QAAoD,OAAO,cAAc,eAAe,cAAc,sNAAsN;AAAA,IACtV;AAEA,iBAAa,yBAAyB,GAAG;AAIzC,UAAM,aAAa,KAAK;AACxB,UAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAMiC,QAAuC,KAAK,WAAW,UAAU,CAAC,IAAI,GAAG,IAAI;AAAA;AACpH,WAAO,OAAO,KAAK,UAAU,MAAM;AAEjC,aAAO,IAAI,WAAW,cAAY;AAGhC,cAAM,MAAM,WAAW,MAAM;AAC7B,YAAI,KAAK,IAAI,QAAQ,IAAI,aAAa;AACtC,YAAI,IAAI,iBAAiB;AACvB,cAAI,kBAAkB;AAAA,QACxB;AAEA,YAAI,QAAQ,QAAQ,CAAC,MAAM,WAAW,IAAI,iBAAiB,MAAM,OAAO,KAAK,GAAG,CAAC,CAAC;AAElF,YAAI,CAAC,IAAI,QAAQ,IAAI,aAAa,GAAG;AACnC,cAAI,iBAAiB,eAAe,mBAAmB;AAAA,QACzD;AAEA,YAAI,CAAC,IAAI,QAAQ,IAAI,mBAAmB,GAAG;AACzC,gBAAM,eAAe,IAAI,wBAAwB;AAEjD,cAAI,iBAAiB,MAAM;AACzB,gBAAI,iBAAiB,qBAAqB,YAAY;AAAA,UACxD;AAAA,QACF;AACA,YAAI,IAAI,SAAS;AACf,cAAI,UAAU,IAAI;AAAA,QACpB;AAEA,YAAI,IAAI,cAAc;AACpB,gBAAM,eAAe,IAAI,aAAa,YAAY;AAMlD,cAAI,eAAe,iBAAiB,SAAS,eAAe;AAAA,QAC9D;AAEA,cAAM,UAAU,IAAI,cAAc;AAOlC,YAAI,iBAAiB;AAGrB,cAAM,iBAAiB,MAAM;AAC3B,cAAI,mBAAmB,MAAM;AAC3B,mBAAO;AAAA,UACT;AACA,gBAAM,aAAa,IAAI,cAAc;AAErC,gBAAM,UAAU,IAAI,YAAY,IAAI,sBAAsB,CAAC;AAG3D,gBAAM,MAAM,eAAe,GAAG,KAAK,IAAI;AAEvC,2BAAiB,IAAI,mBAAmB;AAAA,YACtC;AAAA,YACA,QAAQ,IAAI;AAAA,YACZ;AAAA,YACA;AAAA,UACF,CAAC;AACD,iBAAO;AAAA,QACT;AAIA,cAAM,SAAS,MAAM;AAEnB,cAAI;AAAA,YACF;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,UACF,IAAI,eAAe;AAEnB,cAAI,OAAO;AACX,cAAI,WAAW,6BAA6B;AAE1C,mBAAO,OAAO,IAAI,aAAa,cAAc,IAAI,eAAe,IAAI;AAAA,UACtE;AAEA,cAAI,WAAW,GAAG;AAChB,qBAAS,CAAC,CAAC,OAAO,sBAAsB;AAAA,UAC1C;AAKA,cAAI,KAAK,UAAU,OAAO,SAAS;AAGnC,cAAI,IAAI,iBAAiB,UAAU,OAAO,SAAS,UAAU;AAE3D,kBAAM,eAAe;AACrB,mBAAO,KAAK,QAAQ,aAAa,EAAE;AACnC,gBAAI;AAGF,qBAAO,SAAS,KAAK,KAAK,MAAM,IAAI,IAAI;AAAA,YAC1C,SAAS,OAAO;AAId,qBAAO;AAGP,kBAAI,IAAI;AAEN,qBAAK;AAEL,uBAAO;AAAA,kBACL;AAAA,kBACA,MAAM;AAAA,gBACR;AAAA,cACF;AAAA,YACF;AAAA,UACF;AACA,cAAI,IAAI;AAEN,qBAAS,KAAK,IAAI,aAAa;AAAA,cAC7B;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,cACA,KAAK,OAAO;AAAA,YACd,CAAC,CAAC;AAGF,qBAAS,SAAS;AAAA,UACpB,OAAO;AAEL,qBAAS,MAAM,IAAI,kBAAkB;AAAA;AAAA,cAEnC,OAAO;AAAA,cACP;AAAA,cACA;AAAA,cACA;AAAA,cACA,KAAK,OAAO;AAAA,YACd,CAAC,CAAC;AAAA,UACJ;AAAA,QACF;AAIA,cAAM,UAAU,WAAS;AACvB,gBAAM;AAAA,YACJ;AAAA,UACF,IAAI,eAAe;AACnB,gBAAM,MAAM,IAAI,kBAAkB;AAAA,YAChC;AAAA,YACA,QAAQ,IAAI,UAAU;AAAA,YACtB,YAAY,IAAI,cAAc;AAAA,YAC9B,KAAK,OAAO;AAAA,UACd,CAAC;AACD,mBAAS,MAAM,GAAG;AAAA,QACpB;AACA,YAAI,YAAY;AAChB,YAAI,IAAI,SAAS;AACf,sBAAY,OAAK;AACf,kBAAM;AAAA,cACJ;AAAA,YACF,IAAI,eAAe;AACnB,kBAAM,MAAM,IAAI,kBAAkB;AAAA,cAChC,OAAO,IAAI,aAAa,qBAAqB,cAAc;AAAA,cAC3D,QAAQ,IAAI,UAAU;AAAA,cACtB,YAAY,IAAI,cAAc;AAAA,cAC9B,KAAK,OAAO;AAAA,YACd,CAAC;AACD,qBAAS,MAAM,GAAG;AAAA,UACpB;AAAA,QACF;AAKA,YAAI,cAAc;AAGlB,cAAM,iBAAiB,WAAS;AAE9B,cAAI,CAAC,aAAa;AAChB,qBAAS,KAAK,eAAe,CAAC;AAC9B,0BAAc;AAAA,UAChB;AAGA,cAAI,gBAAgB;AAAA,YAClB,MAAM,cAAc;AAAA,YACpB,QAAQ,MAAM;AAAA,UAChB;AAEA,cAAI,MAAM,kBAAkB;AAC1B,0BAAc,QAAQ,MAAM;AAAA,UAC9B;AAIA,cAAI,IAAI,iBAAiB,UAAU,CAAC,CAAC,IAAI,cAAc;AACrD,0BAAc,cAAc,IAAI;AAAA,UAClC;AAEA,mBAAS,KAAK,aAAa;AAAA,QAC7B;AAGA,cAAM,eAAe,WAAS;AAG5B,cAAI,WAAW;AAAA,YACb,MAAM,cAAc;AAAA,YACpB,QAAQ,MAAM;AAAA,UAChB;AAGA,cAAI,MAAM,kBAAkB;AAC1B,qBAAS,QAAQ,MAAM;AAAA,UACzB;AAEA,mBAAS,KAAK,QAAQ;AAAA,QACxB;AAEA,YAAI,iBAAiB,QAAQ,MAAM;AACnC,YAAI,iBAAiB,SAAS,OAAO;AACrC,YAAI,iBAAiB,WAAW,SAAS;AACzC,YAAI,iBAAiB,SAAS,OAAO;AAErC,YAAI,IAAI,gBAAgB;AAEtB,cAAI,iBAAiB,YAAY,cAAc;AAE/C,cAAI,YAAY,QAAQ,IAAI,QAAQ;AAClC,gBAAI,OAAO,iBAAiB,YAAY,YAAY;AAAA,UACtD;AAAA,QACF;AAEA,YAAI,KAAK,OAAO;AAChB,iBAAS,KAAK;AAAA,UACZ,MAAM,cAAc;AAAA,QACtB,CAAC;AAGD,eAAO,MAAM;AAEX,cAAI,oBAAoB,SAAS,OAAO;AACxC,cAAI,oBAAoB,SAAS,OAAO;AACxC,cAAI,oBAAoB,QAAQ,MAAM;AACtC,cAAI,oBAAoB,WAAW,SAAS;AAC5C,cAAI,IAAI,gBAAgB;AACtB,gBAAI,oBAAoB,YAAY,cAAc;AAClD,gBAAI,YAAY,QAAQ,IAAI,QAAQ;AAClC,kBAAI,OAAO,oBAAoB,YAAY,YAAY;AAAA,YACzD;AAAA,UACF;AAEA,cAAI,IAAI,eAAe,IAAI,MAAM;AAC/B,gBAAI,MAAM;AAAA,UACZ;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH,CAAC,CAAC;AAAA,EACJ;AAAA,EACA,OAAO,OAAO,SAAS,uBAAuB,mBAAmB;AAC/D,WAAO,KAAK,qBAAqB,iBAAmB,SAAS,UAAU,CAAC;AAAA,EAC1E;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,gBAAe;AAAA,EAC1B,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gBAAgB,CAAC;AAAA,IACvF,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,EACR,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAM,eAAe,IAAI,eAAe,YAAY,iBAAiB,EAAE;AACvE,IAAM,2BAA2B;AACjC,IAAM,mBAAmB,IAAI,eAAe,YAAY,qBAAqB,IAAI;AAAA,EAC/E,YAAY;AAAA,EACZ,SAAS,MAAM;AACjB,CAAC;AACD,IAAM,2BAA2B;AACjC,IAAM,mBAAmB,IAAI,eAAe,YAAY,qBAAqB,IAAI;AAAA,EAC/E,YAAY;AAAA,EACZ,SAAS,MAAM;AACjB,CAAC;AAMD,IAAM,yBAAN,MAA6B;AAAC;AAI9B,IAAM,0BAAN,MAAM,yBAAwB;AAAA,EAC5B;AAAA,EACA;AAAA,EACA,mBAAmB;AAAA,EACnB,YAAY;AAAA;AAAA;AAAA;AAAA,EAIZ,aAAa;AAAA,EACb,YAAY,KAAK,YAAY;AAC3B,SAAK,MAAM;AACX,SAAK,aAAa;AAAA,EACpB;AAAA,EACA,WAAW;AACT,QAA2C,OAAc;AACvD,aAAO;AAAA,IACT;AACA,UAAM,eAAe,KAAK,IAAI,UAAU;AACxC,QAAI,iBAAiB,KAAK,kBAAkB;AAC1C,WAAK;AACL,WAAK,YAAY,iBAAiB,cAAc,KAAK,UAAU;AAC/D,WAAK,mBAAmB;AAAA,IAC1B;AACA,WAAO,KAAK;AAAA,EACd;AAAA,EACA,OAAO,OAAO,SAAS,gCAAgC,mBAAmB;AACxE,WAAO,KAAK,qBAAqB,0BAA4B,SAAS,QAAQ,GAAM,SAAS,gBAAgB,CAAC;AAAA,EAChH;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,yBAAwB;AAAA,EACnC,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,yBAAyB,CAAC;AAAA,IAChG,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,QAAQ;AAAA,IACjB,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,gBAAgB;AAAA,IACzB,CAAC;AAAA,EACH,CAAC,GAAG,IAAI;AACV,GAAG;AACH,SAAS,kBAAkB,KAAK,MAAM;AACpC,QAAM,QAAQ,IAAI,IAAI,YAAY;AAKlC,MAAI,CAAC,OAAO,YAAY,KAAK,IAAI,WAAW,SAAS,IAAI,WAAW,UAAU,MAAM,WAAW,SAAS,KAAK,MAAM,WAAW,UAAU,GAAG;AACzI,WAAO,KAAK,GAAG;AAAA,EACjB;AACA,QAAM,QAAQ,OAAO,sBAAsB,EAAE,SAAS;AACtD,QAAM,aAAa,OAAO,gBAAgB;AAE1C,MAAI,SAAS,QAAQ,CAAC,IAAI,QAAQ,IAAI,UAAU,GAAG;AACjD,UAAM,IAAI,MAAM;AAAA,MACd,SAAS,IAAI,QAAQ,IAAI,YAAY,KAAK;AAAA,IAC5C,CAAC;AAAA,EACH;AACA,SAAO,KAAK,GAAG;AACjB;AAIA,IAAM,sBAAN,MAAM,qBAAoB;AAAA,EACxB;AAAA,EACA,YAAY,UAAU;AACpB,SAAK,WAAW;AAAA,EAClB;AAAA,EACA,UAAU,gBAAgB,MAAM;AAC9B,WAAO,sBAAsB,KAAK,UAAU,MAAM,kBAAkB,gBAAgB,uBAAqB,KAAK,OAAO,iBAAiB,CAAC,CAAC;AAAA,EAC1I;AAAA,EACA,OAAO,OAAO,SAAS,4BAA4B,mBAAmB;AACpE,WAAO,KAAK,qBAAqB,sBAAwB,SAAY,mBAAmB,CAAC;AAAA,EAC3F;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,qBAAoB;AAAA,EAC/B,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,qBAAqB,CAAC;AAAA,IAC5F,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,CAAC,GAAG,IAAI;AACV,GAAG;AAOH,IAAI;AAAA,CACH,SAAUC,kBAAiB;AAC1B,EAAAA,iBAAgBA,iBAAgB,cAAc,IAAI,CAAC,IAAI;AACvD,EAAAA,iBAAgBA,iBAAgB,oBAAoB,IAAI,CAAC,IAAI;AAC7D,EAAAA,iBAAgBA,iBAAgB,yBAAyB,IAAI,CAAC,IAAI;AAClE,EAAAA,iBAAgBA,iBAAgB,kBAAkB,IAAI,CAAC,IAAI;AAC3D,EAAAA,iBAAgBA,iBAAgB,cAAc,IAAI,CAAC,IAAI;AACvD,EAAAA,iBAAgBA,iBAAgB,uBAAuB,IAAI,CAAC,IAAI;AAChE,EAAAA,iBAAgBA,iBAAgB,OAAO,IAAI,CAAC,IAAI;AAClD,GAAG,oBAAoB,kBAAkB,CAAC,EAAE;AAC5C,SAAS,gBAAgB,MAAM,WAAW;AACxC,SAAO;AAAA,IACL,OAAO;AAAA,IACP,YAAY;AAAA,EACd;AACF;AA8BA,SAAS,qBAAqB,UAAU;AACtC,MAAI,WAAW;AACb,UAAM,eAAe,IAAI,IAAI,SAAS,IAAI,OAAK,EAAE,KAAK,CAAC;AACvD,QAAI,aAAa,IAAI,gBAAgB,gBAAgB,KAAK,aAAa,IAAI,gBAAgB,uBAAuB,GAAG;AACnH,YAAM,IAAI,MAAM,YAAY,0JAA0J,EAAE;AAAA,IAC1L;AAAA,EACF;AACA,QAAM,YAAY,CAAC,YAAY,gBAAgB,wBAAwB;AAAA,IACrE,SAAS;AAAA,IACT,aAAa;AAAA,EACf,GAAG;AAAA,IACD,SAAS;AAAA,IACT,YAAY,MAAM;AAChB,aAAO,OAAO,eAAe;AAAA,QAC3B,UAAU;AAAA,MACZ,CAAC,KAAK,OAAO,cAAc;AAAA,IAC7B;AAAA,EACF,GAAG;AAAA,IACD,SAAS;AAAA,IACT,UAAU;AAAA,IACV,OAAO;AAAA,EACT,GAAG;AAAA,IACD,SAAS;AAAA,IACT,UAAU;AAAA,EACZ,GAAG;AAAA,IACD,SAAS;AAAA,IACT,UAAU;AAAA,EACZ,CAAC;AACD,aAAW,WAAW,UAAU;AAC9B,cAAU,KAAK,GAAG,QAAQ,UAAU;AAAA,EACtC;AACA,SAAO,yBAAyB,SAAS;AAC3C;AAkBA,IAAM,wBAAwB,IAAI,eAAe,YAAY,0BAA0B,EAAE;AAYzF,SAAS,yBAAyB;AAMhC,SAAO,gBAAgB,gBAAgB,oBAAoB,CAAC;AAAA,IAC1D,SAAS;AAAA,IACT,YAAY;AAAA,EACd,GAAG;AAAA,IACD,SAAS;AAAA,IACT,aAAa;AAAA,IACb,OAAO;AAAA,EACT,CAAC,CAAC;AACJ;AAQA,SAAS,sBAAsB;AAAA,EAC7B;AAAA,EACA;AACF,GAAG;AACD,QAAM,YAAY,CAAC;AACnB,MAAI,eAAe,QAAW;AAC5B,cAAU,KAAK;AAAA,MACb,SAAS;AAAA,MACT,UAAU;AAAA,IACZ,CAAC;AAAA,EACH;AACA,MAAI,eAAe,QAAW;AAC5B,cAAU,KAAK;AAAA,MACb,SAAS;AAAA,MACT,UAAU;AAAA,IACZ,CAAC;AAAA,EACH;AACA,SAAO,gBAAgB,gBAAgB,yBAAyB,SAAS;AAC3E;AAQA,SAAS,uBAAuB;AAC9B,SAAO,gBAAgB,gBAAgB,kBAAkB,CAAC;AAAA,IACxD,SAAS;AAAA,IACT,UAAU;AAAA,EACZ,CAAC,CAAC;AACJ;AAMA,SAAS,mBAAmB;AAC1B,SAAO,gBAAgB,gBAAgB,cAAc,CAAC,oBAAoB;AAAA,IACxE,SAAS;AAAA,IACT,YAAY;AAAA,EACd,GAAG;AAAA,IACD,SAAS;AAAA,IACT,UAAU;AAAA,IACV,OAAO;AAAA,EACT,CAAC,CAAC;AACJ;AAmEA,IAAM,uBAAN,MAAM,sBAAqB;AAAA;AAAA;AAAA;AAAA,EAIzB,OAAO,UAAU;AACf,WAAO;AAAA,MACL,UAAU;AAAA,MACV,WAAW,CAAC,qBAAqB,EAAE,UAAU;AAAA,IAC/C;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,OAAO,YAAY,UAAU,CAAC,GAAG;AAC/B,WAAO;AAAA,MACL,UAAU;AAAA,MACV,WAAW,sBAAsB,OAAO,EAAE;AAAA,IAC5C;AAAA,EACF;AAAA,EACA,OAAO,OAAO,SAAS,6BAA6B,mBAAmB;AACrE,WAAO,KAAK,qBAAqB,uBAAsB;AAAA,EACzD;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,EACR,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,WAAW,CAAC,qBAAqB;AAAA,MAC/B,SAAS;AAAA,MACT,aAAa;AAAA,MACb,OAAO;AAAA,IACT,GAAG;AAAA,MACD,SAAS;AAAA,MACT,UAAU;AAAA,IACZ,GAAG,sBAAsB;AAAA,MACvB,YAAY;AAAA,MACZ,YAAY;AAAA,IACd,CAAC,EAAE,YAAY;AAAA,MACb,SAAS;AAAA,MACT,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,sBAAsB,CAAC;AAAA,IAC7F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,WAAW,CAAC,qBAAqB;AAAA,QAC/B,SAAS;AAAA,QACT,aAAa;AAAA,QACb,OAAO;AAAA,MACT,GAAG;AAAA,QACD,SAAS;AAAA,QACT,UAAU;AAAA,MACZ,GAAG,sBAAsB;AAAA,QACvB,YAAY;AAAA,QACZ,YAAY;AAAA,MACd,CAAC,EAAE,YAAY;AAAA,QACb,SAAS;AAAA,QACT,UAAU;AAAA,MACZ,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAWH,IAAM,mBAAN,MAAM,kBAAiB;AAAA,EACrB,OAAO,OAAO,SAAS,yBAAyB,mBAAmB;AACjE,WAAO,KAAK,qBAAqB,mBAAkB;AAAA,EACrD;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,EACR,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,WAAW,CAAC,kBAAkB,uBAAuB,CAAC,CAAC;AAAA,EACzD,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKL,WAAW,CAAC,kBAAkB,uBAAuB,CAAC,CAAC;AAAA,IACzD,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAUH,IAAM,wBAAN,MAAM,uBAAsB;AAAA,EAC1B,OAAO,OAAO,SAAS,8BAA8B,mBAAmB;AACtE,WAAO,KAAK,qBAAqB,wBAAuB;AAAA,EAC1D;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,EACR,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,WAAW,CAAC,iBAAiB,EAAE,UAAU;AAAA,EAC3C,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,uBAAuB,CAAC;AAAA,IAC9F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,WAAW,CAAC,iBAAiB,EAAE,UAAU;AAAA,IAC3C,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;;;ACtoGH,IAAM,gBAAgB,MAAM;AACxB,QAAM,SAAS,mBAAmB,MAAM;AACxC,SAAO,cAAc,mBAAmB,aAAa;AACrD,SAAO,OAAO,mBAAmB,MAAM;AACvC,SAAO,OAAO,mBAAmB,MAAM;AACvC,SAAO;AACX,GAAG;AACH,SAAS,mBAAmB,cAAc;AACtC,SAAO,SAASC,cAAa,SAAS,SAAS;AAC3C,QAAI,aAAa,CAAC,SAAS,UAAU;AACjC,+BAAyBA,aAAY;AAAA,IACzC;AACA,UAAM,WAAW,SAAS,YAAY,OAAO,QAAQ;AACrD,WAAO,IAAI,iBAAiB,UAAU,MAAM,iBAAiB,SAAS,YAAY,GAAG,SAAS,cAAc,SAAS,OAAO,SAAS,KAAK;AAAA,EAC9I;AACJ;AACA,SAAS,iBAAiB,SAAS,cAAc;AAC7C,MAAI,mBAAmB,OAAO,YAAY,aAAa,QAAQ,IAAI;AACnE,MAAI,qBAAqB,QAAW;AAChC,WAAO;AAAA,EACX,WACS,OAAO,qBAAqB,UAAU;AAC3C,uBAAmB,EAAE,KAAK,iBAAiB;AAAA,EAC/C;AACA,QAAM,UAAU,iBAAiB,mBAAmB,cAC9C,iBAAiB,UACjB,IAAI,YAAY,iBAAiB,OAAO;AAC9C,QAAM,SAAS,iBAAiB,kBAAkB,aAC5C,iBAAiB,SACjB,IAAI,WAAW,EAAE,YAAY,iBAAiB,OAAO,CAAC;AAC5D,SAAO,IAAI,YAAY,iBAAiB,UAAU,OAAO,iBAAiB,KAAK,iBAAiB,QAAQ,MAAM;AAAA,IAC1G;AAAA,IACA;AAAA,IACA,gBAAgB,iBAAiB;AAAA,IACjC,iBAAiB,iBAAiB;AAAA,IAClC,WAAW,iBAAiB;AAAA,IAC5B,OAAO,iBAAiB;AAAA,IACxB,UAAU,iBAAiB;AAAA,IAC3B,MAAM,iBAAiB;AAAA,IACvB,UAAU,iBAAiB;AAAA,IAC3B;AAAA,IACA,SAAS,iBAAiB;AAAA,IAC1B,eAAe,iBAAiB;AAAA,IAChC,aAAa,iBAAiB;AAAA,IAC9B,SAAS,iBAAiB;AAAA,EAC9B,CAAC;AACL;AACA,IAAM,mBAAN,cAA+B,aAAc;AAAA,EACzC;AAAA,EACA,WAAW,aAAa;AAAA,IACpB,QAAQ,KAAK;AAAA,IACb,aAAa,MAAM;AAAA,EACvB,CAAC;AAAA,EACD,YAAY,aAAa;AAAA,IACrB,QAAQ,KAAK;AAAA,IACb,aAAa,MAAM;AAAA,EACvB,CAAC;AAAA,EACD,cAAc,aAAa;AAAA,IACvB,QAAQ,KAAK;AAAA,IACb,aAAa,MAAM;AAAA,EACvB,CAAC;AAAA,EACD,UAAU,SAAS,MAAM,KAAK,OAAO,MAAM,cAAc,KAAK,OAAO,MAAM,UAAU,KAAK,SAAS,IAAI,QAAW,GAAI,YAAY,CAAC,EAAE,WAAW,UAAU,CAAC,IAAI,CAAC,CAAE;AAAA,EAClK,WAAW,KAAK,UAAU,WAAW;AAAA,EACrC,aAAa,KAAK,YAAY,WAAW;AAAA,EACzC,YAAY,UAAU,SAAS,cAAc,OAAO,OAAO;AACvD,UAAM,SAAS,CAAC,EAAE,QAAQC,UAAS,YAAY,MAAM;AACjD,UAAI;AAGJ,YAAM,UAAU,MAAM,IAAI,YAAY;AACtC,kBAAY,iBAAiB,SAAS,OAAO;AAE7C,YAAM,SAAS,OAAO,EAAE,OAAO,OAAU,GAAG,GAAI,YAAY,CAAC,EAAE,WAAW,SAAS,CAAC,IAAI,CAAC,CAAE;AAC3F,UAAI;AACJ,YAAM,UAAU,IAAI,QAAQ,CAAC,MAAO,UAAU,CAAE;AAChD,YAAM,OAAO,CAAC,UAAU;AACpB,eAAO,IAAI,KAAK;AAChB,kBAAU,MAAM;AAChB,kBAAU;AAAA,MACd;AACA,YAAM,KAAK,OAAO,QAAQA,QAAO,EAAE,UAAU;AAAA,QACzC,MAAM,CAAC,UAAU;AACb,kBAAQ,MAAM,MAAM;AAAA,YAChB,KAAK,cAAc;AACf,mBAAK,SAAS,IAAI,MAAM,OAAO;AAC/B,mBAAK,YAAY,IAAI,MAAM,MAAM;AACjC,kBAAI;AACA,qBAAK,EAAE,OAAO,QAAQ,MAAM,MAAM,IAAI,IAAI,MAAM,KAAK,CAAC;AAAA,cAC1D,SACO,OAAO;AACV,qBAAK,EAAE,OAAO,yBAA0B,KAAK,EAAE,CAAC;AAAA,cACpD;AACA;AAAA,YACJ,KAAK,cAAc;AACf,mBAAK,UAAU,IAAI,KAAK;AACxB;AAAA,UACR;AAAA,QACJ;AAAA,QACA,OAAO,CAAC,UAAU;AACd,cAAI,iBAAiB,mBAAmB;AACpC,iBAAK,SAAS,IAAI,MAAM,OAAO;AAC/B,iBAAK,YAAY,IAAI,MAAM,MAAM;AAAA,UACrC;AACA,eAAK,EAAE,MAAM,CAAC;AACd,sBAAY,oBAAoB,SAAS,OAAO;AAAA,QACpD;AAAA,QACA,UAAU,MAAM;AACZ,cAAI,SAAS;AACT,iBAAK;AAAA,cACD,OAAO,IAAI,aAAc,KAAuE,aAAa,6CAA6C;AAAA,YAC9J,CAAC;AAAA,UACL;AACA,sBAAY,oBAAoB,SAAS,OAAO;AAAA,QACpD;AAAA,MACJ,CAAC;AACD,aAAO;AAAA,IACX,GAAG,cAAc,OAAO,QAAQ;AAChC,SAAK,SAAS,SAAS,IAAI,UAAU;AAAA,EACzC;AACJ;AA4BA,IAAM,iCAAiC,IAAI,eAAe,YAAY,mCAAmC,EAAE;AAI3G,IAAM,OAAO;AACb,IAAM,UAAU;AAChB,IAAM,SAAS;AACf,IAAM,cAAc;AACpB,IAAM,UAAU;AAChB,IAAM,gBAAgB;AACtB,IAAM,gBAAgB,IAAI,eAAe,YAAY,sCAAsC,EAAE;AAI7F,IAAM,kBAAkB,CAAC,OAAO,MAAM;AACtC,SAAS,2BAA2B,KAAK,MAAM;AAC3C,QAA4C,YAAO,aAAa,GAAxD,gBAzLZ,IAyLgD,IAAlB,0BAAkB,IAAlB,CAAlB;AACR,QAAM,EAAE,eAAe,gBAAgB,QAAQ,cAAc,IAAI;AAEjE,MAAI,CAAC,iBACD,mBAAmB;AAAA,EAElB,kBAAkB,UAAU,CAAC,cAAc,uBAAuB,CAAC,kBACnE,kBAAkB,UAAU,CAAC,gBAAgB,SAAS,aAAa;AAAA,EAEnE,CAAC,cAAc,kCAAkC,eAAe,GAAG,KACpE,cAAc,SAAS,GAAG,MAAM,OAAO;AACvC,WAAO,KAAK,GAAG;AAAA,EACnB;AACA,QAAM,gBAAgB,OAAO,aAAa;AAC1C,QAAM,YAAY,OAAO,gCAAgC;AAAA,IACrD,UAAU;AAAA,EACd,CAAC;AACD,MAA4D,WAAW;AACnE,UAAM,IAAI,aAAc,MAA4D,aAChF,2MAEqC;AAAA,EAC7C;AACA,QAAM,aAAoD,QACpD,oBAAoB,IAAI,KAAK,SAAS,IACtC,IAAI;AACV,QAAM,WAAW,aAAa,KAAK,UAAU;AAC7C,QAAM,WAAW,cAAc,IAAI,UAAU,IAAI;AACjD,MAAI,mBAAmB,cAAc;AACrC,MAAI,OAAO,mBAAmB,YAAY,eAAe,gBAAgB;AAErE,uBAAmB,eAAe;AAAA,EACtC;AACA,MAAI,UAAU;AACV,UAAM,EAAE,CAAC,IAAI,GAAG,eAAe,CAAC,aAAa,GAAG,cAAc,CAAC,OAAO,GAAG,aAAa,CAAC,MAAM,GAAG,QAAQ,CAAC,WAAW,GAAG,YAAY,CAAC,OAAO,GAAG,IAAK,IAAI;AAEvJ,QAAI,OAAO;AACX,YAAQ,cAAc;AAAA,MAClB,KAAK;AACD,eAAO,IAAI,YAAY,EAAE,OAAO,aAAa,EAAE;AAC/C;AAAA,MACJ,KAAK;AACD,eAAO,IAAI,KAAK,CAAC,aAAa,CAAC;AAC/B;AAAA,IACR;AAIA,QAAI,UAAU,IAAI,YAAY,WAAW;AACzC,QAAI,OAAO,cAAc,eAAe,WAAW;AAI/C,gBAAU,8BAA8B,IAAI,KAAK,SAAS,oBAAoB,CAAC,CAAC;AAAA,IACpF;AACA,WAAO,GAAG,IAAI,aAAa;AAAA,MACvB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ,CAAC,CAAC;AAAA,EACN;AACA,QAAM,SAAS,KAAK,GAAG;AACvB,MAA2C,OAAc;AAErD,WAAO,OAAO,KAAK,IAAI,CAAC,UAAU;AAE9B,UAAI,iBAAiB,cAAc;AAC/B,sBAAc,IAAI,UAAU;AAAA,UACxB,CAAC,IAAI,GAAG,MAAM;AAAA,UACd,CAAC,OAAO,GAAG,mBAAmB,MAAM,SAAS,gBAAgB;AAAA,UAC7D,CAAC,MAAM,GAAG,MAAM;AAAA,UAChB,CAAC,WAAW,GAAG,MAAM;AAAA,UACrB,CAAC,OAAO,GAAG;AAAA,UACX,CAAC,aAAa,GAAG,IAAI;AAAA,QACzB,CAAC;AAAA,MACL;AAAA,IACJ,CAAC,CAAC;AAAA,EACN;AACA,SAAO;AACX;AAEA,SAAS,eAAe,KAAK;AACzB,SAAO,IAAI,QAAQ,IAAI,eAAe,KAAK,IAAI,QAAQ,IAAI,qBAAqB;AACpF;AAcA,SAAS,oBAAoB,QAAQ;AACjC,SAAO,CAAC,GAAG,OAAO,KAAK,CAAC,EACnB,KAAK,EACL,IAAI,CAAC,MAAM,GAAG,CAAC,IAAI,OAAO,OAAO,CAAC,CAAC,EAAE,EACrC,KAAK,GAAG;AACjB;AACA,SAAS,aAAa,SAAS,kBAAkB;AAE7C,QAAM,EAAE,QAAQ,QAAQ,aAAa,IAAI;AACzC,QAAM,gBAAgB,oBAAoB,MAAM;AAChD,MAAI,iBAAiB,QAAQ,cAAc;AAC3C,MAAI,0BAA0B,iBAAiB;AAC3C,qBAAiB,oBAAoB,cAAc;AAAA,EACvD,WACS,OAAO,mBAAmB,UAAU;AACzC,qBAAiB;AAAA,EACrB;AACA,QAAM,MAAM,CAAC,QAAQ,cAAc,kBAAkB,gBAAgB,aAAa,EAAE,KAAK,GAAG;AAC5F,QAAM,OAAO,aAAa,GAAG;AAC7B,SAAO,aAAa,IAAI;AAC5B;AAOA,SAAS,aAAa,OAAO;AACzB,MAAI,OAAO;AACX,aAAW,QAAQ,OAAO;AACtB,WAAQ,KAAK,KAAK,IAAI,IAAI,IAAI,KAAK,WAAW,CAAC,KAAM;AAAA,EACzD;AAGA,UAAQ,aAAa;AACrB,SAAO,KAAK,SAAS;AACzB;AAYA,SAAS,sBAAsB,cAAc;AACzC,SAAO;AAAA,IACH;AAAA,MACI,SAAS;AAAA,MACT,YAAY,MAAM;AACd,+BAAwB,qBAAqB;AAC7C,eAAO,iBAAE,eAAe,QAAS;AAAA,MACrC;AAAA,IACJ;AAAA,IACA;AAAA,MACI,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,IACX;AAAA,IACA;AAAA,MACI,SAAS;AAAA,MACT,OAAO;AAAA,MACP,YAAY,MAAM;AACd,cAAM,SAAS,OAAO,cAAc;AACpC,cAAM,aAAa,OAAO,aAAa;AACvC,eAAO,MAAM;AACT,iBAAO,WAAW,EAAE,KAAK,MAAM;AAC3B,uBAAW,gBAAgB;AAAA,UAC/B,CAAC;AAAA,QACL;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AACJ;AAKA,SAAS,8BAA8B,KAAK,SAAS,kBAAkB;AACnE,QAAM,kBAAkB,oBAAI,IAAI;AAChC,SAAO,IAAI,MAAM,SAAS;AAAA,IACtB,IAAI,QAAQ,MAAM;AACd,YAAM,QAAQ,QAAQ,IAAI,QAAQ,IAAI;AACtC,YAAM,UAAU,oBAAI,IAAI,CAAC,OAAO,OAAO,QAAQ,CAAC;AAChD,UAAI,OAAO,UAAU,cAAc,CAAC,QAAQ,IAAI,IAAI,GAAG;AACnD,eAAO;AAAA,MACX;AACA,aAAO,CAAC,eAAe;AAEnB,cAAM,OAAO,OAAO,MAAM,YAAY,YAAY;AAClD,YAAI,CAAC,iBAAiB,SAAS,UAAU,KAAK,CAAC,gBAAgB,IAAI,GAAG,GAAG;AACrE,0BAAgB,IAAI,GAAG;AACvB,gBAAM,eAAe,eAAgB,GAAG;AAExC,kBAAQ,KAAK,mBAAoB,MAA+D,+BAA+B,UAAU,gKAEpG,UAAU,uBAAuB,YAAY,2RAIzC,CAAC;AAAA,QAC9C;AAEA,eAAO,MAAM,MAAM,QAAQ,CAAC,UAAU,CAAC;AAAA,MAC3C;AAAA,IACJ;AAAA,EACJ,CAAC;AACL;;;ACrWA,IAAM,OAAN,MAAM,MAAK;AAAA,EACT;AAAA,EACA;AAAA,EACA,YAAY,MAAM;AAChB,SAAK,OAAO;AACZ,SAAK,OAAO,OAAQ;AAAA,EACtB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,OAAO,KAAK,gBAAgB,OAAO;AACjC,QAAI,CAAC,IAAK,QAAO;AACjB,WAAO,KAAK,oBAAoB,KAAK,aAAa;AAAA,EACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,QAAQ,MAAM,gBAAgB,OAAO;AACnC,QAAI,CAAC,KAAM,QAAO,CAAC;AACnB,WAAO,KAAK,OAAO,CAAC,QAAQ,QAAQ;AAClC,UAAI,KAAK;AACP,eAAO,KAAK,KAAK,oBAAoB,KAAK,aAAa,CAAC;AAAA,MAC1D;AACA,aAAO;AAAA,IACT,GAAG,CAAC,CAAC;AAAA,EACP;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,OAAO,cAAc;AACnB,QAAI,CAAC,aAAc,QAAO;AAC1B,WAAO,KAAK,KAAK,cAAc,QAAQ,YAAY,GAAG,KAAK;AAAA,EAC7D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,QAAQ,cAAc;AACpB,QAAI,CAAC,aAAc,QAAO,CAAC;AAC3B,UAAM,OAAoB,KAAK,KAAK,iBAAiB,QAAQ,YAAY,GAAG;AAC5E,WAAO,OAAO,CAAC,EAAE,MAAM,KAAK,IAAI,IAAI,CAAC;AAAA,EACvC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,UAAU,KAAK,UAAU;AACvB,QAAI,CAAC,IAAK,QAAO;AACjB,eAAW,YAAY,KAAK,eAAe,GAAG;AAC9C,UAAM,OAAO,KAAK,OAAO,QAAQ;AACjC,QAAI,MAAM;AACR,aAAO,KAAK,0BAA0B,KAAK,IAAI;AAAA,IACjD;AACA,WAAO,KAAK,oBAAoB,KAAK,IAAI;AAAA,EAC3C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,UAAU,cAAc;AACtB,SAAK,iBAAiB,KAAK,OAAO,YAAY,CAAC;AAAA,EACjD;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,iBAAiB,MAAM;AACrB,QAAI,MAAM;AACR,WAAK,KAAK,OAAO,IAAI;AAAA,IACvB;AAAA,EACF;AAAA,EACA,oBAAoB,MAAM,gBAAgB,OAAO;AAC/C,QAAI,CAAC,eAAe;AAClB,YAAM,WAAW,KAAK,eAAe,IAAI;AAIzC,YAAM,OAAO,KAAK,QAAQ,QAAQ,EAAE,OAAO,CAAAC,UAAQ,KAAK,oBAAoB,MAAMA,KAAI,CAAC,EAAE,CAAC;AAC1F,UAAI,SAAS,OAAW,QAAO;AAAA,IACjC;AACA,UAAM,UAAU,KAAK,KAAK,cAAc,MAAM;AAC9C,SAAK,0BAA0B,MAAM,OAAO;AAC5C,UAAM,OAAO,KAAK,KAAK,qBAAqB,MAAM,EAAE,CAAC;AACrD,SAAK,YAAY,OAAO;AACxB,WAAO;AAAA,EACT;AAAA,EACA,0BAA0B,KAAK,IAAI;AACjC,WAAO,KAAK,GAAG,EAAE,QAAQ,UAAQ,GAAG,aAAa,KAAK,eAAe,IAAI,GAAG,IAAI,IAAI,CAAC,CAAC;AACtF,WAAO;AAAA,EACT;AAAA,EACA,eAAe,KAAK;AAClB,UAAM,OAAO,IAAI,OAAO,SAAS;AACjC,WAAO,GAAG,IAAI,KAAK,IAAI,IAAI,CAAC;AAAA,EAC9B;AAAA,EACA,oBAAoB,KAAK,MAAM;AAC7B,WAAO,OAAO,KAAK,GAAG,EAAE,MAAM,SAAO,KAAK,aAAa,KAAK,eAAe,GAAG,CAAC,MAAM,IAAI,GAAG,CAAC;AAAA,EAC/F;AAAA,EACA,eAAe,MAAM;AACnB,WAAO,cAAc,IAAI,KAAK;AAAA,EAChC;AAAA,EACA,OAAO,OAAO,SAAS,aAAa,mBAAmB;AACrD,WAAO,KAAK,qBAAqB,OAAS,SAAS,QAAQ,CAAC;AAAA,EAC9D;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,MAAK;AAAA,IACd,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,MAAM,CAAC;AAAA,IAC7E,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,QAAQ;AAAA,IACjB,CAAC;AAAA,EACH,CAAC,GAAG,IAAI;AACV,GAAG;AAIH,IAAM,gBAAgB;AAAA,EACpB,WAAW;AACb;AAYA,IAAM,QAAN,MAAM,OAAM;AAAA,EACV;AAAA,EACA,YAAY,MAAM;AAChB,SAAK,OAAO;AAAA,EACd;AAAA;AAAA;AAAA;AAAA,EAIA,WAAW;AACT,WAAO,KAAK,KAAK;AAAA,EACnB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,SAAS,UAAU;AACjB,SAAK,KAAK,QAAQ,YAAY;AAAA,EAChC;AAAA,EACA,OAAO,OAAO,SAAS,cAAc,mBAAmB;AACtD,WAAO,KAAK,qBAAqB,QAAU,SAAS,QAAQ,CAAC;AAAA,EAC/D;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,OAAM;AAAA,IACf,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,OAAO,CAAC;AAAA,IAC9E,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,QAAQ;AAAA,IACjB,CAAC;AAAA,EACH,CAAC,GAAG,IAAI;AACV,GAAG;AAUH,SAAS,YAAY,MAAM,OAAO;AAChC,MAAI,OAAO,aAAa,eAAe,CAAC,UAAU;AAKhD,UAAM,KAAK,QAAQ,IAAI,IAAI,QAAQ,IAAI,KAAK,CAAC;AAC7C,OAAG,IAAI,IAAI;AAAA,EACb;AACF;AACA,IAAM,4BAAN,MAAgC;AAAA,EAC9B;AAAA,EACA;AAAA,EACA,YAAY,WAAW,UAAU;AAC/B,SAAK,YAAY;AACjB,SAAK,WAAW;AAAA,EAClB;AACF;AAKA,IAAM,kBAAN,MAAsB;AAAA,EACpB;AAAA,EACA,YAAY,KAAK;AACf,SAAK,SAAS,IAAI,SAAS,IAAI,cAAc;AAAA,EAC/C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAkBA,oBAAoB,QAAQ;AAC1B,UAAM,SAAS,UAAU,OAAO,QAAQ;AACxC,UAAM,cAAc;AAEpB,QAAI,UAAU,aAAa,WAAW,OAAO,QAAQ,YAAY,YAAY;AAC3E,cAAQ,QAAQ,WAAW;AAAA,IAC7B;AACA,UAAM,QAAQ,YAAY,IAAI;AAC9B,QAAI,WAAW;AACf,WAAO,WAAW,KAAK,YAAY,IAAI,IAAI,QAAQ,KAAK;AACtD,WAAK,OAAO,KAAK;AACjB;AAAA,IACF;AACA,UAAM,MAAM,YAAY,IAAI;AAC5B,QAAI,UAAU,gBAAgB,WAAW,OAAO,QAAQ,eAAe,YAAY;AACjF,cAAQ,WAAW,WAAW;AAAA,IAChC;AACA,UAAM,aAAa,MAAM,SAAS;AAClC,YAAQ,IAAI,OAAO,QAAQ,0BAA0B;AACrD,YAAQ,IAAI,GAAG,UAAU,QAAQ,CAAC,CAAC,eAAe;AAClD,WAAO,IAAI,0BAA0B,WAAW,QAAQ;AAAA,EAC1D;AACF;AACA,IAAM,uBAAuB;AAc7B,SAAS,iBAAiB,KAAK;AAC7B,cAAY,sBAAsB,IAAI,gBAAgB,GAAG,CAAC;AAC1D,SAAO;AACT;AAMA,SAAS,oBAAoB;AAC3B,cAAY,sBAAsB,IAAI;AACxC;AAOA,IAAM,KAAN,MAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASP,OAAO,MAAM;AACX,WAAO,MAAM;AAAA,EACf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,OAAO,IAAI,UAAU;AACnB,WAAO,kBAAgB;AACrB,aAAO,aAAa,iBAAiB,OAAO,eAAe,aAAa,eAAe,QAAQ,IAAI;AAAA,IACrG;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,OAAO,UAAU,MAAM;AACrB,WAAO,eAAa,UAAU,eAAe,QAAQ,IAAI,MAAM;AAAA,EACjE;AACF;AACA,SAAS,eAAe,GAAG,UAAU;AACnC,MAAI,OAAQ,EAAE,cAAc,CAAC,GAAG;AAC9B,WAAO,EAAE,WAAW,EAAE,QAAQ,QAAQ,KAAK,EAAE,qBAAqB,EAAE,kBAAkB,QAAQ,KAAK,EAAE,yBAAyB,EAAE,sBAAsB,QAAQ;AAAA,EAChK;AACA,SAAO;AACT;AAMA,IAAM,cAAc;AAAA;AAAA,EAElB,OAAO;AAAA,EACP,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,UAAU;AAAA,EACV,aAAa;AAAA,EACb,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,SAAS;AAAA,EACT,WAAW;AAAA;AAAA,EAEX,SAAS;AAAA,EACT,cAAc;AAAA,EACd,aAAa;AAAA,EACb,YAAY;AAAA,EACZ,eAAe;AAAA,EACf,WAAW;AAAA,EACX,YAAY;AAAA;AAAA,EAEZ,SAAS;AAAA,EACT,WAAW;AAAA;AAAA,EAEX,UAAU;AAAA,EACV,eAAe;AAAA,EACf,cAAc;AAAA,EACd,aAAa;AAAA,EACb,gBAAgB;AAAA;AAAA,EAEhB,SAAS;AAAA,EACT,aAAa;AAAA,EACb,cAAc;AAAA,EACd,WAAW;AAAA,EACX,aAAa;AAAA;AAAA,EAEb,OAAO;AAAA,EACP,aAAa;AACf;AAUA,IAAM,wBAAwB,IAAI,eAAe,OAAO,cAAc,eAAe,YAAY,wBAAwB,EAAE;AAU3H,IAAM,gBAAgB,IAAI,eAAe,OAAO,cAAc,eAAe,YAAY,iBAAiB,EAAE;AAQ5G,IAAM,sBAAN,MAAM,qBAAoB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMxB,SAAS,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAiBV,YAAY,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQb;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,YAAY,SAAS;AACnB,UAAM,KAAK,IAAI,OAAO,SAAS,KAAK,OAAO;AAC3C,OAAG,IAAI,OAAO,EAAE,IAAI;AAAA,MAClB,QAAQ;AAAA,IACV,CAAC;AACD,OAAG,IAAI,QAAQ,EAAE,IAAI;AAAA,MACnB,QAAQ;AAAA,IACV,CAAC;AACD,eAAW,aAAa,KAAK,WAAW;AACtC,SAAG,IAAI,SAAS,EAAE,IAAI,KAAK,UAAU,SAAS,CAAC;AAAA,IACjD;AACA,WAAO;AAAA,EACT;AAAA,EACA,OAAO,OAAO,SAAS,4BAA4B,mBAAmB;AACpE,WAAO,KAAK,qBAAqB,sBAAqB;AAAA,EACxD;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,qBAAoB;AAAA,EAC/B,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,qBAAqB,CAAC;AAAA,IAC5F,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAMH,IAAM,uBAAN,MAAM,8BAA6B,mBAAmB;AAAA,EACpD;AAAA,EACA;AAAA,EACA;AAAA,EACA,iBAAiB;AAAA,EACjB,YAAY,KAAK,SAAS,WAAW,QAAQ;AAC3C,UAAM,GAAG;AACT,SAAK,UAAU;AACf,SAAK,YAAY;AACjB,SAAK,SAAS;AAAA,EAChB;AAAA,EACA,SAAS,WAAW;AAClB,QAAI,CAAC,YAAY,eAAe,UAAU,YAAY,CAAC,KAAK,CAAC,KAAK,cAAc,SAAS,GAAG;AAC1F,aAAO;AAAA,IACT;AACA,QAAI,CAAC,OAAO,UAAU,CAAC,KAAK,QAAQ;AAClC,UAAI,OAAO,cAAc,eAAe,WAAW;AAGjD,cAAM,WAAW,KAAK,UAAU,IAAI,OAAQ;AAC5C,iBAAS,KAAK,QAAQ,SAAS,kGAAuG;AAAA,MACxI;AACA,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAAA,EACA,iBAAiB,SAAS,WAAW,SAAS;AAC5C,UAAM,OAAO,KAAK,QAAQ,QAAQ;AAClC,gBAAY,UAAU,YAAY;AAGlC,QAAI,CAAC,OAAO,UAAU,KAAK,QAAQ;AACjC,WAAK,iBAAiB,KAAK,kBAAkB,KAAK,kBAAkB,MAAM,KAAK,OAAO,CAAC;AAIvF,UAAI,qBAAqB;AACzB,UAAI,aAAa,MAAM;AACrB,6BAAqB;AAAA,MACvB;AACA,WAAK,kBAAkB,MAAM,KAAK,eAAe,KAAK,MAAM;AAE1D,YAAI,CAAC,OAAO,QAAQ;AAClB,cAAI,OAAO,cAAc,eAAe,WAAW;AACjD,kBAAM,WAAW,KAAK,UAAU,IAAI,OAAQ;AAC5C,qBAAS,KAAK,mEAAmE;AAAA,UACnF;AACA,uBAAa,MAAM;AAAA,UAAC;AACpB;AAAA,QACF;AACA,YAAI,CAAC,oBAAoB;AAIvB,uBAAa,KAAK,iBAAiB,SAAS,WAAW,OAAO;AAAA,QAChE;AAAA,MACF,CAAC,EAAE,MAAM,MAAM;AACb,YAAI,OAAO,cAAc,eAAe,WAAW;AACjD,gBAAM,WAAW,KAAK,UAAU,IAAI,OAAQ;AAC5C,mBAAS,KAAK,QAAQ,SAAS,qEAA0E;AAAA,QAC3G;AACA,qBAAa,MAAM;AAAA,QAAC;AAAA,MACtB,CAAC,CAAC;AAIF,aAAO,MAAM;AACX,mBAAW;AAAA,MACb;AAAA,IACF;AACA,WAAO,KAAK,kBAAkB,MAAM;AAElC,YAAM,KAAK,KAAK,QAAQ,YAAY,OAAO;AAC3C,YAAM,WAAW,SAAU,UAAU;AACnC,aAAK,WAAW,WAAY;AAC1B,kBAAQ,QAAQ;AAAA,QAClB,CAAC;AAAA,MACH;AACA,SAAG,GAAG,WAAW,QAAQ;AACzB,aAAO,MAAM;AACX,WAAG,IAAI,WAAW,QAAQ;AAE1B,YAAI,OAAO,GAAG,YAAY,YAAY;AACpC,aAAG,QAAQ;AAAA,QACb;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,cAAc,WAAW;AACvB,WAAO,KAAK,QAAQ,OAAO,QAAQ,SAAS,IAAI;AAAA,EAClD;AAAA,EACA,OAAO,OAAO,SAAS,6BAA6B,mBAAmB;AACrE,WAAO,KAAK,qBAAqB,uBAAyB,SAAS,QAAQ,GAAM,SAAS,qBAAqB,GAAM,SAAY,QAAQ,GAAM,SAAS,eAAe,CAAC,CAAC;AAAA,EAC3K;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,sBAAqB;AAAA,EAChC,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,sBAAsB,CAAC;AAAA,IAC7F,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,QAAQ;AAAA,IACjB,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,qBAAqB;AAAA,IAC9B,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM,CAAC,aAAa;AAAA,IACtB,CAAC;AAAA,EACH,CAAC,GAAG,IAAI;AACV,GAAG;AAcH,IAAM,eAAN,MAAM,cAAa;AAAA,EACjB,OAAO,OAAO,SAAS,qBAAqB,mBAAmB;AAC7D,WAAO,KAAK,qBAAqB,eAAc;AAAA,EACjD;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,EACR,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,WAAW,CAAC;AAAA,MACV,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,MACP,MAAM,CAAC,UAAU,uBAAuB,UAAU,CAAC,IAAI,SAAS,GAAG,aAAa,CAAC;AAAA,IACnF,GAAG;AAAA,MACD,SAAS;AAAA,MACT,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,cAAc,CAAC;AAAA,IACrF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,UAAU;AAAA,QACV,OAAO;AAAA,QACP,MAAM,CAAC,UAAU,uBAAuB,UAAU,CAAC,IAAI,SAAS,GAAG,aAAa,CAAC;AAAA,MACnF,GAAG;AAAA,QACD,SAAS;AAAA,QACT,UAAU;AAAA,MACZ,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAiCH,IAAM,eAAN,MAAM,cAAa;AAAA,EACjB,OAAO,OAAO,SAAS,qBAAqB,mBAAmB;AAC7D,WAAO,KAAK,qBAAqB,eAAc;AAAA,EACjD;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,SAAS,qBAAqB,mBAAmB;AACxD,UAAI,2BAA2B;AAC/B,UAAI,mBAAmB;AACrB,mCAA2B,KAAK,qBAAqB,eAAc;AAAA,MACrE,OAAO;AACL,mCAA8B,SAAS,gBAAgB;AAAA,MACzD;AACA,aAAO;AAAA,IACT;AAAA,IACA,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,cAAc,CAAC;AAAA,IACrF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,MACZ,aAAa,WAAW,MAAM,gBAAgB;AAAA,IAChD,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,mBAAN,MAAM,0BAAyB,aAAa;AAAA,EAC1C;AAAA,EACA,YAAY,MAAM;AAChB,UAAM;AACN,SAAK,OAAO;AAAA,EACd;AAAA,EACA,SAAS,KAAK,OAAO;AACnB,QAAI,SAAS,KAAM,QAAO;AAC1B,YAAQ,KAAK;AAAA,MACX,KAAK,gBAAgB;AACnB,eAAO;AAAA,MACT,KAAK,gBAAgB;AACnB,YAAI;AAAA,UAAiC;AAAA,UAAO;AAAA;AAAA,QAA4B,GAAG;AACzE,iBAAO,gBAAiB,KAAK;AAAA,QAC/B;AACA,eAAO,cAAe,KAAK,MAAM,OAAO,KAAK,CAAC,EAAE,SAAS;AAAA,MAC3D,KAAK,gBAAgB;AACnB,YAAI;AAAA,UAAiC;AAAA,UAAO;AAAA;AAAA,QAA8B,GAAG;AAC3E,iBAAO,gBAAiB,KAAK;AAAA,QAC/B;AACA,eAAO;AAAA,MACT,KAAK,gBAAgB;AACnB,YAAI;AAAA,UAAiC;AAAA,UAAO;AAAA;AAAA,QAAgC,GAAG;AAC7E,iBAAO,gBAAiB,KAAK;AAAA,QAC/B;AACA,cAAM,IAAI,aAAc,OAAyD,OAAO,cAAc,eAAe,cAAc,uCAAuC;AAAA,MAC5K,KAAK,gBAAgB;AACnB,YAAI;AAAA,UAAiC;AAAA,UAAO;AAAA;AAAA,QAA0B,GAAG;AACvE,iBAAO,gBAAiB,KAAK;AAAA,QAC/B;AACA,eAAO,aAAc,OAAO,KAAK,CAAC;AAAA,MACpC,KAAK,gBAAgB;AACnB,YAAI;AAAA,UAAiC;AAAA,UAAO;AAAA;AAAA,QAA0C,GAAG;AACvF,iBAAO,gBAAiB,KAAK;AAAA,QAC/B;AACA,cAAM,IAAI,aAAc,OAA+D,OAAO,cAAc,eAAe,cAAc,oDAAoD,gBAAiB,GAAG;AAAA,MACnN;AACE,cAAM,IAAI,aAAc,OAA0D,OAAO,cAAc,eAAe,cAAc,8BAA8B,GAAG,SAAS,gBAAiB,GAAG;AAAA,IACtM;AAAA,EACF;AAAA,EACA,wBAAwB,OAAO;AAC7B,WAAO,4BAA6B,KAAK;AAAA,EAC3C;AAAA,EACA,yBAAyB,OAAO;AAC9B,WAAO,6BAA8B,KAAK;AAAA,EAC5C;AAAA,EACA,0BAA0B,OAAO;AAC/B,WAAO,8BAA+B,KAAK;AAAA,EAC7C;AAAA,EACA,uBAAuB,OAAO;AAC5B,WAAO,2BAA4B,KAAK;AAAA,EAC1C;AAAA,EACA,+BAA+B,OAAO;AACpC,WAAO,mCAAoC,KAAK;AAAA,EAClD;AAAA,EACA,OAAO,OAAO,SAAS,yBAAyB,mBAAmB;AACjE,WAAO,KAAK,qBAAqB,mBAAqB,SAAS,QAAQ,CAAC;AAAA,EAC1E;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,kBAAiB;AAAA,IAC1B,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,QAAQ;AAAA,IACjB,CAAC;AAAA,EACH,CAAC,GAAG,IAAI;AACV,GAAG;AAQH,IAAI;AAAA,CACH,SAAUC,uBAAsB;AAC/B,EAAAA,sBAAqBA,sBAAqB,qBAAqB,IAAI,CAAC,IAAI;AACxE,EAAAA,sBAAqBA,sBAAqB,0BAA0B,IAAI,CAAC,IAAI;AAC7E,EAAAA,sBAAqBA,sBAAqB,aAAa,IAAI,CAAC,IAAI;AAChE,EAAAA,sBAAqBA,sBAAqB,aAAa,IAAI,CAAC,IAAI;AAChE,EAAAA,sBAAqBA,sBAAqB,sBAAsB,IAAI,CAAC,IAAI;AAC3E,GAAG,yBAAyB,uBAAuB,CAAC,EAAE;AAItD,SAAS,iBAAiB,OAAO,aAAa,CAAC,GAAG,WAAW,CAAC,GAAG;AAC/D,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AAOA,SAAS,0BAA0B;AAGjC,SAAO,iBAAiB,qBAAqB,mBAAmB;AAClE;AASA,SAAS,6BAA6B,SAAS;AAE7C,SAAO,iBAAiB,qBAAqB,0BAA0B,sBAAuB,OAAO,CAAC;AACxG;AAMA,SAASC,mBAAkB;AACzB,SAAO,iBAAiB,qBAAqB,aAAa,gBAAiB,CAAC;AAC9E;AAkBA,SAASC,mBAAkB;AACzB,SAAO,iBAAiB,qBAAqB,aAAa,gBAAiB,CAAC;AAC9E;AAgBA,SAASC,4BAA2B;AAClC,SAAO,iBAAiB,qBAAqB,sBAAsB,yBAA0B,CAAC;AAChG;AAMA,SAAS,qCAAqC;AAC5C,SAAO,CAAC;AAAA,IACN,SAAS;AAAA,IACT,UAAU,MAAM;AACd,YAAM,SAAS,OAAO,MAAM;AAC5B,YAAM,aAAa,OAAO,gBAAiB;AAG3C,UAAI,CAAC,cAAc,OAAO,gBAAgB,QAAQ;AAChD,cAAMC,WAAU,OAAO,OAAQ;AAC/B,cAAM,UAAU,mBAAoB,MAA0D,sKAAgL;AAC9Q,QAAAA,SAAQ,KAAK,OAAO;AAAA,MACtB;AAAA,IACF;AAAA,IACA,OAAO;AAAA,EACT,CAAC;AACH;AAkDA,SAAS,0BAA0B,UAAU;AAC3C,QAAM,YAAY,CAAC;AACnB,QAAM,eAAe,oBAAI,IAAI;AAC7B,aAAW;AAAA,IACT;AAAA,IACA;AAAA,EACF,KAAK,UAAU;AACb,iBAAa,IAAI,KAAK;AACtB,QAAI,WAAW,QAAQ;AACrB,gBAAU,KAAK,UAAU;AAAA,IAC3B;AAAA,EACF;AACA,QAAM,8BAA8B,aAAa,IAAI,qBAAqB,wBAAwB;AAClG,MAAI,OAAO,cAAc,eAAe,aAAa,aAAa,IAAI,qBAAqB,mBAAmB,KAAK,6BAA6B;AAC9I,UAAM,IAAI,aAAc,MAA4D,sKAAsK;AAAA,EAC5P;AACA,SAAO,yBAAyB,CAAC,OAAO,cAAc,eAAe,YAAY,mCAAmC,IAAI,CAAC,GAAG,iBAAkB,GAAG,aAAa,IAAI,qBAAqB,mBAAmB,KAAK,8BAA8B,CAAC,IAAI,sBAAuB,CAAC,CAAC,GAAG,SAAS,CAAC;AAC1R;AAUA,IAAM,UAAU,IAAI,QAAQ,QAAQ;", "names": ["plugin", "value", "map", "base", "headers", "params", "HttpEventType", "HttpStatusCode", "req", "signal", "document", "HttpFeatureKind", "httpResource", "request", "elem", "HydrationFeatureKind", "withI18nSupport", "withEventReplay", "withIncrementalHydration", "console"]}